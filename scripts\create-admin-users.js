import dotenv from 'dotenv';
import { neon } from '@neondatabase/serverless';
import bcrypt from 'bcryptjs';

dotenv.config();
const sql = neon(process.env.VITE_DATABASE_URL);

async function createAdminUsers() {
  try {
    console.log('🔐 Creating admin users...\n');

    // Hash passwords
    const adminPassword = await bcrypt.hash('admin123', 12);
    const subAdminPassword = await bcrypt.hash('subadmin123', 12);

    // Create main admin user
    console.log('👤 Creating main admin user...');
    const adminResult = await sql`
      INSERT INTO users (
        id,
        email,
        password,
        name,
        role,
        "isActive",
        "createdAt",
        "updatedAt"
      ) VALUES (
        'admin-main-001',
        '<EMAIL>',
        ${adminPassword},
        'Main Administrator',
        'ADMIN',
        true,
        NOW(),
        NOW()
      )
      ON CONFLICT (email) DO UPDATE SET
        password = ${adminPassword},
        "updatedAt" = NOW()
      RETURNING id, email, name, role
    `;
    console.log('✅ Main admin created:', adminResult[0]);

    // Create demo sub-admin user
    console.log('👤 Creating demo sub-admin user...');
    const subAdminResult = await sql`
      INSERT INTO users (
        id,
        email,
        password,
        name,
        role,
        "isActive",
        "createdById",
        address,
        city,
        state,
        country,
        "panNumber",
        "subAdminOnboardingDate",
        "createdAt",
        "updatedAt"
      ) VALUES (
        'subadmin-demo-001',
        '<EMAIL>',
        ${subAdminPassword},
        'Demo Sub Administrator',
        'SUB_ADMIN',
        true,
        'admin-main-001',
        '123 Demo Street',
        'Mumbai',
        'Maharashtra',
        'India',
        '**********',
        NOW(),
        NOW(),
        NOW()
      )
      ON CONFLICT (email) DO UPDATE SET
        password = ${subAdminPassword},
        "updatedAt" = NOW()
      RETURNING id, email, name, role
    `;
    console.log('✅ Demo sub-admin created:', subAdminResult[0]);

    // Create additional admin user
    console.log('👤 Creating secondary admin user...');
    const admin2Result = await sql`
      INSERT INTO users (
        id,
        email,
        password,
        name,
        role,
        "isActive",
        "createdAt",
        "updatedAt"
      ) VALUES (
        'admin-secondary-001',
        '<EMAIL>',
        ${adminPassword},
        'Secondary Administrator',
        'ADMIN',
        true,
        NOW(),
        NOW()
      )
      ON CONFLICT (email) DO UPDATE SET
        password = ${adminPassword},
        "updatedAt" = NOW()
      RETURNING id, email, name, role
    `;
    console.log('✅ Secondary admin created:', admin2Result[0]);

    // Verify users were created
    console.log('\n🔍 Verifying created users...');
    const allUsers = await sql`
      SELECT id, email, name, role, "isActive", "createdAt"
      FROM users
      ORDER BY "createdAt" DESC
    `;

    console.log('\n📋 All users in database:');
    allUsers.forEach(user => {
      console.log(`  - ${user.email} (${user.role}) - ${user.isActive ? 'Active' : 'Inactive'}`);
    });

    console.log('\n✅ Admin users setup completed!');
    console.log('\n🔑 Login credentials:');
    console.log('  Main Admin: <EMAIL> / admin123');
    console.log('  Sub-Admin: <EMAIL> / subadmin123');
    console.log('  Secondary Admin: <EMAIL> / admin123');

  } catch (error) {
    console.error('❌ Error creating admin users:', error);
    console.error('Details:', error.message);
  }
}

// Run the script
createAdminUsers();
