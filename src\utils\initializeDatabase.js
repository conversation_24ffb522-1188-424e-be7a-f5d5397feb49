/**
 * Database Initialization Script
 * This will create all missing tables and fix NeonDB issues
 */

import { sql } from '../config/database.js';

/**
 * Create all required database tables
 */
export const createAllTables = async () => {
  try {
    console.log('🚀 Creating all database tables...');

    // Create area_of_expertise table
    await sql`
      CREATE TABLE IF NOT EXISTS area_of_expertise (
        id SERIAL PRIMARY KEY,
        name VARCHAR(255) NOT NULL UNIQUE,
        description TEXT,
        is_active BOOLEAN DEFAULT true,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `;
    console.log('✅ area_of_expertise table created');

    // Create service_categories table
    await sql`
      CREATE TABLE IF NOT EXISTS service_categories (
        id SERIAL PRIMARY KEY,
        name VARCHAR(255) NOT NULL UNIQUE,
        description TEXT,
        is_active BOOLEAN DEFAULT true,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `;
    console.log('✅ service_categories table created');

    // Create services table
    await sql`
      CREATE TABLE IF NOT EXISTS services (
        id SERIAL PRIMARY KEY,
        name VARCHAR(255) NOT NULL UNIQUE,
        description TEXT,
        category_id INTEGER REFERENCES service_categories(id),
        is_active BOOLEAN DEFAULT true,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `;
    console.log('✅ services table created');

    // Create vendors table
    await sql`
      CREATE TABLE IF NOT EXISTS vendors (
        id SERIAL PRIMARY KEY,
        company_name VARCHAR(255) NOT NULL,
        company_type VARCHAR(100),
        onboarding_date DATE,
        emails JSONB DEFAULT '[]',
        phones JSONB DEFAULT '[]',
        address TEXT,
        country VARCHAR(100),
        state VARCHAR(100),
        city VARCHAR(100),
        username VARCHAR(255),
        gst_number VARCHAR(50),
        description TEXT,
        services JSONB DEFAULT '[]',
        website VARCHAR(255),
        type_of_work VARCHAR(255),
        status VARCHAR(50) DEFAULT 'Pending',
        files JSONB DEFAULT '{}',
        point_of_contact JSONB DEFAULT '[]',
        startup_benefits VARCHAR(10) DEFAULT 'No',
        rating DECIMAL(3,2) DEFAULT 0.00,
        total_orders INTEGER DEFAULT 0,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `;
    console.log('✅ vendors table created');

    // Create clients table
    await sql`
      CREATE TABLE IF NOT EXISTS clients (
        id SERIAL PRIMARY KEY,
        company_name VARCHAR(255) NOT NULL,
        company_type VARCHAR(100),
        onboarding_date DATE,
        emails JSONB DEFAULT '[]',
        phones JSONB DEFAULT '[]',
        address TEXT,
        country VARCHAR(100),
        state VARCHAR(100),
        city VARCHAR(100),
        username VARCHAR(255),
        gst_number VARCHAR(50),
        description TEXT,
        website VARCHAR(255),
        status VARCHAR(50) DEFAULT 'Pending',
        files JSONB DEFAULT '{}',
        point_of_contact JSONB DEFAULT '[]',
        rating DECIMAL(3,2) DEFAULT 0.00,
        total_orders INTEGER DEFAULT 0,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `;
    console.log('✅ clients table created');

    // Create orders table
    await sql`
      CREATE TABLE IF NOT EXISTS orders (
        id SERIAL PRIMARY KEY,
        order_reference_number VARCHAR(255) UNIQUE,
        onboarding_date DATE,
        customer_id INTEGER REFERENCES clients(id),
        vendor_id INTEGER REFERENCES vendors(id),
        type_of_work VARCHAR(255),
        date_of_work_completion_expected DATE,
        date_of_payment_expected DATE,
        total_invoice_value DECIMAL(12,2),
        total_gst_govt_fees DECIMAL(12,2),
        date_of_completion DATE,
        country_to_implement VARCHAR(100),
        application_diary_number VARCHAR(255),
        date_of_filing_at_po DATE,
        lawyer_reference_number VARCHAR(255),
        status VARCHAR(50) DEFAULT 'Pending',
        files JSONB DEFAULT '{}',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `;
    console.log('✅ orders table created');

    console.log('🎉 All tables created successfully!');
    return true;

  } catch (error) {
    console.error('❌ Error creating tables:', error);
    throw error;
  }
};

/**
 * Insert sample data into tables
 */
export const insertSampleData = async () => {
  try {
    console.log('📊 Inserting sample data...');

    // Insert sample service categories
    await sql`
      INSERT INTO service_categories (name, description) VALUES
      ('Legal Services', 'Legal and compliance related services'),
      ('Technology', 'IT and software development services'),
      ('Consulting', 'Business and management consulting'),
      ('Marketing', 'Marketing and advertising services')
      ON CONFLICT (name) DO NOTHING
    `;
    console.log('✅ Sample service categories inserted');

    // Insert sample services
    await sql`
      INSERT INTO services (name, description, category_id) VALUES
      ('Patent Filing', 'Patent application and filing services', 1),
      ('Trademark Registration', 'Trademark registration and protection', 1),
      ('Web Development', 'Website and web application development', 2),
      ('Mobile App Development', 'iOS and Android app development', 2),
      ('Business Strategy', 'Strategic planning and consulting', 3),
      ('Digital Marketing', 'Online marketing and SEO services', 4)
      ON CONFLICT (name) DO NOTHING
    `;
    console.log('✅ Sample services inserted');

    // Insert sample area of expertise
    await sql`
      INSERT INTO area_of_expertise (name, description) VALUES
      ('Intellectual Property', 'Patents, trademarks, and IP protection'),
      ('Software Development', 'Custom software and application development'),
      ('Business Consulting', 'Strategic business advice and planning'),
      ('Digital Marketing', 'Online marketing and brand promotion'),
      ('Legal Compliance', 'Regulatory compliance and legal advice')
      ON CONFLICT (name) DO NOTHING
    `;
    console.log('✅ Sample area of expertise inserted');

    console.log('🎉 Sample data inserted successfully!');
    return true;

  } catch (error) {
    console.error('❌ Error inserting sample data:', error);
    throw error;
  }
};

/**
 * Complete database initialization
 */
export const initializeCompleteDatabase = async () => {
  try {
    console.log('🚀 Starting complete database initialization...');

    // Test connection first
    console.log('1️⃣ Testing database connection...');
    const connectionTest = await sql`SELECT NOW() as current_time`;
    console.log('✅ Database connected:', connectionTest[0].current_time);

    // Create all tables
    console.log('2️⃣ Creating database tables...');
    await createAllTables();

    // Insert sample data
    console.log('3️⃣ Inserting sample data...');
    await insertSampleData();

    // Verify tables exist
    console.log('4️⃣ Verifying tables...');
    const tables = await sql`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public'
      ORDER BY table_name
    `;
    console.log('📋 Created tables:', tables.map(t => t.table_name));

    console.log('🎉 Database initialization completed successfully!');
    console.log('🔄 Please refresh the application to see changes.');

    return {
      success: true,
      tables: tables.map(t => t.table_name),
      message: 'Database initialized successfully'
    };

  } catch (error) {
    console.error('💥 Database initialization failed:', error);
    return {
      success: false,
      error: error.message,
      message: 'Database initialization failed'
    };
  }
};

/**
 * Quick fix for missing tables
 */
export const quickFixDatabase = async () => {
  try {
    console.log('🔧 Quick fix: Creating missing tables...');

    // Check which tables are missing
    const existingTables = await sql`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public'
    `;
    
    const tableNames = existingTables.map(t => t.table_name);
    console.log('📋 Existing tables:', tableNames);

    const requiredTables = ['services', 'service_categories', 'area_of_expertise', 'vendors', 'clients', 'orders'];
    const missingTables = requiredTables.filter(table => !tableNames.includes(table));
    
    if (missingTables.length > 0) {
      console.log('❌ Missing tables:', missingTables);
      await createAllTables();
      await insertSampleData();
    } else {
      console.log('✅ All required tables exist');
    }

    return { success: true, missingTables };

  } catch (error) {
    console.error('❌ Quick fix failed:', error);
    return { success: false, error: error.message };
  }
};

// Make functions available globally
if (typeof window !== 'undefined') {
  window.initializeCompleteDatabase = initializeCompleteDatabase;
  window.quickFixDatabase = quickFixDatabase;
  window.createAllTables = createAllTables;
  window.insertSampleData = insertSampleData;
  
  console.log('🔧 Database initialization functions available:');
  console.log('- window.initializeCompleteDatabase() - Complete setup');
  console.log('- window.quickFixDatabase() - Quick fix missing tables');
  console.log('- window.createAllTables() - Create all tables');
  console.log('- window.insertSampleData() - Insert sample data');
}
