<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document Viewer Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .test-url {
            background: #f5f5f5;
            padding: 10px;
            border-radius: 4px;
            word-break: break-all;
            margin: 10px 0;
        }
        iframe {
            width: 100%;
            height: 400px;
            border: 1px solid #ccc;
            border-radius: 4px;
        }
        img {
            max-width: 100%;
            height: auto;
            border: 1px solid #ccc;
            border-radius: 4px;
        }
        .error {
            color: red;
            font-weight: bold;
        }
        .success {
            color: green;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <h1>Document Viewer Test</h1>
    
    <div class="test-section">
        <h2>Test S3 URL Detection</h2>
        <div id="url-tests"></div>
    </div>

    <div class="test-section">
        <h2>Test PDF Viewing</h2>
        <p>Testing if S3 PDFs can be viewed in iframe:</p>
        <div class="test-url">Sample S3 URL: https://innoventory3solutions.s3.us-east-1.amazonaws.com/vendors/1/gst-123456-sample.pdf</div>
        <iframe src="https://innoventory3solutions.s3.us-east-1.amazonaws.com/vendors/1/gst-123456-sample.pdf" 
                title="Test PDF" 
                onload="console.log('PDF loaded successfully')"
                onerror="console.log('PDF failed to load')">
        </iframe>
    </div>

    <div class="test-section">
        <h2>Test Image Viewing</h2>
        <p>Testing if S3 images can be viewed:</p>
        <div class="test-url">Sample S3 Image URL: https://innoventory3solutions.s3.us-east-1.amazonaws.com/vendors/1/logo-123456-sample.jpg</div>
        <img src="https://innoventory3solutions.s3.us-east-1.amazonaws.com/vendors/1/logo-123456-sample.jpg" 
             alt="Test Image"
             onload="console.log('Image loaded successfully')"
             onerror="console.log('Image failed to load')">
    </div>

    <script>
        // Test URL detection logic
        function testUrlDetection() {
            const testUrls = [
                'https://innoventory3solutions.s3.us-east-1.amazonaws.com/vendors/1/gst-123456-sample.pdf',
                'https://s3.amazonaws.com/bucket/file.pdf',
                'https://bucket.s3.amazonaws.com/file.pdf',
                '/uploads/local/file.pdf',
                'blob:http://localhost:5173/abc-123'
            ];

            const container = document.getElementById('url-tests');
            
            testUrls.forEach(url => {
                const isS3 = url.includes('s3.amazonaws.com') || url.includes('amazonaws.com');
                const div = document.createElement('div');
                div.innerHTML = `
                    <div class="test-url">${url}</div>
                    <div class="${isS3 ? 'success' : 'error'}">
                        ${isS3 ? '✅ Detected as S3 URL' : '❌ Not detected as S3 URL'}
                    </div>
                `;
                container.appendChild(div);
            });
        }

        // Test document click simulation
        function simulateDocumentClick(documentType, documentUrl) {
            console.log('🔍 Simulating document click:', { documentType, documentUrl });

            let workingUrl = documentUrl;

            // If it's an S3 URL, use it directly
            if (documentUrl && (documentUrl.includes('s3.amazonaws.com') || documentUrl.includes('amazonaws.com'))) {
                workingUrl = documentUrl;
                console.log('🔗 Using S3 URL directly:', workingUrl);
                return { success: true, url: workingUrl };
            } else {
                console.log('🔗 Non-S3 URL, would need processing:', documentUrl);
                return { success: false, url: null };
            }
        }

        // Run tests
        document.addEventListener('DOMContentLoaded', function() {
            testUrlDetection();
            
            // Test the document click logic
            console.log('Testing document click logic:');
            console.log(simulateDocumentClick('Test PDF', 'https://innoventory3solutions.s3.us-east-1.amazonaws.com/vendors/1/test.pdf'));
            console.log(simulateDocumentClick('Local File', '/uploads/local/test.pdf'));
        });
    </script>
</body>
</html>
