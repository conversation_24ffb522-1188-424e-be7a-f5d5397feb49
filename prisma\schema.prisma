generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model activity_logs {
  id          String   @id
  action      String
  description String
  entityType  String
  entityId    String
  userId      String
  orderId     String?
  createdAt   DateTime @default(now())
  users       users    @relation(fields: [userId], references: [id])
}

model company_types {
  id          String      @id
  name        String      @unique
  description String?
  isActive    Boolean     @default(true)
  createdAt   DateTime    @default(now())
  updatedAt   DateTime    @updatedAt
  customers   customers[]
  vendors     vendors[]
}

model clients {
  id                 Int       @id @default(autoincrement())
  company_name       String    @db.VarChar(255)
  company_type       String?   @db.VarChar(100)
  onboarding_date    DateTime? @db.Date
  email              String?   @db.VarChar(255)
  phone              String?   @db.VarChar(20)
  emails             Json?     @default("[]")
  phones             Json?     @default("[]")
  address            String?
  country            String?   @db.VarChar(100)
  state              String?   @db.VarChar(100)
  city               String?   @db.Var<PERSON>har(100)
  dpiit_registered   Boolean?  @default(false)
  dpiit_number       String?   @db.VarChar(100)
  files              Json?     @default("{}")
  status             String?   @default("Active") @db.VarChar(50)
  created_at         DateTime? @default(now()) @db.Timestamp(6)
  updated_at         DateTime? @default(now()) @db.Timestamp(6)
  agreement_files_s3 Json?     @default("[]")
  dpiit_files_s3     Json?     @default("[]")
  files_s3           Json?     @default("{}")
  gst_files_s3       Json?     @default("[]")
  nda_files_s3       Json?     @default("[]")
  others_files_s3    Json?     @default("[]")
  pancard_files_s3   Json?     @default("[]")
  quotation_files_s3 Json?     @default("[]")
  tds_files_s3       Json?     @default("[]")
  udhyam_files_s3    Json?     @default("[]")
}

model customers {
  id                    String         @id
  name                  String
  email                 String         @unique
  phone                 String?
  company               String?
  country               String
  address               String?
  isActive              Boolean        @default(true)
  createdAt             DateTime       @default(now())
  updatedAt             DateTime
  createdById           String
  agreementFileUrl      String?
  city                  String?
  clientOnboardingDate  DateTime?
  companyName           String?
  companyType           String?
  dpiitCertificateUrl   String?
  dpiitRegister         String?
  dpiitValidTill        DateTime?
  gstFileUrl            String?
  gstNumber             String?
  individualName        String?
  ndaFileUrl            String?
  otherDocsUrls         String[]
  panCardFileUrl        String?
  pointOfContact        String?
  quotationFileUrl      String?
  state                 String?
  tdsFileUrl            String?
  udhyamRegistrationUrl String?
  username              String?
  companyTypeId         String?
  companyTypeRef        company_types? @relation(fields: [companyTypeId], references: [id])
  users                 users          @relation(fields: [createdById], references: [id])
}

model invoices {
  id            String        @id
  invoiceNumber String        @unique
  orderId       String
  amount        Float
  paidAmount    Float         @default(0)
  currency      String        @default("INR")
  status        InvoiceStatus @default(PENDING)
  dueDate       DateTime
  paidDate      DateTime?
  imageUrl      String?
  createdAt     DateTime      @default(now())
  updatedAt     DateTime
}

model orders {
  id                                           Int       @id @default(autoincrement())
  orderreferencenumber                         String    @unique @db.VarChar(100)
  order_reference_number                       String?   @db.VarChar(100)
  orderonboardingdate                          DateTime? @db.Date
  order_onboarding_date                        DateTime? @db.Date
  client                                       String?   @db.VarChar(255)
  typeofwork                                   String?   @db.VarChar(255)
  type_of_work                                 String?   @db.VarChar(255)
  vendorname                                   String?   @db.VarChar(255)
  vendor_name                                  String?   @db.VarChar(255)
  dateofworkcompletionexpected                 DateTime? @db.Date
  date_of_work_completion_expected             DateTime? @db.Date
  totalinvoicevalue                            Decimal?  @db.Decimal(15, 2)
  total_invoice_value                          Decimal?  @db.Decimal(15, 2)
  totalvaluegstgovtfees                        Decimal?  @db.Decimal(15, 2)
  total_value_gst_govt_fees                    Decimal?  @db.Decimal(15, 2)
  dateofpaymentexpected                        DateTime? @db.Date
  date_of_payment_expected                     DateTime? @db.Date
  dateofonboardingvendor                       DateTime? @db.Date
  date_of_onboarding_vendor                    DateTime? @db.Date
  currentstatus                                String?   @default("Pending") @db.VarChar(100)
  current_status                               String?   @default("Pending") @db.VarChar(100)
  statuscomments                               String?
  status_comments                              String?
  dateofstatuschange                           DateTime? @db.Date
  date_of_status_change                        DateTime? @db.Date
  dateofworkcompletionexpectedfromvendor       DateTime? @db.Date
  date_of_work_completion_expected_from_vendor DateTime? @db.Date
  amounttobepaidtovendor                       Decimal?  @db.Decimal(15, 2)
  amount_to_be_paid_to_vendor                  Decimal?  @db.Decimal(15, 2)
  amountpaidtovendor                           Decimal?  @default(0) @db.Decimal(15, 2)
  amount_paid_to_vendor                        Decimal?  @default(0) @db.Decimal(15, 2)
  statushistory                                Json?     @default("[]")
  status_history                               Json?     @default("[]")
  documentsprovidedbyclient                    Json?     @default("[]")
  documents_provided_by_client                 Json?     @default("[]")
  documentsprovidedbyvendor                    Json?     @default("[]")
  documents_provided_by_vendor                 Json?     @default("[]")
  invoicefromvendor                            Json?     @default("[]")
  invoice_from_vendor                          Json?     @default("[]")
  created_at                                   DateTime? @default(now()) @db.Timestamp(6)
  updated_at                                   DateTime? @default(now()) @db.Timestamp(6)
  customerinvoice_files_s3                     Json?     @default("[]")
  files_s3                                     Json?     @default("{}")
  orderdocs_files_s3                           Json?     @default("[]")
  orderimage_files_s3                          Json?     @default("[]")
  vendordocs_files_s3                          Json?     @default("[]")
  vendorinvoice_files_s3                       Json?     @default("[]")
}

model sub_admins {
  id                     Int       @id @default(autoincrement())
  name                   String    @db.VarChar(255)
  email                  String    @unique @db.VarChar(255)
  username               String?   @unique @db.VarChar(255)
  phone                  String?   @db.VarChar(20)
  subadminonboardingdate DateTime? @db.Date
  onboarding_date        DateTime? @db.Date
  address                String?
  country                String?   @db.VarChar(100)
  state                  String?   @db.VarChar(100)
  city                   String?   @db.VarChar(100)
  termofwork             String?   @db.VarChar(100)
  term_of_work           String?   @db.VarChar(100)
  role                   String?   @db.VarChar(100)
  department             String?   @db.VarChar(100)
  pan_number             String?   @db.VarChar(20)
  pannumber              String?   @db.VarChar(20)
  permissions            Json?     @default("[]")
  files                  Json?     @default("{}")
  uploadedfiles          Json?     @default("{}")
  status                 String?   @default("Active") @db.VarChar(50)
  last_login             DateTime? @db.Timestamp(6)
  lastlogin              String?   @db.VarChar(100)
  createddate            DateTime? @db.Date
  created_at             DateTime? @default(now()) @db.Timestamp(6)
  updated_at             DateTime? @default(now()) @db.Timestamp(6)
  employment_files_s3    Json?     @default("[]")
  files_s3               Json?     @default("{}")
  nda_files_s3           Json?     @default("[]")
  pancard_files_s3       Json?     @default("[]")
  tds_files_s3           Json?     @default("[]")
}

model type_of_work {
  id           Int       @id @default(autoincrement())
  name         String    @unique @db.VarChar(255)
  description  String?
  status       String?   @default("Active") @db.VarChar(50)
  createddate  DateTime? @default(dbgenerated("CURRENT_DATE")) @db.Date
  created_date DateTime? @default(dbgenerated("CURRENT_DATE")) @db.Date
  isActive     Boolean?  @default(true)
  created_at   DateTime? @default(now()) @db.Timestamp(6)
  updated_at   DateTime? @default(now()) @db.Timestamp(6)
}

model user_permissions {
  id         String     @id
  userId     String
  permission Permission
  createdAt  DateTime   @default(now())
  users      users      @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([userId, permission])
}

model users {
  id                     String             @id
  email                  String             @unique
  password               String
  isActive               Boolean            @default(true)
  createdAt              DateTime           @default(now())
  updatedAt              DateTime
  createdById            String?
  name                   String
  role                   UserRole           @default(SUB_ADMIN)
  address                String?
  city                   String?
  country                String?
  employmentAgreementUrl String?
  ndaFileUrl             String?
  otherDocsUrls          String[]
  panCardFileUrl         String?
  panNumber              String?
  state                  String?
  subAdminOnboardingDate DateTime?
  tdsFileUrl             String?
  termOfWork             String?
  username               String?
  activity_logs          activity_logs[]
  customers              customers[]
  user_permissions       user_permissions[]
  users                  users?             @relation("usersTousers", fields: [createdById], references: [id])
  other_users            users[]            @relation("usersTousers")
}

model vendors {
  id                 Int            @id @default(autoincrement())
  company_name       String         @db.VarChar(255)
  company_type       String?        @db.VarChar(100)
  onboarding_date    DateTime?      @db.Date
  onboardingdate     DateTime?      @db.Date
  emails             Json?          @default("[]")
  phones             Json?          @default("[]")
  address            String?
  country            String?        @db.VarChar(100)
  state              String?        @db.VarChar(100)
  city               String?        @db.VarChar(100)
  username           String?        @db.VarChar(255)
  gst_number         String?        @db.VarChar(50)
  description        String?
  services           Json?          @default("[]")
  website            String?        @db.VarChar(255)
  type_of_work       String?        @db.VarChar(255)
  status             String?        @default("Active") @db.VarChar(50)
  files              Json?          @default("{}")
  rating             Decimal?       @default(0.00) @db.Decimal(3, 2)
  total_orders       Int?           @default(0)
  totalorders        Int?           @default(0)
  created_at         DateTime?      @default(now()) @db.Timestamp(6)
  updated_at         DateTime?      @default(now()) @db.Timestamp(6)
  agreement_files_s3 Json?          @default("[]")
  files_s3           Json?          @default("{}")
  gst_files_s3       Json?          @default("[]")
  logo_files_s3      Json?          @default("[]")
  nda_files_s3       Json?          @default("[]")
  companyTypeId      String?
  companyType        company_types? @relation(fields: [companyTypeId], references: [id])
}

enum InvoiceStatus {
  PENDING
  PAID
  OVERDUE
  CANCELLED
}

enum OrderStatus {
  YET_TO_START
  IN_PROGRESS
  PENDING_WITH_CLIENT
  PENDING_PAYMENT
  COMPLETED
  CLOSED
  CANCELLED
}

enum OrderType {
  PATENT
  TRADEMARK
  COPYRIGHT
  DESIGN
}

enum Permission {
  MANAGE_USERS
  MANAGE_CUSTOMERS
  MANAGE_VENDORS
  MANAGE_ORDERS
  VIEW_ANALYTICS
  MANAGE_PAYMENTS
  VIEW_REPORTS
}

enum Priority {
  LOW
  MEDIUM
  HIGH
  URGENT
}

enum UserRole {
  ADMIN
  SUB_ADMIN
}
