import { sql } from '../config/database.js';
import { createUserAccount, sendLoginCredentials } from '../utils/createAuthenticationSystem.js';
import bcrypt from 'bcryptjs';

/**
 * Authentication Service
 * Handles user login, logout, and session management
 */

// Login user
export const loginUser = async (email, password) => {
  try {
    console.log('🔐 Attempting to authenticate user:', email);

    // Find user by email
    const users = await sql`
      SELECT 
        id,
        email,
        password,
        name,
        role,
        "isActive",
        "createdAt",
        "updatedAt"
      FROM users 
      WHERE email = ${email}
      LIMIT 1
    `;

    if (users.length === 0) {
      console.log('❌ User not found:', email);
      return {
        success: false,
        message: 'Invalid email or password'
      };
    }

    const user = users[0];

    // Check if user is active
    if (!user.isActive) {
      console.log('❌ User account is inactive:', email);
      return {
        success: false,
        message: 'Account is inactive. Please contact administrator.'
      };
    }

    // Verify password
    const isPasswordValid = await bcrypt.compare(password, user.password);
    
    if (!isPasswordValid) {
      console.log('❌ Invalid password for user:', email);
      return {
        success: false,
        message: 'Invalid email or password'
      };
    }

    // Update last updated timestamp
    await sql`
      UPDATE users
      SET "updatedAt" = NOW()
      WHERE id = ${user.id}
    `;

    // Generate session token (simple implementation)
    const token = generateSessionToken(user.id);

    // Remove password from response
    const { password: _, ...userWithoutPassword } = user;

    console.log('✅ User authenticated successfully:', email);

    return {
      success: true,
      user: userWithoutPassword,
      token: token,
      message: 'Login successful'
    };

  } catch (error) {
    console.error('❌ Authentication error:', error);
    return {
      success: false,
      message: 'Authentication failed. Please try again.'
    };
  }
};

// Create admin user (for initial setup)
export const createAdminUser = async (userData) => {
  try {
    console.log('👤 Creating admin user:', userData.email);

    // Check if user already exists
    const existingUsers = await sql`
      SELECT id FROM users WHERE email = ${userData.email}
    `;

    if (existingUsers.length > 0) {
      return {
        success: false,
        message: 'User with this email already exists'
      };
    }

    // Hash password
    const hashedPassword = await bcrypt.hash(userData.password, 12);

    // Generate user ID
    const userId = `admin-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;

    // Create user
    const newUser = await sql`
      INSERT INTO users (
        id,
        email,
        password,
        name,
        role,
        "isActive",
        "createdAt",
        "updatedAt"
      ) VALUES (
        ${userId},
        ${userData.email},
        ${hashedPassword},
        ${userData.name},
        ${userData.role || 'ADMIN'},
        ${true},
        NOW(),
        NOW()
      )
      RETURNING id, email, name, role, "isActive", "createdAt"
    `;

    console.log('✅ Admin user created successfully:', userData.email);

    return {
      success: true,
      user: newUser[0],
      message: 'Admin user created successfully'
    };

  } catch (error) {
    console.error('❌ Error creating admin user:', error);
    return {
      success: false,
      message: 'Failed to create admin user'
    };
  }
};

// Create sub-admin user
export const createSubAdminUser = async (userData, createdById) => {
  try {
    console.log('👤 Creating sub-admin user:', userData.email);

    // Check if user already exists
    const existingUsers = await sql`
      SELECT id FROM users WHERE email = ${userData.email}
    `;

    if (existingUsers.length > 0) {
      return {
        success: false,
        message: 'User with this email already exists'
      };
    }

    // Hash password
    const hashedPassword = await bcrypt.hash(userData.password, 12);

    // Generate user ID
    const userId = `subadmin-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;

    // Create user
    const newUser = await sql`
      INSERT INTO users (
        id,
        email,
        password,
        name,
        role,
        "isActive",
        "createdById",
        address,
        city,
        state,
        country,
        "panNumber",
        "subAdminOnboardingDate",
        "createdAt",
        "updatedAt"
      ) VALUES (
        ${userId},
        ${userData.email},
        ${hashedPassword},
        ${userData.name},
        ${'SUB_ADMIN'},
        ${userData.isActive !== undefined ? userData.isActive : true},
        ${createdById},
        ${userData.address || null},
        ${userData.city || null},
        ${userData.state || null},
        ${userData.country || null},
        ${userData.panNumber || null},
        ${userData.subAdminOnboardingDate || null},
        NOW(),
        NOW()
      )
      RETURNING id, email, name, role, "isActive", "createdAt"
    `;

    // Create user permissions if provided
    if (userData.permissions && userData.permissions.length > 0) {
      try {
        const { createUserPermissions } = await import('./userPermissionsService.js');
        await createUserPermissions(userId, userData.permissions);
        console.log('✅ User permissions created for sub-admin');
      } catch (permError) {
        console.error('⚠️ Error creating permissions (user created successfully):', permError);
        // Don't fail the entire operation if permissions fail
      }
    }

    console.log('✅ Sub-admin user created successfully:', userData.email);

    // Also create user account in new authentication system
    try {
      console.log('🔐 Creating username-based login account for sub-admin...');

      const userAccountData = {
        username: userData.username || userData.email.split('@')[0], // Use username or email prefix
        email: userData.email,
        userType: 'subadmin',
        userId: newUser[0].id
      };

      const userResult = await createUserAccount(userAccountData);

      if (userResult.success) {
        console.log('✅ Username-based account created for sub-admin:', userResult.user.username);

        // Send login credentials via email
        const emailResult = await sendLoginCredentials(
          userResult.user.email,
          userResult.user.username,
          userResult.password,
          'subadmin'
        );

        if (emailResult.success) {
          console.log('✅ Login credentials sent to sub-admin email');
        } else {
          console.warn('⚠️ Failed to send login credentials email:', emailResult.error);
        }

        // Add user account info to response
        newUser[0].userAccount = {
          username: userResult.user.username,
          email: userResult.user.email,
          credentialsSent: emailResult.success
        };
      } else {
        console.warn('⚠️ Failed to create username-based account for sub-admin:', userResult.error);
      }
    } catch (authError) {
      console.warn('⚠️ Username-based authentication setup failed for sub-admin:', authError);
      // Don't fail sub-admin creation if auth setup fails
    }

    return {
      success: true,
      user: newUser[0],
      message: 'Sub-admin user created successfully'
    };

  } catch (error) {
    console.error('❌ Error creating sub-admin user:', error);
    return {
      success: false,
      message: 'Failed to create sub-admin user'
    };
  }
};

// Get current user from token
export const getCurrentUser = () => {
  try {
    const userStr = localStorage.getItem('currentUser');
    const token = localStorage.getItem('authToken');
    
    if (!userStr || !token) {
      return null;
    }

    const user = JSON.parse(userStr);
    
    // Validate token (simple implementation)
    if (isValidToken(token)) {
      return user;
    }
    
    return null;
  } catch (error) {
    console.error('❌ Error getting current user:', error);
    return null;
  }
};

// Logout user
export const logoutUser = () => {
  try {
    localStorage.removeItem('currentUser');
    localStorage.removeItem('authToken');
    console.log('✅ User logged out successfully');
    return true;
  } catch (error) {
    console.error('❌ Error during logout:', error);
    return false;
  }
};

// Check if user is authenticated
export const isAuthenticated = () => {
  const user = getCurrentUser();
  return user !== null;
};

// Check if user has specific role
export const hasRole = (requiredRole) => {
  const user = getCurrentUser();
  if (!user) return false;
  
  if (requiredRole === 'ADMIN') {
    return user.role === 'ADMIN';
  }
  
  if (requiredRole === 'SUB_ADMIN') {
    return user.role === 'SUB_ADMIN' || user.role === 'ADMIN';
  }
  
  return false;
};

// Generate session token (simple implementation)
const generateSessionToken = (userId) => {
  const timestamp = Date.now();
  const random = Math.random().toString(36).substr(2, 9);
  return `${userId}-${timestamp}-${random}`;
};

// Validate token (simple implementation)
const isValidToken = (token) => {
  try {
    const parts = token.split('-');
    if (parts.length < 3) return false;
    
    const timestamp = parseInt(parts[1]);
    const now = Date.now();
    
    // Token expires after 24 hours
    const expirationTime = 24 * 60 * 60 * 1000;
    
    return (now - timestamp) < expirationTime;
  } catch (error) {
    return false;
  }
};
