import { useEffect, useState } from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { getCurrentUser, isAuthenticated } from '../../services/enhancedAuthService';

const ProtectedRoute = ({ children, requiredRole = null }) => {
  const [loading, setLoading] = useState(true);
  const [user, setUser] = useState(null);
  const location = useLocation();

  useEffect(() => {
    const checkAuth = () => {
      try {
        console.log('🔍 ProtectedRoute: Checking authentication...');
        const authenticated = isAuthenticated();
        console.log('🔍 ProtectedRoute: Is authenticated?', authenticated);

        if (authenticated) {
          const currentUser = getCurrentUser();
          console.log('🔍 ProtectedRoute: Current user:', currentUser);
          setUser(currentUser);
        } else {
          console.log('🔍 ProtectedRoute: User not authenticated, will redirect to login');
        }
      } catch (error) {
        console.error('❌ Auth check error:', error);
      } finally {
        setLoading(false);
      }
    };

    checkAuth();
  }, []);

  // Show loading spinner while checking authentication
  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Checking authentication...</p>
        </div>
      </div>
    );
  }

  // Redirect to login if not authenticated
  if (!user) {
    return <Navigate to="/login" state={{ from: location }} replace />;
  }

  // Check role-based access
  if (requiredRole) {
    // Convert old role format to new format
    const userRole = user.userType || user.role;
    const hasAccess = checkRoleAccess(userRole, requiredRole);

    if (!hasAccess) {
      return (
        <div className="min-h-screen bg-gray-50 flex items-center justify-center">
          <div className="text-center">
            <div className="mx-auto h-16 w-16 bg-red-100 rounded-full flex items-center justify-center mb-4">
              <svg className="h-8 w-8 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 18.5c-.77.833.192 2.5 1.732 2.5z" />
              </svg>
            </div>
            <h2 className="text-xl font-semibold text-gray-900 mb-2">Access Denied</h2>
            <p className="text-gray-600 mb-4">You don't have permission to access this page.</p>
            <p className="text-sm text-gray-500">Required role: {requiredRole} | Your role: {userRole}</p>
          </div>
        </div>
      );
    }
  }

  // Helper function to check role access
  function checkRoleAccess(userRole, requiredRole) {
    // Admin access
    if (requiredRole === 'ADMIN') {
      return userRole === 'admin' || userRole === 'ADMIN';
    }

    // Sub-admin access (admin can also access)
    if (requiredRole === 'SUB_ADMIN') {
      return userRole === 'admin' || userRole === 'ADMIN' || userRole === 'subadmin' || userRole === 'SUB_ADMIN';
    }

    // Vendor access
    if (requiredRole === 'VENDOR') {
      return userRole === 'vendor' || userRole === 'VENDOR';
    }

    return false;
  }

  // Render protected content
  return children;
};

export default ProtectedRoute;
