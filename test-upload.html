<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Upload Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .upload-area {
            border: 2px dashed #ccc;
            border-radius: 10px;
            padding: 40px;
            text-align: center;
            margin: 20px 0;
            cursor: pointer;
        }
        .upload-area:hover {
            border-color: #007bff;
            background-color: #f8f9fa;
        }
        .result {
            margin: 20px 0;
            padding: 15px;
            border-radius: 5px;
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .info {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        .progress {
            width: 100%;
            height: 20px;
            background-color: #f0f0f0;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }
        .progress-bar {
            height: 100%;
            background-color: #007bff;
            width: 0%;
            transition: width 0.3s ease;
        }
    </style>
</head>
<body>
    <h1>🧪 S3 Upload Test</h1>
    
    <div class="info">
        <h3>📋 Test Information</h3>
        <p><strong>Environment:</strong> <span id="environment">Loading...</span></p>
        <p><strong>Upload Endpoint:</strong> <span id="endpoint">Loading...</span></p>
        <p><strong>S3 Bucket:</strong> innoventory3solutions</p>
        <p><strong>Region:</strong> us-east-1</p>
    </div>

    <div class="upload-area" onclick="document.getElementById('fileInput').click()">
        <h3>📁 Click to Select File or Drag & Drop</h3>
        <p>Supported: PDF, Images, Office Documents (Max: 10MB)</p>
        <input type="file" id="fileInput" style="display: none;" accept=".pdf,.jpg,.jpeg,.png,.gif,.doc,.docx,.xls,.xlsx">
    </div>

    <div id="uploadProgress" style="display: none;">
        <h4>⏳ Uploading...</h4>
        <div class="progress">
            <div class="progress-bar" id="progressBar"></div>
        </div>
        <p id="progressText">0%</p>
    </div>

    <div>
        <button onclick="testUpload()" id="uploadBtn" disabled>🚀 Test Upload</button>
        <button onclick="testEndpoint()">🔍 Test Endpoint</button>
        <button onclick="clearResults()">🧹 Clear Results</button>
    </div>

    <div id="results"></div>

    <script>
        let selectedFile = null;
        
        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            updateEnvironmentInfo();
            setupFileInput();
        });

        function updateEnvironmentInfo() {
            const isDev = window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1';
            const environment = isDev ? 'Development' : 'Production';
            const endpoint = isDev ? `${window.location.origin}/api/upload` : '/api/upload';
            
            document.getElementById('environment').textContent = environment;
            document.getElementById('endpoint').textContent = endpoint;
        }

        function setupFileInput() {
            const fileInput = document.getElementById('fileInput');
            fileInput.addEventListener('change', function(e) {
                if (e.target.files.length > 0) {
                    selectedFile = e.target.files[0];
                    document.getElementById('uploadBtn').disabled = false;
                    addResult(`📄 File selected: ${selectedFile.name} (${formatFileSize(selectedFile.size)})`, 'info');
                }
            });
        }

        async function testEndpoint() {
            const endpoint = document.getElementById('endpoint').textContent;
            addResult('🔍 Testing endpoint availability...', 'info');
            
            try {
                const response = await fetch(endpoint, {
                    method: 'OPTIONS'
                });
                
                if (response.ok) {
                    addResult(`✅ Endpoint is accessible: ${endpoint}`, 'success');
                } else {
                    addResult(`⚠️ Endpoint returned status: ${response.status}`, 'error');
                }
            } catch (error) {
                addResult(`❌ Endpoint test failed: ${error.message}`, 'error');
            }
        }

        async function testUpload() {
            if (!selectedFile) {
                addResult('❌ Please select a file first', 'error');
                return;
            }

            const endpoint = document.getElementById('endpoint').textContent;
            const uploadBtn = document.getElementById('uploadBtn');
            const progressDiv = document.getElementById('uploadProgress');
            const progressBar = document.getElementById('progressBar');
            const progressText = document.getElementById('progressText');

            uploadBtn.disabled = true;
            progressDiv.style.display = 'block';
            
            addResult('🚀 Starting upload test...', 'info');

            try {
                // Prepare form data
                const formData = new FormData();
                formData.append('file', selectedFile);

                // Test parameters
                const module = 'test';
                const recordId = 'test-' + Date.now();
                const fileType = 'document';
                const allowedTypes = 'all';

                const uploadUrl = `${endpoint}?module=${module}&recordId=${recordId}&fileType=${fileType}&allowedTypes=${allowedTypes}`;
                
                addResult(`📤 Uploading to: ${uploadUrl}`, 'info');

                // Simulate progress
                let progress = 0;
                const progressInterval = setInterval(() => {
                    progress += 10;
                    if (progress <= 90) {
                        progressBar.style.width = progress + '%';
                        progressText.textContent = progress + '%';
                    }
                }, 100);

                const response = await fetch(uploadUrl, {
                    method: 'POST',
                    body: formData
                });

                clearInterval(progressInterval);
                progressBar.style.width = '100%';
                progressText.textContent = '100%';

                const result = await response.json();

                if (response.ok && result.success) {
                    addResult('✅ Upload successful!', 'success');
                    addResult(`📄 File details:
                        • Key: ${result.file.key}
                        • URL: ${result.file.url}
                        • Size: ${formatFileSize(result.file.size)}
                        • Type: ${result.file.type}`, 'success');
                    
                    // Test file access
                    testFileAccess(result.file.url);
                } else {
                    addResult(`❌ Upload failed: ${result.error || 'Unknown error'}`, 'error');
                    console.error('Upload response:', result);
                }

            } catch (error) {
                addResult(`❌ Upload error: ${error.message}`, 'error');
                console.error('Upload error:', error);
            } finally {
                uploadBtn.disabled = false;
                setTimeout(() => {
                    progressDiv.style.display = 'none';
                    progressBar.style.width = '0%';
                    progressText.textContent = '0%';
                }, 2000);
            }
        }

        async function testFileAccess(fileUrl) {
            addResult('🔍 Testing file access...', 'info');
            
            try {
                const response = await fetch(fileUrl, { method: 'HEAD' });
                
                if (response.ok) {
                    addResult(`✅ File is accessible: ${fileUrl}`, 'success');
                    addResult(`<a href="${fileUrl}" target="_blank">🔗 Open file in new tab</a>`, 'success');
                } else {
                    addResult(`❌ File access denied (${response.status}): ${fileUrl}`, 'error');
                }
            } catch (error) {
                addResult(`❌ File access test failed: ${error.message}`, 'error');
            }
        }

        function addResult(message, type) {
            const resultsDiv = document.getElementById('results');
            const resultDiv = document.createElement('div');
            resultDiv.className = `result ${type}`;
            resultDiv.innerHTML = `<strong>${new Date().toLocaleTimeString()}</strong>: ${message}`;
            resultsDiv.appendChild(resultDiv);
            resultsDiv.scrollTop = resultsDiv.scrollHeight;
        }

        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }

        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        // Drag and drop functionality
        const uploadArea = document.querySelector('.upload-area');
        
        uploadArea.addEventListener('dragover', function(e) {
            e.preventDefault();
            uploadArea.style.borderColor = '#007bff';
            uploadArea.style.backgroundColor = '#f8f9fa';
        });

        uploadArea.addEventListener('dragleave', function(e) {
            e.preventDefault();
            uploadArea.style.borderColor = '#ccc';
            uploadArea.style.backgroundColor = 'transparent';
        });

        uploadArea.addEventListener('drop', function(e) {
            e.preventDefault();
            uploadArea.style.borderColor = '#ccc';
            uploadArea.style.backgroundColor = 'transparent';
            
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                selectedFile = files[0];
                document.getElementById('uploadBtn').disabled = false;
                addResult(`📄 File dropped: ${selectedFile.name} (${formatFileSize(selectedFile.size)})`, 'info');
            }
        });
    </script>
</body>
</html>
