import { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
  ArrowLeftIcon,
  PencilIcon,
  PrinterIcon,
  DocumentArrowDownIcon,
  BuildingOfficeIcon,
  EnvelopeIcon,
  PhoneIcon,
  GlobeAltIcon,
  UserIcon,
  CreditCardIcon,
  XMarkIcon,
  EyeIcon
} from '@heroicons/react/24/outline';
import jsPDF from 'jspdf';
import html2canvas from 'html2canvas';
import * as XLSX from 'xlsx';
import FileViewer from '../components/FileViewer/FileViewer';
import { getWorkingFileUrl } from '../services/fileUploadService';
import { openFileWithFallbacks } from '../utils/fileUtils';

const ClientView = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const [client, setClient] = useState(null);
  const [loading, setLoading] = useState(true);
  const [selectedDocument, setSelectedDocument] = useState(null);
  const [showDocumentViewer, setShowDocumentViewer] = useState(false);
  const [showEmailModal, setShowEmailModal] = useState(false);
  const [showMeetingModal, setShowMeetingModal] = useState(false);
  const [showReportModal, setShowReportModal] = useState(false);

  // Load client data from database service
  useEffect(() => {
    const loadClient = async () => {
      try {
        setLoading(true);
        console.log('🔄 Loading client data for ID:', id);

        // Import and use database service
        const { getClientById } = await import('../services/clientService');
        const clientData = await getClientById(id);

        if (clientData) {
          console.log('✅ Client data loaded:', clientData);
          console.log('📁 Client files structure:', clientData.files);

          // Transform client data to ensure proper structure
          const transformedClient = {
            ...clientData,
            // Ensure emails and phones are arrays
            emails: Array.isArray(clientData.emails) ? clientData.emails :
                    (clientData.emails ? [clientData.emails] : []),
            phones: Array.isArray(clientData.phones) ? clientData.phones :
                    (clientData.phones ? [clientData.phones] : []),
            // Ensure files object exists
            files: clientData.files || {}
          };

          console.log('✅ Transformed client data:', transformedClient);
          setClient(transformedClient);
        } else {
          console.log('❌ Client not found');
          alert('Client not found');
          navigate('/clients');
        }
      } catch (error) {
        console.error('❌ Error loading client:', error);
        alert('Failed to load client data');
        navigate('/clients');
      } finally {
        setLoading(false);
      }
    };

    loadClient();
  }, [id, navigate]);

  const handleDocumentClick = async (documentType, documentUrl) => {
    try {
      console.log('🔍 Attempting to open client document:', { documentType, documentUrl });

      if (!documentUrl) {
        console.error('❌ No document URL provided');
        alert('Document URL is not available.');
        return;
      }

      // Use enhanced file opening with multiple fallback strategies
      const filename = `${documentType}.${documentUrl.split('.').pop()}`;
      const opened = await openFileWithFallbacks(documentUrl, filename);

      if (opened) {
        console.log('✅ Document opened successfully');
        return;
      }

      // If all strategies failed, fall back to modal viewer
      console.log('⚠️ All opening strategies failed, using modal viewer');

      let workingUrl = documentUrl;

      // If it's an S3 URL, try to generate a signed URL for private access
      if (documentUrl && (documentUrl.includes('s3.amazonaws.com') || documentUrl.includes('amazonaws.com'))) {
        try {
          // Extract the S3 key from the URL
          const urlParts = documentUrl.split('/');
          const bucketIndex = urlParts.findIndex(part => part.includes('s3.amazonaws.com'));
          if (bucketIndex !== -1 && urlParts.length > bucketIndex + 1) {
            const s3Key = urlParts.slice(bucketIndex + 1).join('/');
            console.log('🔑 Extracted S3 key:', s3Key);

            // Import and use the signed URL service
            const { getSignedFileUrl } = await import('../services/s3Service');
            workingUrl = await getSignedFileUrl(s3Key);
            console.log('🔗 Generated signed URL:', workingUrl);
          } else {
            // Fallback to direct URL
            workingUrl = documentUrl;
            console.log('🔗 Using direct S3 URL:', workingUrl);
          }
        } catch (signedUrlError) {
          console.warn('⚠️ Failed to generate signed URL, using direct URL:', signedUrlError);
          workingUrl = documentUrl;
        }
      } else {
        // For non-S3 URLs, try to get a working URL
        try {
          workingUrl = await getWorkingFileUrl(documentUrl, null, 'clients');
          console.log('🔗 Resolved working URL:', workingUrl);
        } catch (resolveError) {
          console.warn('⚠️ Failed to resolve URL, using original:', resolveError);
          workingUrl = documentUrl;
        }
      }

      if (workingUrl) {
        setSelectedDocument({
          type: documentType,
          url: workingUrl,
          name: documentType
        });
        setShowDocumentViewer(true);
        console.log('✅ Document viewer opened successfully');
      } else {
        console.error('❌ Could not resolve file URL for:', documentUrl);
        alert('Sorry, this document is not available for viewing.');
      }
    } catch (error) {
      console.error('❌ Error opening document:', error);
      alert('Error opening document. Please try again.');
    }
  };

  const closeDocumentViewer = () => {
    setShowDocumentViewer(false);
    setSelectedDocument(null);
  };

  // Print functionality
  const handlePrint = () => {
    const printContent = document.getElementById('client-print-content');
    const originalContent = document.body.innerHTML;

    // Create print-specific styles
    const printStyles = `
      <style>
        @media print {
          body { margin: 0; padding: 20px; font-family: Arial, sans-serif; }
          .no-print { display: none !important; }
          .print-only { display: block !important; }
          .card { border: 1px solid #ddd; margin-bottom: 20px; padding: 15px; }
          .grid { display: block; }
          .grid > div { margin-bottom: 15px; }
          h1 { font-size: 24px; margin-bottom: 10px; }
          h2 { font-size: 18px; margin-bottom: 8px; }
          .text-sm { font-size: 12px; }
          .text-gray-600 { color: #666; }
          .bg-green-100 { background-color: #f0f9ff; }
          .text-green-800 { color: #166534; }
        }
      </style>
    `;

    document.body.innerHTML = printStyles + printContent.innerHTML;
    window.print();
    document.body.innerHTML = originalContent;
    window.location.reload(); // Reload to restore functionality
  };

  // Export to PDF functionality
  const handleExportPDF = async () => {
    try {
      const element = document.getElementById('client-print-content');
      const canvas = await html2canvas(element, {
        scale: 2,
        useCORS: true,
        allowTaint: true
      });

      const imgData = canvas.toDataURL('image/png');
      const pdf = new jsPDF('p', 'mm', 'a4');
      const imgWidth = 210;
      const pageHeight = 295;
      const imgHeight = (canvas.height * imgWidth) / canvas.width;
      let heightLeft = imgHeight;
      let position = 0;

      pdf.addImage(imgData, 'PNG', 0, position, imgWidth, imgHeight);
      heightLeft -= pageHeight;

      while (heightLeft >= 0) {
        position = heightLeft - imgHeight;
        pdf.addPage();
        pdf.addImage(imgData, 'PNG', 0, position, imgWidth, imgHeight);
        heightLeft -= pageHeight;
      }

      pdf.save(`client-${client.companyName || 'details'}.pdf`);
    } catch (error) {
      console.error('Error generating PDF:', error);
      alert('Failed to generate PDF. Please try again.');
    }
  };

  // Export to Excel functionality
  const handleExportExcel = () => {
    try {
      const workbook = XLSX.utils.book_new();

      // Client basic information
      const clientData = [
        ['Client Information', ''],
        ['Company Name', client.companyName || 'Not specified'],
        ['Company Type', client.companyType || 'Not specified'],
        ['Email', client.emails?.[0] || 'Not specified'],
        ['Phone', client.phones?.[0] || 'Not specified'],
        ['Website', client.website || 'Not specified'],
        ['GST Number', client.gstNumber || 'Not specified'],
        ['DPIIT Registered', client.dpitRegistered || 'Not specified'],
        ['Username', client.username || 'Not specified'],
        ['Onboarding Date', client.onboardingDate || 'Not specified'],
        [''],
        ['Address Information', ''],
        ['Address', client.address || 'Not specified'],
        ['Country', client.country || 'Not specified'],
        ['State', client.state || 'Not specified'],
        ['City', client.city || 'Not specified'],
      ];

      // Add additional emails if any
      if (client.emails && client.emails.length > 1) {
        clientData.push([''], ['Additional Emails', '']);
        client.emails.slice(1).forEach((email, index) => {
          clientData.push([`Email ${index + 2}`, email]);
        });
      }

      // Add additional phones if any
      if (client.phones && client.phones.length > 1) {
        clientData.push([''], ['Additional Phones', '']);
        client.phones.slice(1).forEach((phone, index) => {
          clientData.push([`Phone ${index + 2}`, phone]);
        });
      }

      const worksheet = XLSX.utils.aoa_to_sheet(clientData);
      XLSX.utils.book_append_sheet(workbook, worksheet, 'Client Details');

      XLSX.writeFile(workbook, `client-${client.companyName || 'details'}.xlsx`);
    } catch (error) {
      console.error('Error generating Excel:', error);
      alert('Failed to generate Excel file. Please try again.');
    }
  };

  // Quick Actions Handlers
  const handleSendEmail = () => {
    setShowEmailModal(true);
  };

  const handleScheduleMeeting = () => {
    setShowMeetingModal(true);
  };

  const handleCreateNewOrder = () => {
    navigate('/orders/new', {
      state: {
        prefilledClient: {
          id: client.id,
          name: client.companyName,
          email: client.emails?.[0],
          phone: client.phones?.[0]
        }
      }
    });
  };

  const handleGenerateReport = () => {
    setShowReportModal(true);
  };

  // Email Modal Handler
  const handleSendEmailSubmit = (emailData) => {
    // In a real app, this would send the email via an API
    console.log('Sending email:', emailData);

    // Simulate email sending
    const emailBody = `
      To: ${emailData.to}
      Subject: ${emailData.subject}

      ${emailData.message}

      Best regards,
      ${emailData.from || 'Your Company'}
    `;

    // For demo purposes, we'll create a mailto link
    const mailtoLink = `mailto:${emailData.to}?subject=${encodeURIComponent(emailData.subject)}&body=${encodeURIComponent(emailData.message)}`;
    window.open(mailtoLink);

    setShowEmailModal(false);
    alert('Email client opened. Please send the email from your email application.');
  };

  // Meeting Modal Handler
  const handleScheduleMeetingSubmit = (meetingData) => {
    // In a real app, this would integrate with calendar APIs
    console.log('Scheduling meeting:', meetingData);

    // Create calendar event
    const startDate = new Date(meetingData.date + 'T' + meetingData.time);
    const endDate = new Date(startDate.getTime() + (meetingData.duration * 60000));

    const calendarEvent = {
      title: meetingData.title,
      start: startDate.toISOString(),
      end: endDate.toISOString(),
      description: meetingData.description,
      location: meetingData.location
    };

    // For demo purposes, create a Google Calendar link
    const googleCalendarUrl = `https://calendar.google.com/calendar/render?action=TEMPLATE&text=${encodeURIComponent(meetingData.title)}&dates=${startDate.toISOString().replace(/[-:]/g, '').split('.')[0]}Z/${endDate.toISOString().replace(/[-:]/g, '').split('.')[0]}Z&details=${encodeURIComponent(meetingData.description)}&location=${encodeURIComponent(meetingData.location)}`;

    window.open(googleCalendarUrl, '_blank');
    setShowMeetingModal(false);
    alert('Calendar event created. Please save it in your calendar application.');
  };

  // Report Generation Handler
  const handleGenerateReportSubmit = (reportData) => {
    console.log('Generating report:', reportData);

    if (reportData.type === 'pdf') {
      handleExportPDF();
    } else if (reportData.type === 'excel') {
      handleExportExcel();
    } else if (reportData.type === 'summary') {
      generateSummaryReport(reportData);
    }

    setShowReportModal(false);
  };

  const generateSummaryReport = (reportData) => {
    const reportContent = `
CLIENT SUMMARY REPORT
Generated on: ${new Date().toLocaleDateString()}
Report Period: ${reportData.dateRange || 'All Time'}

CLIENT INFORMATION:
- Company: ${client.companyName}
- Contact Person: ${client.contactPerson}
- Email: ${client.emails?.[0] || 'Not provided'}
- Phone: ${client.phones?.[0] || 'Not provided'}
- Status: ${client.status}
- Business Type: ${client.businessType}

ADDITIONAL DETAILS:
- Registration Date: ${client.createdAt ? new Date(client.createdAt).toLocaleDateString() : 'Not available'}
- Last Updated: ${client.updatedAt ? new Date(client.updatedAt).toLocaleDateString() : 'Not available'}
- Documents: ${client.files ? Object.keys(client.files).length : 0} files uploaded

NOTES:
${reportData.notes || 'No additional notes provided.'}

---
This report was generated automatically by the Innoventory Admin Panel.
    `;

    // Create and download the report
    const blob = new Blob([reportContent], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `client-summary-${client.companyName}-${new Date().toISOString().split('T')[0]}.txt`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (!client) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-900 mb-2">Client Not Found</h2>
          <p className="text-gray-600 mb-4">The client you're looking for doesn't exist.</p>
          <button
            onClick={() => navigate('/clients')}
            className="btn-primary"
          >
            Back to Clients
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      {/* Header */}
      <div className="mb-8">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <button
              onClick={() => navigate('/clients')}
              className="flex items-center text-gray-600 hover:text-gray-900"
            >
              <ArrowLeftIcon className="h-5 w-5 mr-2" />
              Back to Clients
            </button>
            <div>
              <h1 className="text-3xl font-bold text-gray-900">{client.company_name}</h1>
              <p className="text-gray-600">Client details and relationship overview</p>
            </div>
          </div>
          <div className="flex space-x-3">
            <button
              onClick={handlePrint}
              className="btn-secondary flex items-center"
            >
              <PrinterIcon className="h-4 w-4 mr-2" />
              Print
            </button>
            <div className="relative group">
              <button className="btn-secondary flex items-center">
                <DocumentArrowDownIcon className="h-4 w-4 mr-2" />
                Export
              </button>
              <div className="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg border border-gray-200 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-10">
                <button
                  onClick={handleExportPDF}
                  className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50"
                >
                  Export as PDF
                </button>
                <button
                  onClick={handleExportExcel}
                  className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50"
                >
                  Export as Excel
                </button>
              </div>
            </div>
            <button
              onClick={() => navigate(`/clients/${client.id}/edit`)}
              className="btn-primary flex items-center"
            >
              <PencilIcon className="h-4 w-4 mr-2" />
              Edit Client
            </button>
          </div>
        </div>
      </div>

      <div id="client-print-content">
        <div className="print-only" style={{display: 'none'}}>
          <h1>{client.companyName}</h1>
          <p>Client Details and Information</p>
          <hr style={{margin: '20px 0'}} />
        </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Main Content */}
        <div className="lg:col-span-2 space-y-6">
          {/* Client Overview */}
          <div className="card">
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-xl font-semibold text-gray-900">Client Overview</h2>
              <span className={`inline-flex px-3 py-1 text-sm font-semibold rounded-full ${
                client.status === 'Active' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
              }`}>
                {client.status}
              </span>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-4">
                <div className="flex items-center space-x-3">
                  <BuildingOfficeIcon className="h-5 w-5 text-gray-400" />
                  <div>
                    <p className="text-sm text-gray-500">Company Name</p>
                    <p className="text-sm font-medium text-gray-900">{client.company_name || 'Not specified'}</p>
                  </div>
                </div>

                <div className="flex items-center space-x-3">
                  <BuildingOfficeIcon className="h-5 w-5 text-gray-400" />
                  <div>
                    <p className="text-sm text-gray-500">Company Type</p>
                    <p className="text-sm font-medium text-gray-900">{client.company_type || 'Not specified'}</p>
                  </div>
                </div>

                <div className="flex items-center space-x-3">
                  <EnvelopeIcon className="h-5 w-5 text-gray-400" />
                  <div>
                    <p className="text-sm text-gray-500">Email Addresses</p>
                    <div>
                      {client.emails && client.emails.length > 0 ? (
                        client.emails.map((email, index) => (
                          <p key={index} className="text-sm font-medium text-gray-900">{email}</p>
                        ))
                      ) : (
                        <p className="text-sm font-medium text-gray-900">Not specified</p>
                      )}
                    </div>
                  </div>
                </div>

                <div className="flex items-center space-x-3">
                  <PhoneIcon className="h-5 w-5 text-gray-400" />
                  <div>
                    <p className="text-sm text-gray-500">Phone Numbers</p>
                    <div>
                      {client.phones && client.phones.length > 0 ? (
                        client.phones.map((phone, index) => (
                          <p key={index} className="text-sm font-medium text-gray-900">{phone}</p>
                        ))
                      ) : (
                        <p className="text-sm font-medium text-gray-900">Not specified</p>
                      )}
                    </div>
                  </div>
                </div>

                <div className="flex items-center space-x-3">
                  <UserIcon className="h-5 w-5 text-gray-400" />
                  <div>
                    <p className="text-sm text-gray-500">Username</p>
                    <p className="text-sm font-medium text-gray-900">{client.username || 'Not specified'}</p>
                  </div>
                </div>
                
              </div>

              <div className="space-y-4">
                <div className="flex items-center space-x-3">
                  <GlobeAltIcon className="h-5 w-5 text-gray-400" />
                  <div>
                    <p className="text-sm text-gray-500">Website</p>
                    {client.website ? (
                      <a href={client.website} target="_blank" rel="noopener noreferrer"
                         className="text-sm font-medium text-blue-600 hover:text-blue-800">
                        {client.website}
                      </a>
                    ) : (
                      <p className="text-sm font-medium text-gray-900">Not specified</p>
                    )}
                  </div>
                </div>

                <div className="flex items-center space-x-3">
                  <CreditCardIcon className="h-5 w-5 text-gray-400" />
                  <div>
                    <p className="text-sm text-gray-500">GST Number</p>
                    <p className="text-sm font-medium text-gray-900">{client.gstNumber || 'Not specified'}</p>
                  </div>
                </div>

                <div>
                  <p className="text-sm text-gray-500">DPIIT Registered</p>
                  <p className="text-sm font-medium text-gray-900">
                    {client.dpiit_registered ? 'Yes' : 'No'}
                  </p>
                </div>

                {client.dpiit_registered && (
                  <div>
                    <p className="text-sm text-gray-500">DPIIT Number</p>
                    <p className="text-sm font-medium text-gray-900">{client.dpiit_number || 'Not specified'}</p>
                  </div>
                )}

                <div>
                  <p className="text-sm text-gray-500">Onboarding Date</p>
                  <p className="text-sm font-medium text-gray-900">{client.onboarding_date || 'Not specified'}</p>
                </div>
              </div>
            </div>

            <div className="mt-6">
              <h3 className="text-sm font-medium text-gray-500 mb-2">Address</h3>
              <p className="text-sm text-gray-900">{client.address || 'Not specified'}</p>
              <div className="mt-2 grid grid-cols-3 gap-4">
                <div>
                  <p className="text-xs text-gray-500">Country</p>
                  <p className="text-sm text-gray-900">{client.country || 'Not specified'}</p>
                </div>
                <div>
                  <p className="text-xs text-gray-500">State</p>
                  <p className="text-sm text-gray-900">{client.state || 'Not specified'}</p>
                </div>
                <div>
                  <p className="text-xs text-gray-500">City</p>
                  <p className="text-sm text-gray-900">{client.city || 'Not specified'}</p>
                </div>
              </div>
            </div>

            {client.description && (
              <div className="mt-6">
                <h3 className="text-sm font-medium text-gray-500 mb-2">Description</h3>
                <p className="text-sm text-gray-700">{client.description}</p>
              </div>
            )}
          </div>

          {/* Uploaded Documents */}
          <div className="card">
            <h2 className="text-xl font-semibold text-gray-900 mb-6">Uploaded Documents</h2>



            <div className="space-y-4">
              {/* DPIIT Certificate */}
              {client.dpiit_registered && client.files?.dpiitCertificateUrl && (
                <div className="document-file-item cursor-pointer hover:bg-gray-50 transition-colors rounded-lg p-3"
                     onClick={() => handleDocumentClick('DPIIT Certificate', client.files.dpiitCertificateUrl)}>
                  <div className="flex items-center space-x-3">
                    <DocumentArrowDownIcon className="h-5 w-5 text-green-600" />
                    <div className="flex-1">
                      <p className="text-sm font-medium text-gray-900">DPIIT Certificate</p>
                      <p className="text-xs text-gray-500">Click to view • Downloadable</p>
                    </div>
                    <EyeIcon className="h-4 w-4 text-blue-600" />
                  </div>
                </div>
              )}

              {/* TDS File */}
              {client.files?.tdsFileUrl && (
                <div className="document-file-item cursor-pointer hover:bg-gray-50 transition-colors rounded-lg p-3"
                     onClick={() => handleDocumentClick('TDS File', client.files.tdsFileUrl)}>
                  <div className="flex items-center space-x-3">
                    <DocumentArrowDownIcon className="h-5 w-5 text-blue-600" />
                    <div className="flex-1">
                      <p className="text-sm font-medium text-gray-900">TDS File</p>
                      <p className="text-xs text-gray-500">Click to view • Downloadable</p>
                    </div>
                    <EyeIcon className="h-4 w-4 text-blue-600" />
                  </div>
                </div>
              )}

              {client.files?.gstFileUrl && (
                <div className="document-file-item cursor-pointer hover:bg-gray-50 transition-colors rounded-lg p-3"
                     onClick={() => handleDocumentClick('GST File', client.files.gstFileUrl)}>
                  <div className="flex items-center space-x-3">
                    <DocumentArrowDownIcon className="h-5 w-5 text-gray-600" />
                    <div className="flex-1">
                      <p className="text-sm font-medium text-gray-900">GST File</p>
                      <p className="text-xs text-gray-500">Click to view</p>
                    </div>
                    <EyeIcon className="h-4 w-4 text-blue-600" />
                  </div>
                </div>
              )}

              {client.files?.ndaFileUrl && (
                <div className="document-file-item cursor-pointer hover:bg-gray-50 transition-colors rounded-lg p-3"
                     onClick={() => handleDocumentClick('NDA', client.files.ndaFileUrl)}>
                  <div className="flex items-center space-x-3">
                    <DocumentArrowDownIcon className="h-5 w-5 text-gray-600" />
                    <div className="flex-1">
                      <p className="text-sm font-medium text-gray-900">NDA Document</p>
                      <p className="text-xs text-gray-500">Click to view</p>
                    </div>
                    <EyeIcon className="h-4 w-4 text-blue-600" />
                  </div>
                </div>
              )}

              {client.files?.agreementFileUrl && (
                <div className="document-file-item cursor-pointer hover:bg-gray-50 transition-colors rounded-lg p-3"
                     onClick={() => handleDocumentClick('Agreement', client.files.agreementFileUrl)}>
                  <div className="flex items-center space-x-3">
                    <DocumentArrowDownIcon className="h-5 w-5 text-gray-600" />
                    <div className="flex-1">
                      <p className="text-sm font-medium text-gray-900">Agreement Document</p>
                      <p className="text-xs text-gray-500">Click to view</p>
                    </div>
                    <EyeIcon className="h-4 w-4 text-blue-600" />
                  </div>
                </div>
              )}

              {client.files?.quotationFileUrl && (
                <div className="document-file-item cursor-pointer hover:bg-gray-50 transition-colors rounded-lg p-3"
                     onClick={() => handleDocumentClick('Quotation', client.files.quotationFileUrl)}>
                  <div className="flex items-center space-x-3">
                    <DocumentArrowDownIcon className="h-5 w-5 text-gray-600" />
                    <div className="flex-1">
                      <p className="text-sm font-medium text-gray-900">Quotation Document</p>
                      <p className="text-xs text-gray-500">Click to view</p>
                    </div>
                    <EyeIcon className="h-4 w-4 text-blue-600" />
                  </div>
                </div>
              )}

              {client.files?.panCardFileUrl && (
                <div className="document-file-item cursor-pointer hover:bg-gray-50 transition-colors rounded-lg p-3"
                     onClick={() => handleDocumentClick('Pan Card', client.files.panCardFileUrl)}>
                  <div className="flex items-center space-x-3">
                    <DocumentArrowDownIcon className="h-5 w-5 text-gray-600" />
                    <div className="flex-1">
                      <p className="text-sm font-medium text-gray-900">Pan Card Document</p>
                      <p className="text-xs text-gray-500">Click to view</p>
                    </div>
                    <EyeIcon className="h-4 w-4 text-blue-600" />
                  </div>
                </div>
              )}

              {client.files?.udhyamRegistrationFileUrl && (
                <div className="document-file-item cursor-pointer hover:bg-gray-50 transition-colors rounded-lg p-3"
                     onClick={() => handleDocumentClick('Udhyam Registration', client.files.udhyamRegistrationFileUrl)}>
                  <div className="flex items-center space-x-3">
                    <DocumentArrowDownIcon className="h-5 w-5 text-gray-600" />
                    <div className="flex-1">
                      <p className="text-sm font-medium text-gray-900">Udhyam Registration Document</p>
                      <p className="text-xs text-gray-500">Click to view</p>
                    </div>
                    <EyeIcon className="h-4 w-4 text-blue-600" />
                  </div>
                </div>
              )}

              {/* Other Documents */}
              {client.files?.othersFileUrls && Array.isArray(client.files.othersFileUrls) && client.files.othersFileUrls.map((docUrl, index) => (
                <div key={index} className="document-file-item cursor-pointer hover:bg-gray-50 transition-colors rounded-lg p-3"
                     onClick={() => handleDocumentClick(`Other Document ${index + 1}`, docUrl)}>
                  <div className="flex items-center space-x-3">
                    <DocumentArrowDownIcon className="h-5 w-5 text-gray-600" />
                    <div className="flex-1">
                      <p className="text-sm font-medium text-gray-900">Other Document {index + 1}</p>
                      <p className="text-xs text-gray-500">Click to view • Downloadable</p>
                    </div>
                    <EyeIcon className="h-4 w-4 text-blue-600" />
                  </div>
                </div>
              ))}

              {/* Show ALL files that exist in client.files */}
              {client.files && Object.keys(client.files).length > 0 && (
                <div className="space-y-3">
                  <h4 className="text-sm font-medium text-gray-900 mb-3">All Uploaded Files:</h4>
                  {Object.entries(client.files).map(([key, value]) => {
                    if (!value) return null;

                    // Handle URL fields
                    if (key.endsWith('Url') && typeof value === 'string') {
                      const displayName = key.replace('Url', '').replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase());
                      return (
                        <div key={key} className="document-file-item cursor-pointer hover:bg-gray-50 transition-colors rounded-lg p-3"
                             onClick={() => handleDocumentClick(displayName, value)}>
                          <div className="flex items-center space-x-3">
                            <DocumentArrowDownIcon className="h-5 w-5 text-blue-600" />
                            <div className="flex-1">
                              <p className="text-sm font-medium text-gray-900">{displayName}</p>
                              <p className="text-xs text-gray-500">Click to view • Downloadable</p>
                            </div>
                            <EyeIcon className="h-4 w-4 text-blue-600" />
                          </div>
                        </div>
                      );
                    }

                    // Handle URL arrays
                    if (key.endsWith('Urls') && Array.isArray(value) && value.length > 0) {
                      const displayName = key.replace('Urls', '').replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase());
                      return value.map((url, index) => (
                        <div key={`${key}_${index}`} className="document-file-item cursor-pointer hover:bg-gray-50 transition-colors rounded-lg p-3"
                             onClick={() => handleDocumentClick(`${displayName} ${index + 1}`, url)}>
                          <div className="flex items-center space-x-3">
                            <DocumentArrowDownIcon className="h-5 w-5 text-gray-600" />
                            <div className="flex-1">
                              <p className="text-sm font-medium text-gray-900">{displayName} {index + 1}</p>
                              <p className="text-xs text-gray-500">Click to view • Downloadable</p>
                            </div>
                            <EyeIcon className="h-4 w-4 text-blue-600" />
                          </div>
                        </div>
                      ));
                    }

                    return null;
                  })}
                </div>
              )}

              {/* No Documents Message */}
              {(!client.files || Object.keys(client.files).length === 0 ||
                Object.values(client.files).every(value => !value || (Array.isArray(value) && value.length === 0))) && (
                <div className="text-center py-8">
                  <DocumentArrowDownIcon className="h-12 w-12 mx-auto mb-3 text-gray-400" />
                  <p className="text-sm text-gray-500 font-medium">No documents uploaded</p>
                  <p className="text-xs text-gray-400 mt-1">Documents will appear here when uploaded</p>

                  {/* DEBUG: Show raw files object */}
                  <details className="mt-4 text-left">
                    <summary className="text-xs text-gray-400 cursor-pointer">Debug: Show files object</summary>
                    <pre className="text-xs mt-2 bg-gray-100 p-2 rounded overflow-auto">
                      {JSON.stringify(client.files, null, 2)}
                    </pre>
                  </details>
                </div>
              )}
            </div>
          </div>

          {/* Business Statistics */}
          <div className="card">
            <h2 className="text-xl font-semibold text-gray-900 mb-6">Business Statistics</h2>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
              <div className="text-center">
                <div className="text-3xl font-bold text-blue-600">{client.totalOrders || 0}</div>
                <div className="text-sm text-gray-500">Total Orders</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-green-600">{client.totalSpent || '₹0'}</div>
                <div className="text-sm text-gray-500">Total Spent</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-purple-600">{client.created_at || client.joinDate || 'N/A'}</div>
                <div className="text-sm text-gray-500">Client Since</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-orange-600">
                  ₹{(() => {
                    const totalSpent = client.totalSpent || '₹0';
                    const totalOrders = client.totalOrders || 1;
                    if (typeof totalSpent === 'string' && totalSpent.includes('₹')) {
                      const amount = parseFloat(totalSpent.replace('₹', '').replace(/,/g, ''));
                      return (amount / totalOrders).toFixed(0);
                    }
                    return '0';
                  })()}
                </div>
                <div className="text-sm text-gray-500">Avg Order Value</div>
              </div>
            </div>
          </div>

          {/* Recent Orders */}
          <div className="card">
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-xl font-semibold text-gray-900">Recent Orders</h2>
              <button 
                onClick={() => navigate('/orders')}
                className="text-blue-600 hover:text-blue-800 text-sm font-medium"
              >
                View All Orders
              </button>
            </div>
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Order ID
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Date
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Amount
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Status
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {(client.recentOrders || []).map((order) => (
                    <tr key={order.id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <button 
                          onClick={() => navigate(`/orders/${order.id}`)}
                          className="text-blue-600 hover:text-blue-800 font-medium"
                        >
                          {order.id}
                        </button>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {order.date}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                        {order.amount}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                          order.status === 'Completed' ? 'bg-green-100 text-green-800' :
                          order.status === 'Processing' ? 'bg-blue-100 text-blue-800' :
                          'bg-yellow-100 text-yellow-800'
                        }`}>
                          {order.status}
                        </span>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Contact History */}
          <div className="card">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Contact History</h3>
            <div className="space-y-4">
              {(client.contactHistory || []).map((contact, index) => (
                <div key={index} className="border-l-4 border-blue-200 pl-4">
                  <div className="flex items-center justify-between">
                    <p className="text-sm font-medium text-gray-900">{contact.type}</p>
                    <p className="text-xs text-gray-500">{contact.date}</p>
                  </div>
                  <p className="text-sm text-gray-600">{contact.subject}</p>
                  <p className="text-xs text-green-600">{contact.status}</p>
                </div>
              ))}
            </div>
          </div>



          {/* Quick Actions */}
          <div className="card">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>
            <div className="space-y-2">
              <button
                onClick={handleSendEmail}
                className="w-full text-left px-3 py-2 text-sm text-gray-700 hover:bg-gray-50 rounded flex items-center space-x-2 transition-colors"
              >
                <EnvelopeIcon className="h-4 w-4" />
                <span>Send Email</span>
              </button>
              <button
                onClick={handleScheduleMeeting}
                className="w-full text-left px-3 py-2 text-sm text-gray-700 hover:bg-gray-50 rounded flex items-center space-x-2 transition-colors"
              >
                <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                </svg>
                <span>Schedule Meeting</span>
              </button>
              <button
                onClick={handleCreateNewOrder}
                className="w-full text-left px-3 py-2 text-sm text-gray-700 hover:bg-gray-50 rounded flex items-center space-x-2 transition-colors"
              >
                <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                </svg>
                <span>Create New Order</span>
              </button>
              <button
                onClick={handleGenerateReport}
                className="w-full text-left px-3 py-2 text-sm text-gray-700 hover:bg-gray-50 rounded flex items-center space-x-2 transition-colors"
              >
                <DocumentArrowDownIcon className="h-4 w-4" />
                <span>Generate Report</span>
              </button>
            </div>
          </div>
        </div>
      </div>
      </div>

      {/* Document Viewer Modal */}
      {showDocumentViewer && selectedDocument && (
        <div className="fixed inset-0 z-50 overflow-y-auto">
          <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            {/* Background overlay */}
            <div
              className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity"
              onClick={closeDocumentViewer}
            ></div>

            {/* Modal panel */}
            <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-4xl sm:w-full">
              {/* Header */}
              <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                <div className="flex items-center justify-between">
                  <h3 className="text-lg leading-6 font-medium text-gray-900">
                    {selectedDocument.name}
                  </h3>
                  <button
                    onClick={closeDocumentViewer}
                    className="bg-white rounded-md text-gray-400 hover:text-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                  >
                    <XMarkIcon className="h-6 w-6" />
                  </button>
                </div>
              </div>

              {/* Document content */}
              <div className="bg-gray-50 px-4 py-3 sm:px-6" style={{ height: '70vh' }}>
                {selectedDocument.url.toLowerCase().includes('.pdf') || selectedDocument.url.toLowerCase().endsWith('.pdf') ? (
                  <iframe
                    src={selectedDocument.url}
                    className="w-full h-full border-0 rounded"
                    title={selectedDocument.name}
                    onError={(e) => {
                      console.error('PDF iframe failed to load:', e);
                      e.target.style.display = 'none';
                      e.target.nextSibling.style.display = 'block';
                    }}
                  />
                ) : selectedDocument.url.match(/\.(jpg|jpeg|png|gif|bmp|webp|svg)$/i) ? (
                  <div className="flex items-center justify-center h-full">
                    <img
                      src={selectedDocument.url}
                      alt={selectedDocument.name}
                      className="max-w-full max-h-full object-contain rounded shadow-lg"
                      onError={(e) => {
                        console.error('Image failed to load:', e);
                        e.target.style.display = 'none';
                        e.target.nextSibling.style.display = 'block';
                      }}
                    />
                    <div className="text-center hidden">
                      <DocumentArrowDownIcon className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                      <p className="text-gray-600 font-medium">Image failed to load</p>
                      <p className="text-sm text-gray-500 mt-2">{selectedDocument.name}</p>
                    </div>
                  </div>
                ) : (
                  <div className="flex items-center justify-center h-full">
                    <div className="text-center">
                      <DocumentArrowDownIcon className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                      <p className="text-gray-600 font-medium">{selectedDocument.name}</p>
                      <p className="text-sm text-gray-500 mt-2">Preview not available for this file type</p>
                      <div className="mt-4 space-x-3">
                        <a
                          href={selectedDocument.url}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
                        >
                          <EyeIcon className="h-4 w-4 mr-2" />
                          Open in New Tab
                        </a>
                        <a
                          href={selectedDocument.url}
                          download
                          className="inline-flex items-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700"
                        >
                          <DocumentArrowDownIcon className="h-4 w-4 mr-2" />
                          Download File
                        </a>
                      </div>
                    </div>
                  </div>
                )}

                {/* Fallback error display for PDF */}
                <div className="hidden flex items-center justify-center h-full">
                  <div className="text-center">
                    <DocumentArrowDownIcon className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                    <p className="text-gray-600 font-medium">Unable to display document</p>
                    <p className="text-sm text-gray-500 mt-2">{selectedDocument.name}</p>
                    <div className="mt-4 space-x-3">
                      <a
                        href={selectedDocument.url}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
                      >
                        <EyeIcon className="h-4 w-4 mr-2" />
                        Open in New Tab
                      </a>
                      <a
                        href={selectedDocument.url}
                        download
                        className="inline-flex items-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700"
                      >
                        <DocumentArrowDownIcon className="h-4 w-4 mr-2" />
                        Download File
                      </a>
                    </div>
                  </div>
                </div>
              </div>

              {/* Footer */}
              <div className="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                <button
                  type="button"
                  onClick={closeDocumentViewer}
                  className="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-blue-600 text-base font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:ml-3 sm:w-auto sm:text-sm"
                >
                  Close
                </button>
                <a
                  href={selectedDocument.url}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
                >
                  Open in New Tab
                </a>
                <a
                  href={selectedDocument.url}
                  download
                  className="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-green-600 text-base font-medium text-white hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
                >
                  Download
                </a>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Email Modal */}
      {showEmailModal && (
        <EmailModal
          client={client}
          onClose={() => setShowEmailModal(false)}
          onSend={handleSendEmailSubmit}
        />
      )}

      {/* Meeting Modal */}
      {showMeetingModal && (
        <MeetingModal
          client={client}
          onClose={() => setShowMeetingModal(false)}
          onSchedule={handleScheduleMeetingSubmit}
        />
      )}

      {/* Report Modal */}
      {showReportModal && (
        <ReportModal
          client={client}
          onClose={() => setShowReportModal(false)}
          onGenerate={handleGenerateReportSubmit}
        />
      )}
    </div>
  );
};

// Email Modal Component
const EmailModal = ({ client, onClose, onSend }) => {
  const [emailData, setEmailData] = useState({
    to: client.emails?.[0] || '',
    subject: `Regarding ${client.companyName}`,
    message: `Dear ${client.contactPerson || 'Sir/Madam'},\n\nI hope this email finds you well.\n\nBest regards,\nYour Company`
  });

  const handleSubmit = (e) => {
    e.preventDefault();
    onSend(emailData);
  };

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" onClick={onClose}></div>

        <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
          <form onSubmit={handleSubmit}>
            <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
              <div className="sm:flex sm:items-start">
                <div className="mt-3 text-center sm:mt-0 sm:text-left w-full">
                  <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
                    Send Email to {client.companyName}
                  </h3>

                  <div className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700">To</label>
                      <input
                        type="email"
                        value={emailData.to}
                        onChange={(e) => setEmailData({...emailData, to: e.target.value})}
                        className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                        required
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700">Subject</label>
                      <input
                        type="text"
                        value={emailData.subject}
                        onChange={(e) => setEmailData({...emailData, subject: e.target.value})}
                        className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                        required
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700">Message</label>
                      <textarea
                        rows={6}
                        value={emailData.message}
                        onChange={(e) => setEmailData({...emailData, message: e.target.value})}
                        className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                        required
                      />
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div className="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
              <button
                type="submit"
                className="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-blue-600 text-base font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:ml-3 sm:w-auto sm:text-sm"
              >
                Send Email
              </button>
              <button
                type="button"
                onClick={onClose}
                className="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
              >
                Cancel
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

// Meeting Modal Component
const MeetingModal = ({ client, onClose, onSchedule }) => {
  const [meetingData, setMeetingData] = useState({
    title: `Meeting with ${client.companyName}`,
    date: new Date().toISOString().split('T')[0],
    time: '10:00',
    duration: 60,
    location: 'Office/Online',
    description: `Meeting with ${client.contactPerson || client.companyName} to discuss business matters.`
  });

  const handleSubmit = (e) => {
    e.preventDefault();
    onSchedule(meetingData);
  };

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" onClick={onClose}></div>

        <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
          <form onSubmit={handleSubmit}>
            <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
              <div className="sm:flex sm:items-start">
                <div className="mt-3 text-center sm:mt-0 sm:text-left w-full">
                  <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
                    Schedule Meeting with {client.companyName}
                  </h3>

                  <div className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Meeting Title</label>
                      <input
                        type="text"
                        value={meetingData.title}
                        onChange={(e) => setMeetingData({...meetingData, title: e.target.value})}
                        className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                        required
                      />
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700">Date</label>
                        <input
                          type="date"
                          value={meetingData.date}
                          onChange={(e) => setMeetingData({...meetingData, date: e.target.value})}
                          className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                          required
                        />
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700">Time</label>
                        <input
                          type="time"
                          value={meetingData.time}
                          onChange={(e) => setMeetingData({...meetingData, time: e.target.value})}
                          className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                          required
                        />
                      </div>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700">Duration (minutes)</label>
                      <select
                        value={meetingData.duration}
                        onChange={(e) => setMeetingData({...meetingData, duration: parseInt(e.target.value)})}
                        className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                      >
                        <option value={30}>30 minutes</option>
                        <option value={60}>1 hour</option>
                        <option value={90}>1.5 hours</option>
                        <option value={120}>2 hours</option>
                      </select>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700">Location</label>
                      <input
                        type="text"
                        value={meetingData.location}
                        onChange={(e) => setMeetingData({...meetingData, location: e.target.value})}
                        className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                        placeholder="Office, Online, or specific address"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700">Description</label>
                      <textarea
                        rows={3}
                        value={meetingData.description}
                        onChange={(e) => setMeetingData({...meetingData, description: e.target.value})}
                        className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                      />
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div className="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
              <button
                type="submit"
                className="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-blue-600 text-base font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:ml-3 sm:w-auto sm:text-sm"
              >
                Schedule Meeting
              </button>
              <button
                type="button"
                onClick={onClose}
                className="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
              >
                Cancel
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

// Report Modal Component
const ReportModal = ({ client, onClose, onGenerate }) => {
  const [reportData, setReportData] = useState({
    type: 'pdf',
    dateRange: 'all',
    includeDocuments: true,
    notes: ''
  });

  const handleSubmit = (e) => {
    e.preventDefault();
    onGenerate(reportData);
  };

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" onClick={onClose}></div>

        <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
          <form onSubmit={handleSubmit}>
            <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
              <div className="sm:flex sm:items-start">
                <div className="mt-3 text-center sm:mt-0 sm:text-left w-full">
                  <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
                    Generate Report for {client.companyName}
                  </h3>

                  <div className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Report Type</label>
                      <select
                        value={reportData.type}
                        onChange={(e) => setReportData({...reportData, type: e.target.value})}
                        className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                      >
                        <option value="pdf">PDF Report</option>
                        <option value="excel">Excel Spreadsheet</option>
                        <option value="summary">Summary Report (Text)</option>
                      </select>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700">Date Range</label>
                      <select
                        value={reportData.dateRange}
                        onChange={(e) => setReportData({...reportData, dateRange: e.target.value})}
                        className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                      >
                        <option value="all">All Time</option>
                        <option value="last30">Last 30 Days</option>
                        <option value="last90">Last 90 Days</option>
                        <option value="thisYear">This Year</option>
                        <option value="lastYear">Last Year</option>
                      </select>
                    </div>

                    <div className="flex items-center">
                      <input
                        type="checkbox"
                        id="includeDocuments"
                        checked={reportData.includeDocuments}
                        onChange={(e) => setReportData({...reportData, includeDocuments: e.target.checked})}
                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                      />
                      <label htmlFor="includeDocuments" className="ml-2 block text-sm text-gray-900">
                        Include document information
                      </label>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700">Additional Notes</label>
                      <textarea
                        rows={3}
                        value={reportData.notes}
                        onChange={(e) => setReportData({...reportData, notes: e.target.value})}
                        className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                        placeholder="Any additional information to include in the report..."
                      />
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div className="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
              <button
                type="submit"
                className="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-blue-600 text-base font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:ml-3 sm:w-auto sm:text-sm"
              >
                Generate Report
              </button>
              <button
                type="button"
                onClick={onClose}
                className="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
              >
                Cancel
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default ClientView;
