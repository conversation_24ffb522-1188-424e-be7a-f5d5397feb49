import { sql } from '../config/database.js';

// Get all service categories
export const getAllServiceCategories = async (includeInactive = false) => {
  try {
    console.log('🔄 Fetching all service categories from database...');

    let query;
    if (includeInactive) {
      query = sql`
        SELECT 
          id,
          name,
          description,
          color,
          "isActive",
          status,
          created_at,
          updated_at
        FROM service_categories 
        ORDER BY name ASC
      `;
    } else {
      query = sql`
        SELECT 
          id,
          name,
          description,
          color,
          "isActive",
          status,
          created_at,
          updated_at
        FROM service_categories 
        WHERE "isActive" = true AND (status IS NULL OR status != 'Inactive')
        ORDER BY name ASC
      `;
    }

    const categories = await query;
    console.log('✅ Service categories fetched successfully:', categories.length);
    
    return categories.map(category => ({
      id: category.id,
      name: category.name,
      description: category.description || '',
      color: category.color || '#6B7280',
      isActive: category.isActive !== false && category.status !== 'Inactive',
      status: category.status || (category.isActive ? 'Active' : 'Inactive'),
      createdAt: category.created_at,
      updatedAt: category.updated_at
    }));

  } catch (error) {
    console.error('❌ Error fetching service categories:', error);
    throw error;
  }
};

// Get active service categories (for dropdowns)
export const getActiveServiceCategories = async () => {
  try {
    console.log('🔄 Fetching active service categories from database...');

    const categories = await sql`
      SELECT 
        id,
        name,
        description,
        color,
        "isActive",
        status
      FROM service_categories 
      WHERE ("isActive" = true OR "isActive" IS NULL)
        AND (status IS NULL OR status != 'Inactive')
      ORDER BY name ASC
    `;

    console.log('✅ Active service categories found in database:', categories.length, 'entries');
    return categories.map(category => ({
      id: category.id,
      name: category.name,
      description: category.description || '',
      color: category.color || '#6B7280',
      isActive: category.isActive !== false && category.status !== 'Inactive'
    }));

  } catch (error) {
    console.error('❌ Error fetching active service categories:', error);
    throw error;
  }
};

// Get service category by ID
export const getServiceCategoryById = async (id) => {
  try {
    console.log('🔄 Fetching service category by ID:', id);

    const categories = await sql`
      SELECT 
        id,
        name,
        description,
        color,
        "isActive",
        status,
        created_at,
        updated_at
      FROM service_categories 
      WHERE id = ${id}
    `;

    if (categories.length === 0) {
      throw new Error('Service category not found');
    }

    const category = categories[0];
    console.log('✅ Service category found:', category.name);

    return {
      id: category.id,
      name: category.name,
      description: category.description || '',
      color: category.color || '#6B7280',
      isActive: category.isActive !== false && category.status !== 'Inactive',
      status: category.status || (category.isActive ? 'Active' : 'Inactive'),
      createdAt: category.created_at,
      updatedAt: category.updated_at
    };

  } catch (error) {
    console.error('❌ Error fetching service category by ID:', error);
    throw error;
  }
};

// Create new service category
export const createServiceCategory = async (categoryData) => {
  try {
    console.log('🔄 Creating new service category:', categoryData);

    const { name, description, color, isActive = true } = categoryData;

    // Check if category name already exists
    const existingCategories = await sql`
      SELECT id FROM service_categories WHERE LOWER(name) = LOWER(${name})
    `;

    if (existingCategories.length > 0) {
      throw new Error('Service category with this name already exists');
    }

    const result = await sql`
      INSERT INTO service_categories (name, description, color, "isActive", status, created_at, updated_at)
      VALUES (
        ${name},
        ${description || null},
        ${color || '#6B7280'},
        ${isActive},
        ${isActive ? 'Active' : 'Inactive'},
        CURRENT_TIMESTAMP,
        CURRENT_TIMESTAMP
      )
      RETURNING id, name, description, color, "isActive", status, created_at, updated_at
    `;

    const newCategory = result[0];
    console.log('✅ Service category created successfully:', newCategory.name);

    return {
      id: newCategory.id,
      name: newCategory.name,
      description: newCategory.description || '',
      color: newCategory.color || '#6B7280',
      isActive: newCategory.isActive,
      status: newCategory.status,
      createdAt: newCategory.created_at,
      updatedAt: newCategory.updated_at
    };

  } catch (error) {
    console.error('❌ Error creating service category:', error);
    throw error;
  }
};

// Update service category
export const updateServiceCategory = async (id, categoryData) => {
  try {
    console.log('🔄 Updating service category:', id, categoryData);

    const { name, description, color, isActive } = categoryData;

    // Check if category exists
    const existingCategories = await sql`
      SELECT id FROM service_categories WHERE id = ${id}
    `;

    if (existingCategories.length === 0) {
      throw new Error('Service category not found');
    }

    // Check if name is being changed and if new name already exists
    if (name) {
      const duplicateCategories = await sql`
        SELECT id FROM service_categories WHERE LOWER(name) = LOWER(${name}) AND id != ${id}
      `;

      if (duplicateCategories.length > 0) {
        throw new Error('Service category with this name already exists');
      }
    }

    const result = await sql`
      UPDATE service_categories 
      SET 
        name = COALESCE(${name}, name),
        description = COALESCE(${description}, description),
        color = COALESCE(${color}, color),
        "isActive" = COALESCE(${isActive}, "isActive"),
        status = COALESCE(${isActive !== undefined ? (isActive ? 'Active' : 'Inactive') : null}, status),
        updated_at = CURRENT_TIMESTAMP
      WHERE id = ${id}
      RETURNING id, name, description, color, "isActive", status, created_at, updated_at
    `;

    const updatedCategory = result[0];
    console.log('✅ Service category updated successfully:', updatedCategory.name);

    return {
      id: updatedCategory.id,
      name: updatedCategory.name,
      description: updatedCategory.description || '',
      color: updatedCategory.color || '#6B7280',
      isActive: updatedCategory.isActive,
      status: updatedCategory.status,
      createdAt: updatedCategory.created_at,
      updatedAt: updatedCategory.updated_at
    };

  } catch (error) {
    console.error('❌ Error updating service category:', error);
    throw error;
  }
};

// Delete service category (soft delete)
export const deleteServiceCategory = async (id) => {
  try {
    console.log('🔄 Deleting service category with ID:', id);

    // Validate ID
    if (!id || isNaN(parseInt(id))) {
      throw new Error('Invalid service category ID provided');
    }

    // Check if category exists
    const existingCategories = await sql`
      SELECT id, name, "isActive", status FROM service_categories WHERE id = ${parseInt(id)}
    `;

    if (existingCategories.length === 0) {
      throw new Error('Service category not found');
    }

    const category = existingCategories[0];
    console.log('📋 Found service category to delete:', category);

    // Check if category is being used by any services
    const servicesUsingCategory = await sql`
      SELECT COUNT(*) as count FROM services WHERE category_id = ${parseInt(id)}
    `;

    if (servicesUsingCategory[0].count > 0) {
      throw new Error(`Cannot delete category "${category.name}" because it is being used by ${servicesUsingCategory[0].count} service(s)`);
    }

    // Soft delete by setting isActive to false
    const updateResult = await sql`
      UPDATE service_categories 
      SET 
        "isActive" = false,
        status = 'Inactive',
        updated_at = CURRENT_TIMESTAMP
      WHERE id = ${parseInt(id)}
      RETURNING id, name, "isActive", status
    `;

    console.log('✅ Service category updated:', updateResult[0]);
    console.log('✅ Service category deleted successfully:', category.name);
    
    return { 
      success: true, 
      message: 'Service category deleted successfully',
      category: updateResult[0]
    };

  } catch (error) {
    console.error('❌ Error deleting service category:', error);
    throw error;
  }
};

// Hard delete service category (permanently remove from database)
export const hardDeleteServiceCategory = async (id) => {
  try {
    console.log('🔄 Hard deleting service category with ID:', id);

    // Validate ID
    if (!id || isNaN(parseInt(id))) {
      throw new Error('Invalid service category ID provided');
    }

    // Check if category exists
    const existingCategories = await sql`
      SELECT id, name FROM service_categories WHERE id = ${parseInt(id)}
    `;

    if (existingCategories.length === 0) {
      throw new Error('Service category not found');
    }

    const category = existingCategories[0];
    console.log('📋 Found service category to hard delete:', category);

    // Check if category is being used by any services
    const servicesUsingCategory = await sql`
      SELECT COUNT(*) as count FROM services WHERE category_id = ${parseInt(id)}
    `;

    if (servicesUsingCategory[0].count > 0) {
      throw new Error(`Cannot permanently delete category "${category.name}" because it is being used by ${servicesUsingCategory[0].count} service(s)`);
    }

    // Permanently delete from database
    const deleteResult = await sql`
      DELETE FROM service_categories WHERE id = ${parseInt(id)}
      RETURNING id, name
    `;

    console.log('✅ Service category permanently deleted:', deleteResult[0]);
    
    return { 
      success: true, 
      message: 'Service category permanently deleted',
      category: deleteResult[0]
    };

  } catch (error) {
    console.error('❌ Error hard deleting service category:', error);
    throw error;
  }
};

// Search service categories
export const searchServiceCategories = async (searchTerm) => {
  try {
    const categories = await sql`
      SELECT
        id,
        name,
        description,
        color,
        "isActive",
        status,
        created_at,
        updated_at
      FROM service_categories
      WHERE
        name ILIKE ${`%${searchTerm}%`} OR
        description ILIKE ${`%${searchTerm}%`}
      ORDER BY created_at DESC
    `;
    return categories;
  } catch (error) {
    console.error('Error searching service categories:', error);
    throw error;
  }
};
