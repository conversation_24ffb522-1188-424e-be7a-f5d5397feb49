/**
 * Test Type of Work Fixes
 * Tests the fixes for description overflow, PDF export, and updated date column
 */

/**
 * Test description column overflow fix
 */
export const testDescriptionOverflow = () => {
  try {
    console.log('🧪 Testing Description Column Overflow Fix...');
    console.log('');

    // Check if we're on the Type of Work page
    const currentPath = window.location.pathname;
    if (!currentPath.includes('type-of-work')) {
      console.log('⚠️ Please navigate to the Type of Work page first');
      return { success: false, message: 'Not on Type of Work page' };
    }

    // Check for description cells
    const descriptionCells = document.querySelectorAll('[class*="max-w-md"]');
    console.log(`📋 Found ${descriptionCells.length} description cells with proper width constraints`);

    // Check for truncate class
    const truncatedCells = document.querySelectorAll('[class*="truncate"]');
    console.log(`✂️ Found ${truncatedCells.length} cells with truncate styling`);

    // Check for title attributes (tooltips)
    const cellsWithTooltips = document.querySelectorAll('[title]');
    console.log(`💬 Found ${cellsWithTooltips.length} cells with tooltip functionality`);

    console.log('');
    console.log('✅ Description overflow fix verification:');
    console.log(`   - Width constraint: ${descriptionCells.length > 0 ? 'APPLIED' : 'MISSING'}`);
    console.log(`   - Text truncation: ${truncatedCells.length > 0 ? 'APPLIED' : 'MISSING'}`);
    console.log(`   - Hover tooltips: ${cellsWithTooltips.length > 0 ? 'APPLIED' : 'MISSING'}`);

    return {
      success: true,
      results: {
        widthConstraint: descriptionCells.length > 0,
        textTruncation: truncatedCells.length > 0,
        hoverTooltips: cellsWithTooltips.length > 0
      },
      message: 'Description overflow fix tested'
    };

  } catch (error) {
    console.error('💥 Description overflow test failed:', error);
    return { success: false, error: error.message };
  }
};

/**
 * Test updated date column
 */
export const testUpdatedDateColumn = () => {
  try {
    console.log('🧪 Testing Updated Date Column...');
    console.log('');

    // Check if we're on the Type of Work page
    const currentPath = window.location.pathname;
    if (!currentPath.includes('type-of-work')) {
      console.log('⚠️ Please navigate to the Type of Work page first');
      return { success: false, message: 'Not on Type of Work page' };
    }

    // Check for Updated Date column header
    const headers = document.querySelectorAll('th');
    let updatedDateHeader = null;
    
    headers.forEach(header => {
      if (header.textContent.includes('Updated Date') || header.textContent.includes('Updated At')) {
        updatedDateHeader = header;
      }
    });

    console.log(`📋 Updated Date column header: ${updatedDateHeader ? 'FOUND' : 'MISSING'}`);

    // Check for updated date data cells
    const tableCells = document.querySelectorAll('td');
    let updatedDateCells = 0;
    
    tableCells.forEach(cell => {
      const text = cell.textContent.trim();
      if (text.includes('/') && text.length >= 8 || text === 'N/A') {
        // Looks like a date or N/A
        updatedDateCells++;
      }
    });

    console.log(`📅 Updated date data cells: ${updatedDateCells}`);

    console.log('');
    console.log('✅ Updated Date column verification:');
    console.log(`   - Column header: ${updatedDateHeader ? 'PRESENT' : 'MISSING'}`);
    console.log(`   - Data cells: ${updatedDateCells > 0 ? 'PRESENT' : 'MISSING'}`);

    return {
      success: true,
      results: {
        columnHeader: !!updatedDateHeader,
        dataCells: updatedDateCells > 0,
        cellCount: updatedDateCells
      },
      message: 'Updated Date column tested'
    };

  } catch (error) {
    console.error('💥 Updated Date column test failed:', error);
    return { success: false, error: error.message };
  }
};

/**
 * Test PDF export functionality
 */
export const testPDFExport = () => {
  try {
    console.log('🧪 Testing PDF Export Functionality...');
    console.log('');

    // Check if we're on the Type of Work page
    const currentPath = window.location.pathname;
    if (!currentPath.includes('type-of-work')) {
      console.log('⚠️ Please navigate to the Type of Work page first');
      return { success: false, message: 'Not on Type of Work page' };
    }

    // Check for Export button
    const exportButton = document.querySelector('button[class*="btn-primary"]');
    let exportButtonFound = false;
    
    if (exportButton && exportButton.textContent.includes('Export')) {
      exportButtonFound = true;
      console.log('✅ Export button found');
    } else {
      console.log('❌ Export button not found');
    }

    // Check if jsPDF is available
    const jsPDFAvailable = typeof window.jsPDF !== 'undefined' || 
                          document.querySelector('script[src*="jspdf"]') !== null;
    
    console.log(`📄 jsPDF library: ${jsPDFAvailable ? 'AVAILABLE' : 'CHECKING...'}`);

    // Check for ExportModal component (by looking for modal-related classes)
    const modalElements = document.querySelectorAll('[class*="modal"], [class*="fixed"], [class*="z-50"]');
    console.log(`🔧 Modal elements available: ${modalElements.length}`);

    console.log('');
    console.log('✅ PDF Export functionality verification:');
    console.log(`   - Export button: ${exportButtonFound ? 'PRESENT' : 'MISSING'}`);
    console.log(`   - PDF library: ${jsPDFAvailable ? 'LOADED' : 'CHECKING'}`);
    console.log(`   - Modal system: ${modalElements.length > 0 ? 'AVAILABLE' : 'LIMITED'}`);
    console.log('');
    console.log('📝 To test PDF export:');
    console.log('   1. Click the "Export" button');
    console.log('   2. Select "PDF" format');
    console.log('   3. Choose columns to include');
    console.log('   4. Click "Export" to download PDF');

    return {
      success: true,
      results: {
        exportButton: exportButtonFound,
        pdfLibrary: jsPDFAvailable,
        modalSystem: modalElements.length > 0
      },
      message: 'PDF export functionality tested'
    };

  } catch (error) {
    console.error('💥 PDF export test failed:', error);
    return { success: false, error: error.message };
  }
};

/**
 * Run all Type of Work fixes tests
 */
export const testAllTypeOfWorkFixes = () => {
  try {
    console.log('🚀 TESTING ALL TYPE OF WORK FIXES...');
    console.log('');

    // Test 1: Description overflow
    const descriptionTest = testDescriptionOverflow();
    console.log('');

    // Test 2: Updated date column
    const updatedDateTest = testUpdatedDateColumn();
    console.log('');

    // Test 3: PDF export
    const pdfExportTest = testPDFExport();
    console.log('');

    // Summary
    console.log('📊 SUMMARY OF ALL TESTS:');
    console.log('');
    console.log('1️⃣ Description Overflow Fix:');
    console.log(`   Status: ${descriptionTest.success ? '✅ PASSED' : '❌ FAILED'}`);
    if (descriptionTest.results) {
      console.log(`   - Width constraint: ${descriptionTest.results.widthConstraint ? '✅' : '❌'}`);
      console.log(`   - Text truncation: ${descriptionTest.results.textTruncation ? '✅' : '❌'}`);
      console.log(`   - Hover tooltips: ${descriptionTest.results.hoverTooltips ? '✅' : '❌'}`);
    }

    console.log('');
    console.log('2️⃣ Updated Date Column:');
    console.log(`   Status: ${updatedDateTest.success ? '✅ PASSED' : '❌ FAILED'}`);
    if (updatedDateTest.results) {
      console.log(`   - Column header: ${updatedDateTest.results.columnHeader ? '✅' : '❌'}`);
      console.log(`   - Data cells: ${updatedDateTest.results.dataCells ? '✅' : '❌'}`);
    }

    console.log('');
    console.log('3️⃣ PDF Export Functionality:');
    console.log(`   Status: ${pdfExportTest.success ? '✅ PASSED' : '❌ FAILED'}`);
    if (pdfExportTest.results) {
      console.log(`   - Export button: ${pdfExportTest.results.exportButton ? '✅' : '❌'}`);
      console.log(`   - PDF library: ${pdfExportTest.results.pdfLibrary ? '✅' : '❌'}`);
      console.log(`   - Modal system: ${pdfExportTest.results.modalSystem ? '✅' : '❌'}`);
    }

    console.log('');
    console.log('🎉 ALL TYPE OF WORK FIXES TESTED!');

    return {
      success: true,
      tests: {
        descriptionOverflow: descriptionTest,
        updatedDateColumn: updatedDateTest,
        pdfExport: pdfExportTest
      },
      message: 'All Type of Work fixes tested successfully'
    };

  } catch (error) {
    console.error('💥 All tests failed:', error);
    return { success: false, error: error.message };
  }
};

// Make functions available globally
if (typeof window !== 'undefined') {
  window.testDescriptionOverflow = testDescriptionOverflow;
  window.testUpdatedDateColumn = testUpdatedDateColumn;
  window.testPDFExport = testPDFExport;
  window.testAllTypeOfWorkFixes = testAllTypeOfWorkFixes;
  
  console.log('🧪 Type of Work Fixes Test Functions Available:');
  console.log('- window.testDescriptionOverflow() - Test description column fix');
  console.log('- window.testUpdatedDateColumn() - Test updated date column');
  console.log('- window.testPDFExport() - Test PDF export functionality');
  console.log('- window.testAllTypeOfWorkFixes() - Run all tests');
}
