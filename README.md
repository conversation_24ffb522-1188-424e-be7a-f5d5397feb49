# Innoventory Admin Panel

A comprehensive admin panel for Innoventory with multi-role authentication, permission management, and complete business workflow management.

## 🚀 Features

### 🔐 Authentication & User Management
- **Multi-role authentication** (ADMIN/SUB_ADMIN)
- **Sub-admin creation** with custom permission assignment
- **Role-based access control** throughout the application
- **Session management** with secure token handling
- **Password hashing** with bcryptjs

### 📊 Dashboard & Analytics
- **Interactive Charts**: Revenue trends, user distribution, order status with Recharts
- **Real-time Statistics**: Clickable widgets showing key metrics
- **Dynamic data** from database instead of static content
- **Responsive Design**: Optimized for all screen sizes
- **Modern UI**: Gradient backgrounds, animations, and hover effects

### 👥 Business Management
- **Customer Management**: Complete CRUD operations with file uploads
- **Vendor Management**: Vendor onboarding and document management
- **Order Management**: 3-step order creation process (Customer → Vendor → Order)
- **Type of Work**: Configurable work categories
- **Sub-admin Management**: Role-based access control and permissions
- **Audit Logs**: Track portal usage and user activity

### 🔑 Permission System
- **Granular permissions** based on database enum
- **Permission assignment** during sub-admin creation
- **Quick permission presets** (Basic, Standard, Full Access)
- **Permission overview** dashboard for admins

### 📋 Data Management
- **Advanced Tables**: Sorting, filtering, pagination (50 rows default)
- **Export Functionality**: PDF and Excel export with column customization
- **Column Toggle**: Show/hide columns as needed
- **Search & Filter**: Multi-column filtering capabilities

### 📁 File Upload System
- **Multiple File Support**: No limit on number of files
- **File Size Limit**: 10MB maximum per file
- **Supported Formats**: PDF, Images, Word, Excel, PowerPoint
- **Duplicate Handling**: Automatic timestamp appending
- **Drag & Drop**: Intuitive file upload interface

### 🎨 Modern UI/UX
- **Responsive Design**: Mobile-first approach
- **Animations**: Smooth transitions and micro-interactions
- **Professional Styling**: Modern gradients and shadows
- **Accessibility**: WCAG compliant design

## 🛠️ Tech Stack

- **Frontend**: React.js 19.1.0
- **Styling**: Tailwind CSS
- **Routing**: React Router DOM
- **Charts**: Recharts
- **Icons**: Heroicons
- **File Export**: jsPDF, xlsx
- **Build Tool**: Vite

## 📦 Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd Innoventory-AdminPannel
   ```

2. **Install dependencies**
   ```bash
   npm install --legacy-peer-deps
   ```

3. **Start development server**
   ```bash
   npm run dev
   ```

4. **Open in browser**
   ```
   http://localhost:5173
   ```

## 🎯 Key Features

✅ **Dashboard with Interactive Charts**
✅ **Advanced Data Tables with Export**
✅ **File Upload System (10MB, Multiple Formats)**
✅ **Responsive Design**
✅ **Modern Animations & Styling**
✅ **Vendor/Client/Order Management**
✅ **Audit & Analytics**
✅ **Settings & Configuration**

## 📱 Responsive Design

- **Mobile**: Optimized for phones (320px+)
- **Tablet**: Enhanced for tablets (768px+)
- **Desktop**: Full features for desktop (1024px+)

## 🔐 Default Login Credentials

### Main Admin
- **Email:** <EMAIL>
- **Password:** admin123
- **Role:** ADMIN (Full access)

### Demo Sub-Admin
- **Email:** <EMAIL>
- **Password:** subadmin123
- **Role:** SUB_ADMIN (Restricted access)

## 🔧 Available Permissions

- **MANAGE_USERS** - Create and manage sub-admin users
- **MANAGE_CUSTOMERS** - Customer record management
- **MANAGE_VENDORS** - Vendor record management
- **MANAGE_ORDERS** - Order creation and management
- **VIEW_ANALYTICS** - Dashboard analytics access
- **MANAGE_PAYMENTS** - Payment processing
- **VIEW_REPORTS** - System reports access

## ⚙️ Environment Setup

Create a `.env` file in the root directory:
```env
VITE_DATABASE_URL=your_neon_database_url
VITE_AWS_ACCESS_KEY_ID=your_aws_access_key
VITE_AWS_SECRET_ACCESS_KEY=your_aws_secret_key
VITE_AWS_REGION=us-east-1
VITE_AWS_BUCKET_NAME=innoventory3solutions
```

## 🚀 Quick Start

1. **Clone and install**
```bash
git clone https://github.com/yourusername/innoventory-admin-panel.git
cd innoventory-admin-panel
npm install
```

2. **Setup database**
```bash
npx prisma generate
npx prisma db push
node scripts/create-admin-users.js
```

3. **Start development**
```bash
npm run dev
```

The application runs at: `http://localhost:5173`

Navigate through different sections:
- `/login` - Authentication page
- `/dashboard` - Main dashboard with charts
- `/vendors` - Vendor management with file uploads
- `/clients` - Client management
- `/orders` - Order tracking and 3-step creation
- `/sub-admins` - User management (admin only)
- `/audit` - Usage analytics
- `/settings` - Configuration

---

**Built with ❤️ for Innoventory - Complete business management solution**
