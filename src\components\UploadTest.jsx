import { useState } from 'react';
import { uploadFileToS3, validateFile } from '../services/s3Service';
import { getDebugInfo } from '../config/uploadConfig';

const UploadTest = () => {
  const [selectedFile, setSelectedFile] = useState(null);
  const [uploading, setUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [result, setResult] = useState(null);
  const [error, setError] = useState(null);

  const handleFileSelect = (event) => {
    const file = event.target.files[0];
    if (file) {
      setSelectedFile(file);
      setResult(null);
      setError(null);
      
      // Validate file
      const validation = validateFile(file, 'all');
      if (!validation.isValid) {
        setError(`File validation failed: ${validation.errors.join(', ')}`);
      }
    }
  };

  const handleUpload = async () => {
    if (!selectedFile) {
      setError('Please select a file first');
      return;
    }

    setUploading(true);
    setUploadProgress(0);
    setError(null);
    setResult(null);

    try {
      console.log('🧪 Starting upload test with debug info:', getDebugInfo());
      
      const uploadResult = await uploadFileToS3(
        selectedFile,
        'test',
        `test-${Date.now()}`,
        'document',
        'all',
        (progress) => setUploadProgress(progress)
      );

      setResult(uploadResult);
      console.log('✅ Upload test successful:', uploadResult);
      
    } catch (uploadError) {
      setError(`Upload failed: ${uploadError.message}`);
      console.error('❌ Upload test failed:', uploadError);
    } finally {
      setUploading(false);
    }
  };

  const debugInfo = getDebugInfo();

  return (
    <div className="max-w-2xl mx-auto p-6 bg-white rounded-lg shadow-lg">
      <h2 className="text-2xl font-bold text-gray-900 mb-6">🧪 Upload Test</h2>
      
      {/* Debug Information */}
      <div className="mb-6 p-4 bg-gray-100 rounded-lg">
        <h3 className="text-lg font-semibold mb-2">Debug Information</h3>
        <div className="text-sm space-y-1">
          <p><strong>Environment:</strong> {debugInfo.environment}</p>
          <p><strong>Upload Endpoint:</strong> {debugInfo.apiEndpoints.upload}</p>
          <p><strong>S3 Bucket:</strong> {debugInfo.s3Config.bucket}</p>
          <p><strong>S3 Region:</strong> {debugInfo.s3Config.region}</p>
          <p><strong>Max File Size:</strong> {debugInfo.uploadConfig.maxFileSize / 1024 / 1024}MB</p>
        </div>
      </div>

      {/* File Selection */}
      <div className="mb-6">
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Select File to Test Upload
        </label>
        <input
          type="file"
          onChange={handleFileSelect}
          className="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"
          accept=".pdf,.jpg,.jpeg,.png,.gif,.doc,.docx,.xls,.xlsx"
        />
        {selectedFile && (
          <p className="mt-2 text-sm text-gray-600">
            Selected: {selectedFile.name} ({(selectedFile.size / 1024 / 1024).toFixed(2)} MB)
          </p>
        )}
      </div>

      {/* Upload Button */}
      <div className="mb-6">
        <button
          onClick={handleUpload}
          disabled={!selectedFile || uploading}
          className={`w-full py-2 px-4 rounded-lg font-semibold ${
            !selectedFile || uploading
              ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
              : 'bg-blue-600 text-white hover:bg-blue-700'
          }`}
        >
          {uploading ? `Uploading... ${uploadProgress}%` : 'Test Upload'}
        </button>
      </div>

      {/* Progress Bar */}
      {uploading && (
        <div className="mb-6">
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div
              className="bg-blue-600 h-2 rounded-full transition-all duration-300"
              style={{ width: `${uploadProgress}%` }}
            ></div>
          </div>
        </div>
      )}

      {/* Error Display */}
      {error && (
        <div className="mb-6 p-4 bg-red-100 border border-red-400 text-red-700 rounded-lg">
          <h4 className="font-semibold">Error:</h4>
          <p>{error}</p>
        </div>
      )}

      {/* Success Result */}
      {result && (
        <div className="mb-6 p-4 bg-green-100 border border-green-400 text-green-700 rounded-lg">
          <h4 className="font-semibold mb-2">Upload Successful! ✅</h4>
          <div className="text-sm space-y-1">
            <p><strong>File Key:</strong> {result.key}</p>
            <p><strong>File URL:</strong> 
              <a 
                href={result.url} 
                target="_blank" 
                rel="noopener noreferrer"
                className="text-blue-600 hover:text-blue-800 ml-1"
              >
                {result.url}
              </a>
            </p>
            <p><strong>File Size:</strong> {(result.size / 1024 / 1024).toFixed(2)} MB</p>
            <p><strong>File Type:</strong> {result.type}</p>
          </div>
          
          {/* Test File Access */}
          <div className="mt-4">
            <button
              onClick={() => window.open(result.url, '_blank')}
              className="bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700"
            >
              🔗 Open File in New Tab
            </button>
          </div>
        </div>
      )}

      {/* Instructions */}
      <div className="p-4 bg-blue-100 border border-blue-400 text-blue-700 rounded-lg">
        <h4 className="font-semibold mb-2">Test Instructions:</h4>
        <ol className="text-sm list-decimal list-inside space-y-1">
          <li>Select a file (PDF, image, or Office document)</li>
          <li>Click "Test Upload" to upload to S3</li>
          <li>Check the console for detailed logs</li>
          <li>If successful, try opening the file link</li>
          <li>Report any errors for debugging</li>
        </ol>
      </div>
    </div>
  );
};

export default UploadTest;
