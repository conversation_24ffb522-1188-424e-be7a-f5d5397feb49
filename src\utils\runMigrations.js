/**
 * Manual migration runner for startup_benefits column
 * This can be run independently to fix the database schema
 */

import { sql } from '../config/database.js';

/**
 * Add startup_benefits column to vendors table
 */
export const addStartupBenefitsColumn = async () => {
  try {
    console.log('🔄 Checking startup_benefits column...');

    // Check if column exists
    const columnCheck = await sql`
      SELECT column_name, data_type, column_default
      FROM information_schema.columns 
      WHERE table_name = 'vendors' AND column_name = 'startup_benefits'
    `;

    if (columnCheck.length > 0) {
      console.log('✅ startup_benefits column already exists:', columnCheck[0]);
      return { success: true, message: 'Column already exists', existing: true };
    }

    // Add the column
    console.log('🔄 Adding startup_benefits column to vendors table...');
    await sql`
      ALTER TABLE vendors 
      ADD COLUMN startup_benefits VARCHAR(10) DEFAULT 'No'
    `;

    console.log('✅ startup_benefits column added successfully');

    // Verify the column was added
    const verifyCheck = await sql`
      SELECT column_name, data_type, column_default
      FROM information_schema.columns 
      WHERE table_name = 'vendors' AND column_name = 'startup_benefits'
    `;

    if (verifyCheck.length > 0) {
      console.log('✅ Column verification successful:', verifyCheck[0]);
      return { 
        success: true, 
        message: 'Column added successfully',
        columnInfo: verifyCheck[0]
      };
    } else {
      throw new Error('Column was not created properly');
    }

  } catch (error) {
    console.error('❌ Error adding startup_benefits column:', error);
    return { 
      success: false, 
      error: error.message 
    };
  }
};

/**
 * Add point_of_contact column to vendors table
 */
export const addPointOfContactColumn = async () => {
  try {
    console.log('🔄 Checking point_of_contact column...');

    // Check if column exists
    const columnCheck = await sql`
      SELECT column_name, data_type
      FROM information_schema.columns 
      WHERE table_name = 'vendors' AND column_name = 'point_of_contact'
    `;

    if (columnCheck.length > 0) {
      console.log('✅ point_of_contact column already exists:', columnCheck[0]);
      return { success: true, message: 'Column already exists', existing: true };
    }

    // Add the column
    console.log('🔄 Adding point_of_contact column to vendors table...');
    await sql`
      ALTER TABLE vendors 
      ADD COLUMN point_of_contact JSONB DEFAULT '[]'
    `;

    console.log('✅ point_of_contact column added successfully');
    return { success: true, message: 'Column added successfully' };

  } catch (error) {
    console.error('❌ Error adding point_of_contact column:', error);
    return { 
      success: false, 
      error: error.message 
    };
  }
};

/**
 * Run all necessary migrations
 */
export const runAllMigrations = async () => {
  try {
    console.log('🚀 Starting database migrations...');

    const results = {
      startupBenefits: await addStartupBenefitsColumn(),
      pointOfContact: await addPointOfContactColumn()
    };

    const allSuccess = Object.values(results).every(result => result.success);

    if (allSuccess) {
      console.log('🎉 All migrations completed successfully!');
      console.log('✅ You can now:');
      console.log('   - Create vendors with startup benefits');
      console.log('   - Add point of contact information');
      console.log('   - Use all vendor form features');
    } else {
      console.log('⚠️ Some migrations failed:', results);
    }

    return { success: allSuccess, results };

  } catch (error) {
    console.error('❌ Migration runner failed:', error);
    return { success: false, error: error.message };
  }
};

/**
 * Test the migrations by checking vendor table structure
 */
export const testMigrations = async () => {
  try {
    console.log('🧪 Testing migrations...');

    // Check vendor table structure
    const columns = await sql`
      SELECT column_name, data_type, column_default, is_nullable
      FROM information_schema.columns 
      WHERE table_name = 'vendors'
      AND column_name IN ('startup_benefits', 'point_of_contact')
      ORDER BY column_name
    `;

    console.log('📊 Vendor table columns:', columns);

    // Test reading vendors
    const vendorCount = await sql`SELECT COUNT(*) as count FROM vendors`;
    console.log(`📊 Total vendors: ${vendorCount[0].count}`);

    if (vendorCount[0].count > 0) {
      // Test reading with new columns
      const sampleVendors = await sql`
        SELECT id, company_name, startup_benefits, point_of_contact 
        FROM vendors 
        LIMIT 3
      `;
      console.log('📋 Sample vendor data:', sampleVendors);
    }

    console.log('✅ Migration test completed successfully');
    return { success: true, columns, vendorCount: vendorCount[0].count };

  } catch (error) {
    console.error('❌ Migration test failed:', error);
    return { success: false, error: error.message };
  }
};

/**
 * Emergency fix function that can be run in browser console
 */
export const emergencyDatabaseFix = async () => {
  try {
    console.log('🚨 EMERGENCY DATABASE FIX STARTING...');
    console.log('');

    // Import database connection
    const { sql } = await import('../config/database.js');

    // Step 1: Test connection
    console.log('1️⃣ Testing database connection...');
    const connectionTest = await sql`SELECT NOW() as current_time`;
    console.log('✅ Database connection successful:', connectionTest[0].current_time);

    // Step 2: Check if startup_benefits column exists
    console.log('2️⃣ Checking for startup_benefits column...');
    const columnCheck = await sql`
      SELECT column_name, data_type, column_default
      FROM information_schema.columns
      WHERE table_name = 'vendors' AND column_name = 'startup_benefits'
    `;

    if (columnCheck.length > 0) {
      console.log('✅ startup_benefits column already exists:', columnCheck[0]);
    } else {
      console.log('❌ startup_benefits column is missing!');

      // Add the column
      console.log('3️⃣ Adding startup_benefits column...');
      await sql`
        ALTER TABLE vendors
        ADD COLUMN startup_benefits VARCHAR(10) DEFAULT 'No'
      `;
      console.log('✅ startup_benefits column added successfully!');
    }

    // Step 3: Verify the fix
    console.log('4️⃣ Verifying the fix...');
    const verifyCheck = await sql`
      SELECT column_name, data_type, column_default
      FROM information_schema.columns
      WHERE table_name = 'vendors' AND column_name = 'startup_benefits'
    `;

    if (verifyCheck.length > 0) {
      console.log('✅ Column verification successful:', verifyCheck[0]);
    }

    // Step 4: Test vendor table
    console.log('5️⃣ Testing vendor table...');
    const vendorCount = await sql`SELECT COUNT(*) as count FROM vendors`;
    console.log(`📊 Current vendor count: ${vendorCount[0].count}`);

    console.log('');
    console.log('🎉 EMERGENCY FIX COMPLETED SUCCESSFULLY!');
    console.log('✅ You can now create vendors with startup benefits!');
    console.log('');

    return { success: true, message: 'Database fixed successfully!' };

  } catch (error) {
    console.error('💥 EMERGENCY FIX FAILED!');
    console.error('❌ Error:', error);
    return { success: false, error: error.message };
  }
};

// Make functions available globally for debugging
if (typeof window !== 'undefined' && process.env.NODE_ENV === 'development') {
  window.addStartupBenefitsColumn = addStartupBenefitsColumn;
  window.addPointOfContactColumn = addPointOfContactColumn;
  window.runAllMigrations = runAllMigrations;
  window.testMigrations = testMigrations;
  window.emergencyDatabaseFix = emergencyDatabaseFix;

  console.log('🔧 Migration functions available:');
  console.log('- window.addStartupBenefitsColumn() - Add startup benefits column');
  console.log('- window.addPointOfContactColumn() - Add point of contact column');
  console.log('- window.runAllMigrations() - Run all migrations');
  console.log('- window.testMigrations() - Test migration results');
  console.log('- window.emergencyDatabaseFix() - EMERGENCY FIX FOR STARTUP BENEFITS');
}
