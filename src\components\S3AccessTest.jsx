import { useState } from 'react';
import { testS3FileAccess, generateS3DebugReport } from '../utils/s3Debug';
import { getSignedFileUrl } from '../services/s3Service';

const S3AccessTest = () => {
  const [testUrl, setTestUrl] = useState('');
  const [testResults, setTestResults] = useState(null);
  const [loading, setLoading] = useState(false);
  const [debugReport, setDebugReport] = useState('');

  const handleTest = async () => {
    if (!testUrl.trim()) {
      alert('Please enter a file URL to test');
      return;
    }

    setLoading(true);
    setTestResults(null);
    setDebugReport('');

    try {
      console.log('🧪 Testing S3 file access for:', testUrl);
      
      // Test direct access
      const results = await testS3FileAccess(testUrl);
      setTestResults(results);
      
      // Generate debug report
      const report = await generateS3DebugReport(testUrl);
      setDebugReport(report);
      
      console.log('📊 Test results:', results);
      
    } catch (error) {
      console.error('❌ Test failed:', error);
      setTestResults({
        url: testUrl,
        accessible: false,
        error: error.message,
        suggestions: ['Test failed due to an unexpected error']
      });
    } finally {
      setLoading(false);
    }
  };

  const testSignedUrl = async () => {
    if (!testUrl.trim()) {
      alert('Please enter an S3 URL to test signed URL generation');
      return;
    }

    setLoading(true);
    
    try {
      // Extract S3 key from URL
      const { extractS3Key } = await import('../utils/s3Debug');
      const s3Key = extractS3Key(testUrl);
      
      if (!s3Key) {
        alert('Could not extract S3 key from URL. Please ensure it\'s a valid S3 URL.');
        return;
      }
      
      console.log('🔑 Extracted S3 key:', s3Key);
      
      // Generate signed URL
      const signedUrl = await getSignedFileUrl(s3Key);
      console.log('🔗 Generated signed URL:', signedUrl);
      
      // Test the signed URL
      const signedResults = await testS3FileAccess(signedUrl);
      
      setTestResults({
        ...signedResults,
        signedUrl,
        originalUrl: testUrl,
        s3Key
      });
      
      // Generate debug report for signed URL
      const report = await generateS3DebugReport(signedUrl);
      setDebugReport(report);
      
    } catch (error) {
      console.error('❌ Signed URL test failed:', error);
      alert(`Signed URL test failed: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="max-w-4xl mx-auto p-6 bg-white rounded-lg shadow-lg">
      <h2 className="text-2xl font-bold text-gray-900 mb-6">S3 File Access Test</h2>
      
      <div className="space-y-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            S3 File URL to Test
          </label>
          <input
            type="url"
            value={testUrl}
            onChange={(e) => setTestUrl(e.target.value)}
            placeholder="https://innoventory3solutions.s3.us-east-1.amazonaws.com/..."
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>
        
        <div className="flex space-x-4">
          <button
            onClick={handleTest}
            disabled={loading}
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50"
          >
            {loading ? 'Testing...' : 'Test Direct Access'}
          </button>
          
          <button
            onClick={testSignedUrl}
            disabled={loading}
            className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:opacity-50"
          >
            {loading ? 'Testing...' : 'Test Signed URL'}
          </button>
        </div>
      </div>
      
      {testResults && (
        <div className="mt-6 p-4 border rounded-lg">
          <h3 className="text-lg font-semibold mb-3">Test Results</h3>
          
          <div className="grid grid-cols-2 gap-4 mb-4">
            <div>
              <span className="font-medium">Status:</span>
              <span className={`ml-2 px-2 py-1 rounded text-sm ${
                testResults.accessible ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
              }`}>
                {testResults.accessible ? 'Accessible' : 'Not Accessible'}
              </span>
            </div>
            
            <div>
              <span className="font-medium">Status Code:</span>
              <span className="ml-2">{testResults.statusCode || 'N/A'}</span>
            </div>
          </div>
          
          {testResults.error && (
            <div className="mb-4">
              <span className="font-medium text-red-600">Error:</span>
              <span className="ml-2 text-red-600">{testResults.error}</span>
            </div>
          )}
          
          {testResults.signedUrl && (
            <div className="mb-4">
              <span className="font-medium">Signed URL:</span>
              <div className="mt-1 p-2 bg-gray-100 rounded text-sm break-all">
                {testResults.signedUrl}
              </div>
            </div>
          )}
          
          {testResults.suggestions && testResults.suggestions.length > 0 && (
            <div>
              <span className="font-medium">Suggestions:</span>
              <ul className="mt-1 list-disc list-inside space-y-1">
                {testResults.suggestions.map((suggestion, index) => (
                  <li key={index} className="text-sm text-gray-600">{suggestion}</li>
                ))}
              </ul>
            </div>
          )}
        </div>
      )}
      
      {debugReport && (
        <div className="mt-6">
          <h3 className="text-lg font-semibold mb-3">Debug Report</h3>
          <pre className="bg-gray-100 p-4 rounded-lg text-sm overflow-x-auto whitespace-pre-wrap">
            {debugReport}
          </pre>
        </div>
      )}
      
      <div className="mt-6 p-4 bg-blue-50 rounded-lg">
        <h4 className="font-semibold text-blue-900 mb-2">Quick Fixes for Access Denied Errors:</h4>
        <ol className="list-decimal list-inside space-y-1 text-sm text-blue-800">
          <li>Check if the S3 bucket allows public read access</li>
          <li>Verify that files are uploaded with 'public-read' ACL</li>
          <li>Ensure bucket policy allows GetObject for public access</li>
          <li>Try using signed URLs for private files</li>
          <li>Check AWS credentials and permissions</li>
        </ol>
      </div>
    </div>
  );
};

export default S3AccessTest;
