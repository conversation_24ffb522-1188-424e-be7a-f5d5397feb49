/**
 * Quick setup utility for Area of Expertise
 * This script creates the area_of_expertise table and seeds initial data
 */

import { sql } from '../config/database.js';

/**
 * Create area_of_expertise table and seed data
 */
export const setupAreaOfExpertise = async () => {
  try {
    console.log('🎯 Setting up Area of Expertise table...');

    // Create the table
    await sql`
      CREATE TABLE IF NOT EXISTS area_of_expertise (
        id SERIAL PRIMARY KEY,
        name VARCHAR(255) NOT NULL UNIQUE,
        description TEXT,
        is_active BOOLEAN DEFAULT TRUE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `;
    console.log('✅ area_of_expertise table created');

    // Check if data already exists
    const existingCount = await sql`SELECT COUNT(*) as count FROM area_of_expertise`;
    if (existingCount[0].count > 0) {
      console.log('✅ Area of expertise data already exists');
      return true;
    }

    // Sample area of expertise data
    const areaOfExpertiseData = [
      {
        name: 'Patent Law',
        description: 'Patent filing, prosecution, and enforcement expertise'
      },
      {
        name: 'Trademark Law',
        description: 'Trademark registration, protection, and enforcement'
      },
      {
        name: 'Copyright Law',
        description: 'Copyright registration and protection services'
      },
      {
        name: 'IP Litigation',
        description: 'Intellectual property litigation and dispute resolution'
      },
      {
        name: 'Technical Writing',
        description: 'Patent drafting and technical documentation'
      },
      {
        name: 'Prior Art Search',
        description: 'Patent search and prior art analysis'
      },
      {
        name: 'Legal Consulting',
        description: 'General intellectual property consulting'
      },
      {
        name: 'Design Registration',
        description: 'Industrial design registration and protection'
      },
      {
        name: 'Trade Secret Protection',
        description: 'Trade secret and confidentiality services'
      },
      {
        name: 'IP Valuation',
        description: 'Intellectual property valuation and assessment'
      }
    ];

    // Insert area of expertise data
    for (const area of areaOfExpertiseData) {
      await sql`
        INSERT INTO area_of_expertise (name, description, is_active, created_at, updated_at)
        VALUES (${area.name}, ${area.description}, true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
        ON CONFLICT (name) DO NOTHING
      `;
    }

    console.log(`✅ Successfully seeded ${areaOfExpertiseData.length} area of expertise entries`);

    // Also add point_of_contact column to vendors table if it doesn't exist
    try {
      await sql`
        ALTER TABLE vendors 
        ADD COLUMN IF NOT EXISTS point_of_contact JSONB DEFAULT '[]'
      `;
      console.log('✅ point_of_contact column added to vendors table');
    } catch (error) {
      console.log('✅ point_of_contact column already exists');
    }

    console.log('🎉 Area of Expertise setup completed successfully!');
    return true;

  } catch (error) {
    console.error('❌ Error setting up area of expertise:', error);
    throw error;
  }
};

// Make setup function available globally for debugging
if (typeof window !== 'undefined' && process.env.NODE_ENV === 'development') {
  window.setupAreaOfExpertise = setupAreaOfExpertise;
  console.log('🔧 Debug function available: window.setupAreaOfExpertise()');
}
