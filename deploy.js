#!/usr/bin/env node

/**
 * Automated Deployment Script for Vercel
 * This script helps deploy the application with all fixes applied
 */

import { execSync } from 'child_process';
import fs from 'fs';
import path from 'path';

console.log('🚀 Starting automated deployment process...\n');

// Check if we're in the right directory
if (!fs.existsSync('package.json')) {
  console.error('❌ Error: package.json not found. Please run this script from the project root directory.');
  process.exit(1);
}

// Step 1: Verify all fixes are in place
console.log('📋 Step 1: Verifying fixes...');

const requiredFiles = [
  'api/upload.js',
  'vercel.json',
  'scripts/fix-database-schema.js',
  'src/components/ErrorBoundary.jsx',
  'DEPLOYMENT_GUIDE.md'
];

let allFilesExist = true;
requiredFiles.forEach(file => {
  if (fs.existsSync(file)) {
    console.log(`✅ ${file} - Found`);
  } else {
    console.log(`❌ ${file} - Missing`);
    allFilesExist = false;
  }
});

if (!allFilesExist) {
  console.error('\n❌ Some required files are missing. Please ensure all fixes have been applied.');
  process.exit(1);
}

// Step 2: Install dependencies
console.log('\n📦 Step 2: Installing dependencies...');
try {
  execSync('npm install', { stdio: 'inherit' });
  console.log('✅ Dependencies installed successfully');
} catch (error) {
  console.error('❌ Failed to install dependencies:', error.message);
  process.exit(1);
}

// Step 3: Build the application
console.log('\n🔨 Step 3: Building application...');
try {
  execSync('npm run build', { stdio: 'inherit' });
  console.log('✅ Build completed successfully');
} catch (error) {
  console.error('❌ Build failed:', error.message);
  process.exit(1);
}

// Step 4: Check if Vercel CLI is installed
console.log('\n🔧 Step 4: Checking Vercel CLI...');
try {
  execSync('vercel --version', { stdio: 'pipe' });
  console.log('✅ Vercel CLI is installed');
} catch (error) {
  console.log('⚠️ Vercel CLI not found. Installing...');
  try {
    execSync('npm install -g vercel', { stdio: 'inherit' });
    console.log('✅ Vercel CLI installed successfully');
  } catch (installError) {
    console.error('❌ Failed to install Vercel CLI:', installError.message);
    console.log('\n📝 Manual installation required:');
    console.log('   npm install -g vercel');
    console.log('   vercel login');
    console.log('   vercel --prod');
    process.exit(1);
  }
}

// Step 5: Deploy to Vercel
console.log('\n🚀 Step 5: Deploying to Vercel...');
console.log('📝 Note: You may need to login to Vercel if not already logged in.');

try {
  // Check if user is logged in
  try {
    execSync('vercel whoami', { stdio: 'pipe' });
    console.log('✅ Already logged in to Vercel');
  } catch (loginError) {
    console.log('🔐 Please login to Vercel...');
    execSync('vercel login', { stdio: 'inherit' });
  }

  // Deploy
  console.log('🚀 Deploying to production...');
  execSync('vercel --prod', { stdio: 'inherit' });
  console.log('✅ Deployment completed successfully!');

} catch (error) {
  console.error('❌ Deployment failed:', error.message);
  console.log('\n📝 Manual deployment steps:');
  console.log('1. vercel login');
  console.log('2. vercel --prod');
  process.exit(1);
}

// Step 6: Post-deployment instructions
console.log('\n🎉 Deployment completed successfully!');
console.log('\n📋 IMPORTANT: Post-deployment steps:');
console.log('1. Set environment variables in Vercel dashboard:');
console.log('   - VITE_DATABASE_URL');
console.log('   - AWS_ACCESS_KEY_ID');
console.log('   - AWS_SECRET_ACCESS_KEY');
console.log('   - AWS_S3_BUCKET_NAME');
console.log('   - AWS_REGION');
console.log('   - VITE_AWS_S3_BASE_URL');
console.log('   - VITE_API_BASE_URL');
console.log('\n2. Run database schema fix:');
console.log('   node scripts/fix-database-schema.js');
console.log('\n3. Test your deployment:');
console.log('   - Check if app loads without blank screen');
console.log('   - Test dashboard functionality');
console.log('   - Test file upload features');
console.log('\n📖 See DEPLOYMENT_GUIDE.md for detailed instructions.');
