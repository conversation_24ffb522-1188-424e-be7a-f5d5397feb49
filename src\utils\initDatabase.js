import { testConnection, initializeDatabase, insertSampleData, refreshDatabaseData, sql } from '../config/database.js';
import { performHealthCheck } from './dbHealthCheck.js';

// Initialize the database when the app starts
export const initApp = async () => {
  try {
    console.log('🚀 Starting application initialization...');

    // Perform comprehensive health check
    console.log('🔄 Performing database health check...');
    const healthCheck = await performHealthCheck();

    if (!healthCheck.connection) {
      throw new Error('Database connection failed: ' + healthCheck.errors.join(', '));
    }

    // Test database connection (legacy)
    console.log('🔄 Testing database connection (legacy)...');
    const connectionSuccess = await testConnection();

    if (!connectionSuccess) {
      throw new Error('Database connection failed');
    }
    
    // Initialize database schema
    console.log('🔄 Initializing database schema...');
    const schemaSuccess = await initializeDatabase();
    
    if (!schemaSuccess) {
      throw new Error('Database schema initialization failed');
    }
    
    // Insert sample data to database
    console.log('🔄 Inserting sample data to database...');
    try {
      const dataSuccess = await insertSampleData();
      if (!dataSuccess) {
        console.warn('⚠️ Initial sample data insertion failed, trying refresh...');
        await refreshDatabaseData();
      }
    } catch (error) {
      console.warn('⚠️ Sample data insertion failed, trying refresh...', error.message);
      await refreshDatabaseData();
    }

    // Initialize Point of Contact system
    console.log('🔄 Initializing Point of Contact system...');
    try {
      await initPointOfContactSystem();
    } catch (error) {
      console.warn('⚠️ Point of Contact system initialization failed:', error.message);
    }
    
    console.log('✅ Application initialization completed successfully!');
    console.log('📊 Database is ready for use');
    
    return true;
  } catch (error) {
    console.error('❌ Application initialization failed:', error);
    console.error('🔧 Please check your database configuration and try again');
    return false;
  }
};

// Check database health
export const checkDatabaseHealth = async () => {
  try {
    const isHealthy = await testConnection();
    return {
      status: isHealthy ? 'healthy' : 'unhealthy',
      timestamp: new Date().toISOString(),
      message: isHealthy ? 'Database connection is working' : 'Database connection failed'
    };
  } catch (error) {
    return {
      status: 'error',
      timestamp: new Date().toISOString(),
      message: error.message
    };
  }
};

// Manual database refresh function (for debugging)
export const manualRefreshDatabase = async () => {
  try {
    console.log('🔄 Manual database refresh triggered...');
    await refreshDatabaseData();
    console.log('✅ Manual database refresh completed');
    return { success: true, message: 'Database refreshed successfully' };
  } catch (error) {
    console.error('❌ Manual database refresh failed:', error);
    return { success: false, message: error.message };
  }
};

/**
 * Initialize Point of Contact system
 */
export const initPointOfContactSystem = async () => {
  try {
    console.log('👥 Initializing Point of Contact system...');

    // Create area_of_expertise table if it doesn't exist
    await sql`
      CREATE TABLE IF NOT EXISTS area_of_expertise (
        id SERIAL PRIMARY KEY,
        name VARCHAR(255) NOT NULL UNIQUE,
        description TEXT,
        is_active BOOLEAN DEFAULT TRUE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `;

    // Check if area of expertise data exists
    const existingCount = await sql`SELECT COUNT(*) as count FROM area_of_expertise`;
    if (existingCount.rows[0].count === 0) {
      // Insert sample area of expertise data
      const areaOfExpertiseData = [
        { name: 'Patent Law', description: 'Patent filing, prosecution, and enforcement expertise' },
        { name: 'Trademark Law', description: 'Trademark registration, protection, and enforcement' },
        { name: 'Copyright Law', description: 'Copyright registration and protection services' },
        { name: 'IP Litigation', description: 'Intellectual property litigation and dispute resolution' },
        { name: 'Technical Writing', description: 'Patent drafting and technical documentation' },
        { name: 'Prior Art Search', description: 'Patent search and prior art analysis' },
        { name: 'Legal Consulting', description: 'General intellectual property consulting' }
      ];

      for (const area of areaOfExpertiseData) {
        await sql`
          INSERT INTO area_of_expertise (name, description, is_active, created_at, updated_at)
          VALUES (${area.name}, ${area.description}, true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
          ON CONFLICT (name) DO NOTHING
        `;
      }
      console.log(`✅ Inserted ${areaOfExpertiseData.length} area of expertise entries`);
    } else {
      console.log('✅ Area of expertise data already exists');
    }

    // Add point_of_contact column to vendors table if it doesn't exist
    try {
      await sql`
        ALTER TABLE vendors
        ADD COLUMN IF NOT EXISTS point_of_contact JSONB DEFAULT '[]'
      `;
      console.log('✅ point_of_contact column added to vendors table');
    } catch (error) {
      // Column might already exist, which is fine
      console.log('✅ point_of_contact column already exists or added');
    }

    console.log('🎉 Point of Contact system initialized successfully!');
    return true;

  } catch (error) {
    console.error('❌ Error initializing Point of Contact system:', error);
    return false;
  }
};

// Add global debug function for manual refresh
if (typeof window !== 'undefined' && process.env.NODE_ENV === 'development') {
  window.refreshDatabase = manualRefreshDatabase;
  window.initPointOfContact = initPointOfContactSystem;
  console.log('🔧 Debug functions available: window.refreshDatabase(), window.initPointOfContact()');
}
