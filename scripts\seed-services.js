import postgres from 'postgres';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Get database URL from environment
const databaseUrl = process.env.VITE_DATABASE_URL || process.env.DATABASE_URL;

if (!databaseUrl) {
  console.error('❌ Database URL not found. Please set VITE_DATABASE_URL or DATABASE_URL in your .env file.');
  process.exit(1);
}

// Create database connection
const sql = postgres(databaseUrl, {
  ssl: databaseUrl.includes('neon.tech') ? { rejectUnauthorized: false } : false
});

// Default services data
const defaultServices = [
  {
    name: 'Office Supplies',
    description: 'Stationery, paper, pens, and general office supplies',
    category: 'Office Supplies'
  },
  {
    name: 'Furniture',
    description: 'Office furniture, chairs, desks, and storage solutions',
    category: 'Office Supplies'
  },
  {
    name: 'Technology Equipment',
    description: 'Computers, printers, networking equipment, and IT hardware',
    category: 'Technology'
  },
  {
    name: 'Maintenance Services',
    description: 'Building maintenance, repairs, and facility management',
    category: 'Maintenance'
  },
  {
    name: 'Cleaning Services',
    description: 'Office cleaning, janitorial services, and sanitation',
    category: 'Maintenance'
  },
  {
    name: 'Security Services',
    description: 'Security guards, surveillance systems, and access control',
    category: 'Professional Services'
  },
  {
    name: 'Catering Services',
    description: 'Food and beverage services for events and daily operations',
    category: 'Professional Services'
  },
  {
    name: 'Transportation',
    description: 'Logistics, delivery, and transportation services',
    category: 'Logistics'
  },
  {
    name: 'Consulting',
    description: 'Business consulting, advisory, and professional services',
    category: 'Professional Services'
  },
  {
    name: 'Marketing Services',
    description: 'Digital marketing, advertising, and promotional services',
    category: 'Professional Services'
  },
  {
    name: 'Legal Services',
    description: 'Legal consultation, documentation, and compliance services',
    category: 'Professional Services'
  },
  {
    name: 'Financial Services',
    description: 'Accounting, bookkeeping, and financial advisory services',
    category: 'Professional Services'
  },
  {
    name: 'Training & Development',
    description: 'Employee training, skill development, and educational services',
    category: 'Professional Services'
  },
  {
    name: 'Software Development',
    description: 'Custom software development and IT solutions',
    category: 'Technology'
  },
  {
    name: 'Other',
    description: 'Miscellaneous services not covered in other categories',
    category: 'General'
  }
];

async function seedServices() {
  try {
    console.log('🌱 Starting services seeding...');

    // Check if services table exists and has data
    const existingServices = await sql`
      SELECT COUNT(*) as count FROM services
    `;

    if (existingServices[0].count > 0) {
      console.log(`📊 Found ${existingServices[0].count} existing services in database`);
      console.log('⚠️  Skipping seeding to avoid duplicates. Delete existing services first if you want to reseed.');
      return;
    }

    console.log('📝 Creating default services...');

    // Insert default services
    for (const service of defaultServices) {
      try {
        const result = await sql`
          INSERT INTO services (name, description, category, "isActive", status, created_at, updated_at)
          VALUES (
            ${service.name},
            ${service.description},
            ${service.category},
            true,
            'Active',
            CURRENT_TIMESTAMP,
            CURRENT_TIMESTAMP
          )
          RETURNING id, name
        `;
        
        console.log(`✅ Created: ${result[0].name}`);
      } catch (error) {
        if (error.message.includes('duplicate key')) {
          console.log(`⚠️  Skipped: ${service.name} (already exists)`);
        } else {
          console.error(`❌ Error creating ${service.name}:`, error.message);
        }
      }
    }

    // Get final count
    const finalCount = await sql`
      SELECT COUNT(*) as count FROM services WHERE "isActive" = true
    `;

    console.log(`🎉 Successfully created ${defaultServices.length} services!`);
    console.log(`📊 Total active services: ${finalCount[0].count}`);
    console.log('✅ Services seeding completed successfully!');

  } catch (error) {
    console.error('❌ Error seeding services:', error);
    process.exit(1);
  } finally {
    // Close database connection
    await sql.end();
  }
}

// Run the seeding
seedServices();
