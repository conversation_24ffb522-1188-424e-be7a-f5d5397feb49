import postgres from 'postgres';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Get database URL from environment
const databaseUrl = process.env.VITE_DATABASE_URL || process.env.DATABASE_URL;

if (!databaseUrl) {
  console.error('❌ Database URL not found. Please set VITE_DATABASE_URL or DATABASE_URL in your .env file.');
  process.exit(1);
}

// Create database connection
const sql = postgres(databaseUrl, {
  ssl: databaseUrl.includes('neon.tech') ? { rejectUnauthorized: false } : false
});

async function initServiceCategoriesTable() {
  try {
    console.log('🔄 Initializing service categories table...');

    // Create service_categories table
    await sql`
      CREATE TABLE IF NOT EXISTS service_categories (
        id SERIAL PRIMARY KEY,
        name VARCHAR(255) NOT NULL UNIQUE,
        description TEXT,
        color VARCHAR(7) DEFAULT '#6B7280',
        "isActive" BOOLEAN DEFAULT TRUE,
        status VARCHAR(50) DEFAULT 'Active',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `;

    console.log('✅ Service categories table created successfully!');

    // Add category_id column to services table if it doesn't exist
    try {
      await sql`
        ALTER TABLE services 
        ADD COLUMN IF NOT EXISTS category_id INTEGER REFERENCES service_categories(id)
      `;
      console.log('✅ Added category_id column to services table!');
    } catch (error) {
      console.log('ℹ️ Category_id column already exists or error adding it:', error.message);
    }

  } catch (error) {
    console.error('❌ Error creating service categories table:', error);
    process.exit(1);
  } finally {
    // Close database connection
    await sql.end();
  }
}

// Run the initialization
initServiceCategoriesTable();
