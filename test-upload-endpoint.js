// Test Upload Endpoint
// This script tests if the upload endpoint is working

import fetch from 'node-fetch';
import FormData from 'form-data';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

console.log('🧪 Testing Upload Endpoint');
console.log('==========================');

// Test endpoint availability
async function testEndpoint() {
  const endpoint = 'http://localhost:5173/api/upload';
  
  console.log('1. 🔍 Testing endpoint availability...');
  
  try {
    // Try OPTIONS first (CORS preflight)
    const optionsResponse = await fetch(endpoint, {
      method: 'OPTIONS'
    });

    console.log(`   OPTIONS Status: ${optionsResponse.status}`);

    // Try GET request to see if endpoint exists
    const getResponse = await fetch(endpoint, {
      method: 'GET'
    });

    console.log(`   GET Status: ${getResponse.status}`);

    // If we get 405 (Method Not Allowed), it means the endpoint exists but doesn't support GET
    // If we get 404, the endpoint doesn't exist
    // If we get 200, the endpoint exists and supports GET

    if (getResponse.status === 405 || getResponse.status === 200 || optionsResponse.status === 204) {
      console.log('✅ Endpoint is reachable');
      return true;
    } else if (getResponse.status === 404) {
      console.log('❌ Endpoint not found (404)');
      return false;
    } else {
      console.log('❌ Endpoint returned unexpected status');
      return false;
    }
  } catch (error) {
    console.log('❌ Endpoint not reachable:', error.message);
    return false;
  }
}

// Test file upload
async function testUpload() {
  console.log('\n2. 📤 Testing file upload...');
  
  // Create a test PDF file (minimal PDF structure)
  const testPdfContent = `%PDF-1.4
1 0 obj
<<
/Type /Catalog
/Pages 2 0 R
>>
endobj
2 0 obj
<<
/Type /Pages
/Kids [3 0 R]
/Count 1
>>
endobj
3 0 obj
<<
/Type /Page
/Parent 2 0 R
/MediaBox [0 0 612 792]
>>
endobj
xref
0 4
0000000000 65535 f
0000000010 00000 n
0000000053 00000 n
0000000125 00000 n
trailer
<<
/Size 4
/Root 1 0 R
>>
startxref
173
%%EOF`;

  const testFilePath = path.join(__dirname, 'test-file.pdf');
  fs.writeFileSync(testFilePath, testPdfContent);
  
  try {
    const form = new FormData();
    form.append('file', fs.createReadStream(testFilePath));
    
    const uploadUrl = 'http://localhost:5173/api/upload?module=test&recordId=123&fileType=document&allowedTypes=all';
    
    console.log('   Upload URL:', uploadUrl);
    
    const response = await fetch(uploadUrl, {
      method: 'POST',
      body: form
    });
    
    console.log(`   Status: ${response.status}`);
    
    const result = await response.text();
    console.log('   Response:', result);
    
    if (response.ok) {
      console.log('✅ Upload test successful');
      
      try {
        const jsonResult = JSON.parse(result);
        if (jsonResult.success) {
          console.log('   File URL:', jsonResult.file.url);
          
          // Test file access
          const fileResponse = await fetch(jsonResult.file.url, { method: 'HEAD' });
          console.log(`   File access status: ${fileResponse.status}`);
          
          if (fileResponse.ok) {
            console.log('✅ File is accessible');
          } else {
            console.log('❌ File access denied');
          }
        }
      } catch (parseError) {
        console.log('⚠️ Could not parse response as JSON');
      }
    } else {
      console.log('❌ Upload test failed');
    }
    
  } catch (error) {
    console.log('❌ Upload error:', error.message);
  } finally {
    // Clean up test file
    if (fs.existsSync(testFilePath)) {
      fs.unlinkSync(testFilePath);
    }
  }
}

// Main test function
async function runTests() {
  const endpointAvailable = await testEndpoint();
  
  if (endpointAvailable) {
    await testUpload();
  } else {
    console.log('\n❌ Cannot test upload - endpoint not available');
    console.log('\n🔧 Troubleshooting:');
    console.log('1. Make sure dev server is running: npm run dev');
    console.log('2. Check if Vite is running on http://localhost:5173');
    console.log('3. Verify upload plugin is configured in vite.config.js');
  }
  
  console.log('\n📋 Summary:');
  console.log('- Endpoint test:', endpointAvailable ? '✅ Pass' : '❌ Fail');
  console.log('- Upload test: See results above');
}

// Run tests
runTests().catch(console.error);
