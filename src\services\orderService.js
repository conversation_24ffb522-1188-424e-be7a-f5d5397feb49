import { sql } from '../config/database.js';

/**
 * Clean Order Service - No Demo Data
 * Works with the actual database schema
 */

// Get all orders from database
export const getAllOrders = async () => {
  try {
    // Check if orders table exists
    const tableExists = await sql`
      SELECT EXISTS (
        SELECT FROM information_schema.tables
        WHERE table_name = 'orders'
      )
    `;

    if (!tableExists[0].exists) {
      return [];
    }

    // Fetch orders using the actual column names from the database
    const orders = await sql`
      SELECT 
        id,
        orderreferencenumber,
        orderonboardingdate,
        client,
        typeofwork,
        vendorname,
        dateofworkcompletionexpected,
        totalinvoicevalue,
        totalvaluegstgovtfees,
        dateofpaymentexpected,
        dateofonboardingvendor,
        currentstatus,
        statuscomments,
        dateofstatuschange,
        dateofworkcompletionexpectedfromvendor,
        amounttobepaidtovendor,
        amountpaidtovendor,
        created_at,
        updated_at
      FROM orders 
      ORDER BY created_at DESC
    `;

    // Transform orders to match UI expectations
    const transformedOrders = orders.map(order => ({
      id: order.id,
      orderReferenceNumber: order.orderreferencenumber || `ORDER-${order.id}`,
      orderOnboardingDate: order.orderonboardingdate ? new Date(order.orderonboardingdate).toISOString().split('T')[0] : '',
      client: order.client || 'Unknown Client',
      typeOfWork: order.typeofwork || 'N/A',
      dateOfWorkCompletionExpected: order.dateofworkcompletionexpected ? new Date(order.dateofworkcompletionexpected).toISOString().split('T')[0] : '',
      totalInvoiceValue: parseFloat(order.totalinvoicevalue) || 0,
      totalValueGstGovtFees: parseFloat(order.totalvaluegstgovtfees) || 0,
      dateOfPaymentExpected: order.dateofpaymentexpected ? new Date(order.dateofpaymentexpected).toISOString().split('T')[0] : '',
      dateOfOnboardingVendor: order.dateofonboardingvendor ? new Date(order.dateofonboardingvendor).toISOString().split('T')[0] : '',
      vendorName: order.vendorname || 'No Vendor Assigned',
      currentStatus: order.currentstatus || 'Pending',
      statusComments: order.statuscomments || '',
      dateOfStatusChange: order.dateofstatuschange ? new Date(order.dateofstatuschange).toISOString().split('T')[0] : '',
      dateOfWorkCompletionExpectedFromVendor: order.dateofworkcompletionexpectedfromvendor ? new Date(order.dateofworkcompletionexpectedfromvendor).toISOString().split('T')[0] : '',
      amountToBePaidToVendor: parseFloat(order.amounttobepaidtovendor) || 0,
      amountPaidToVendor: parseFloat(order.amountpaidtovendor) || 0,
      createdAt: order.created_at,
      updatedAt: order.updated_at
    }));

    return transformedOrders;
  } catch (error) {
    console.error('❌ Error fetching orders:', error);
    return [];
  }
};

// Get order by ID
export const getOrderById = async (id) => {
  try {
    const orders = await sql`
      SELECT 
        id,
        orderreferencenumber,
        orderonboardingdate,
        client,
        typeofwork,
        vendorname,
        dateofworkcompletionexpected,
        totalinvoicevalue,
        totalvaluegstgovtfees,
        dateofpaymentexpected,
        dateofonboardingvendor,
        currentstatus,
        statuscomments,
        dateofstatuschange,
        dateofworkcompletionexpectedfromvendor,
        amounttobepaidtovendor,
        amountpaidtovendor,
        statushistory,
        documentsprovidedbyclient,
        documentsprovidedbyvendor,
        invoicefromvendor,
        created_at,
        updated_at
      FROM orders 
      WHERE id = ${id}
    `;

    if (orders.length === 0) {
      return null;
    }

    const order = orders[0];
    
    // Transform order to match UI expectations
    const transformedOrder = {
      id: order.id,
      orderReferenceNumber: order.orderreferencenumber || `ORDER-${order.id}`,
      orderOnboardingDate: order.orderonboardingdate ? new Date(order.orderonboardingdate).toISOString().split('T')[0] : '',
      client: order.client || 'Unknown Client',
      typeOfWork: order.typeofwork || 'N/A',
      dateOfWorkCompletionExpected: order.dateofworkcompletionexpected ? new Date(order.dateofworkcompletionexpected).toISOString().split('T')[0] : '',
      totalInvoiceValue: parseFloat(order.totalinvoicevalue) || 0,
      totalValueGstGovtFees: parseFloat(order.totalvaluegstgovtfees) || 0,
      dateOfPaymentExpected: order.dateofpaymentexpected ? new Date(order.dateofpaymentexpected).toISOString().split('T')[0] : '',
      dateOfOnboardingVendor: order.dateofonboardingvendor ? new Date(order.dateofonboardingvendor).toISOString().split('T')[0] : '',
      vendorName: order.vendorname || 'No Vendor Assigned',
      currentStatus: order.currentstatus || 'Pending',
      statusComments: order.statuscomments || '',
      dateOfStatusChange: order.dateofstatuschange ? new Date(order.dateofstatuschange).toISOString().split('T')[0] : '',
      dateOfWorkCompletionExpectedFromVendor: order.dateofworkcompletionexpectedfromvendor ? new Date(order.dateofworkcompletionexpectedfromvendor).toISOString().split('T')[0] : '',
      amountToBePaidToVendor: parseFloat(order.amounttobepaidtovendor) || 0,
      amountPaidToVendor: parseFloat(order.amountpaidtovendor) || 0,
      statusHistory: order.statushistory || [],
      documentsProvidedByClient: order.documentsprovidedbyclient || [],
      documentsProvidedByVendor: order.documentsprovidedbyvendor || [],
      invoiceFromVendor: order.invoicefromvendor || [],
      createdAt: order.created_at,
      updatedAt: order.updated_at
    };

    return transformedOrder;
  } catch (error) {
    console.error('❌ Error fetching order by ID:', error);
    return null;
  }
};

// Generate next reference number
export const generateReferenceNumber = async () => {
  try {
    const result = await sql`
      SELECT orderreferencenumber 
      FROM orders 
      WHERE orderreferencenumber LIKE 'IS-%' 
      ORDER BY created_at DESC 
      LIMIT 1
    `;
    
    if (result.length === 0) {
      return 'IS-2024-001';
    }
    
    const lastRef = result[0].orderreferencenumber;
    const lastNumber = parseInt(lastRef.split('-')[2]);
    const nextNumber = (lastNumber + 1).toString().padStart(3, '0');
    return `IS-2024-${nextNumber}`;
  } catch (error) {
    console.error('Error generating reference number:', error);
    return `IS-2024-${Date.now().toString().slice(-3)}`;
  }
};

// Create new order
export const createOrder = async (orderData) => {
  try {
    // Generate reference number if not provided
    const referenceNumber = orderData.orderReferenceNumber || await generateReferenceNumber();

    const result = await sql`
      INSERT INTO orders (
        orderreferencenumber,
        orderonboardingdate,
        client,
        typeofwork,
        vendorname,
        dateofworkcompletionexpected,
        totalinvoicevalue,
        totalvaluegstgovtfees,
        dateofpaymentexpected,
        dateofonboardingvendor,
        currentstatus,
        statuscomments,
        dateofstatuschange,
        dateofworkcompletionexpectedfromvendor,
        amounttobepaidtovendor,
        amountpaidtovendor
      ) VALUES (
        ${referenceNumber},
        ${orderData.orderOnboardingDate || null},
        ${orderData.client || ''},
        ${orderData.typeOfWork || ''},
        ${orderData.vendorName || ''},
        ${orderData.dateOfWorkCompletionExpected || null},
        ${parseFloat(orderData.totalInvoiceValue) || 0},
        ${parseFloat(orderData.totalValueGstGovtFees) || 0},
        ${orderData.dateOfPaymentExpected || null},
        ${orderData.dateOfOnboardingVendor || null},
        ${orderData.currentStatus || 'Pending'},
        ${orderData.statusComments || ''},
        ${orderData.dateOfStatusChange || null},
        ${orderData.dateOfWorkCompletionExpectedFromVendor || null},
        ${parseFloat(orderData.amountToBePaidToVendor) || 0},
        ${parseFloat(orderData.amountPaidToVendor) || 0}
      ) RETURNING id
    `;

    const newOrderId = result[0].id;

    // Return the created order
    return await getOrderById(newOrderId);
  } catch (error) {
    console.error('❌ Error creating order:', error);
    throw error;
  }
};

// Update order
export const updateOrder = async (id, orderData) => {
  try {
    await sql`
      UPDATE orders SET
        orderreferencenumber = ${orderData.orderReferenceNumber},
        orderonboardingdate = ${orderData.orderOnboardingDate || null},
        client = ${orderData.client || ''},
        typeofwork = ${orderData.typeOfWork || ''},
        vendorname = ${orderData.vendorName || ''},
        dateofworkcompletionexpected = ${orderData.dateOfWorkCompletionExpected || null},
        totalinvoicevalue = ${parseFloat(orderData.totalInvoiceValue) || 0},
        totalvaluegstgovtfees = ${parseFloat(orderData.totalValueGstGovtFees) || 0},
        dateofpaymentexpected = ${orderData.dateOfPaymentExpected || null},
        dateofonboardingvendor = ${orderData.dateOfOnboardingVendor || null},
        currentstatus = ${orderData.currentStatus || 'Pending'},
        statuscomments = ${orderData.statusComments || ''},
        dateofstatuschange = ${orderData.dateOfStatusChange || null},
        dateofworkcompletionexpectedfromvendor = ${orderData.dateOfWorkCompletionExpectedFromVendor || null},
        amounttobepaidtovendor = ${parseFloat(orderData.amountToBePaidToVendor) || 0},
        amountpaidtovendor = ${parseFloat(orderData.amountPaidToVendor) || 0},
        updated_at = CURRENT_TIMESTAMP
      WHERE id = ${id}
    `;

    return await getOrderById(id);
  } catch (error) {
    console.error('❌ Error updating order:', error);
    throw error;
  }
};

// Delete order
export const deleteOrder = async (id) => {
  try {
    await sql`DELETE FROM orders WHERE id = ${id}`;

    return true;
  } catch (error) {
    console.error('❌ Error deleting order:', error);
    throw error;
  }
};
