/**
 * IMMEDIATE NEONDB CONNECTION TEST
 * Run this in browser console to test if NeonDB is working
 */

// Test NeonDB connection and all operations
window.testNeonDBNow = async () => {
  try {
    console.log('🚨 TESTING NEONDB CONNECTION...');
    console.log('');

    // Import database connection
    const { sql } = await import('./src/config/database.js');

    // Test 1: Basic Connection
    console.log('1️⃣ Testing basic connection...');
    try {
      const result = await sql`SELECT NOW() as current_time, version() as db_version`;
      console.log('✅ Connection successful!');
      console.log('   Time:', result[0].current_time);
      console.log('   Version:', result[0].db_version.substring(0, 50) + '...');
    } catch (error) {
      console.error('❌ Connection failed:', error.message);
      return false;
    }

    // Test 2: Check Tables
    console.log('');
    console.log('2️⃣ Checking tables...');
    try {
      const tables = await sql`
        SELECT table_name 
        FROM information_schema.tables 
        WHERE table_schema = 'public' 
        ORDER BY table_name
      `;
      console.log('✅ Tables found:', tables.map(t => t.table_name));
    } catch (error) {
      console.error('❌ Table check failed:', error.message);
    }

    // Test 3: Test Area of Expertise
    console.log('');
    console.log('3️⃣ Testing area_of_expertise...');
    try {
      const areaCount = await sql`SELECT COUNT(*) as count FROM area_of_expertise`;
      console.log('✅ Area of expertise accessible, count:', areaCount[0].count);
      
      // Try to fetch some records
      const areas = await sql`SELECT id, name FROM area_of_expertise LIMIT 3`;
      console.log('   Sample records:', areas);
    } catch (error) {
      console.error('❌ Area of expertise failed:', error.message);
    }

    // Test 4: Test Services
    console.log('');
    console.log('4️⃣ Testing services...');
    try {
      const servicesCount = await sql`SELECT COUNT(*) as count FROM services`;
      console.log('✅ Services accessible, count:', servicesCount[0].count);
      
      // Try to fetch some records
      const services = await sql`SELECT id, name FROM services LIMIT 3`;
      console.log('   Sample records:', services);
    } catch (error) {
      console.error('❌ Services failed:', error.message);
    }

    // Test 5: Test Vendors
    console.log('');
    console.log('5️⃣ Testing vendors...');
    try {
      const vendorsCount = await sql`SELECT COUNT(*) as count FROM vendors`;
      console.log('✅ Vendors accessible, count:', vendorsCount[0].count);
      
      // Check vendor table structure
      const vendorColumns = await sql`
        SELECT column_name, data_type 
        FROM information_schema.columns 
        WHERE table_name = 'vendors'
        ORDER BY ordinal_position
      `;
      console.log('   Vendor columns:', vendorColumns.map(c => c.column_name));
      
      // Check for startup_benefits column specifically
      const hasStartupBenefits = vendorColumns.some(c => c.column_name === 'startup_benefits');
      console.log('   Has startup_benefits column:', hasStartupBenefits);
    } catch (error) {
      console.error('❌ Vendors test failed:', error.message);
    }

    // Test 6: Test Write Operation
    console.log('');
    console.log('6️⃣ Testing write operations...');
    try {
      // Try to insert a test record
      const testInsert = await sql`
        INSERT INTO area_of_expertise (name, description) 
        VALUES ('TEST_CONNECTION', 'Test record for connection verification')
        ON CONFLICT (name) DO UPDATE SET description = EXCLUDED.description
        RETURNING id, name
      `;
      console.log('✅ Write operation successful, test record ID:', testInsert[0].id);
      
      // Clean up test record
      await sql`DELETE FROM area_of_expertise WHERE name = 'TEST_CONNECTION'`;
      console.log('✅ Test record cleaned up');
    } catch (error) {
      console.error('❌ Write operation failed:', error.message);
    }

    console.log('');
    console.log('🎉 NEONDB CONNECTION TEST COMPLETED!');
    console.log('');
    return true;

  } catch (error) {
    console.error('💥 FATAL ERROR:', error);
    console.error('');
    console.error('🔧 Possible issues:');
    console.error('   - NeonDB instance is down');
    console.error('   - Network connectivity issues');
    console.error('   - Database credentials expired');
    console.error('   - SSL/TLS connection issues');
    console.error('');
    return false;
  }
};

// Test specific table operations
window.testAreaOfExpertise = async () => {
  try {
    console.log('🧪 Testing Area of Expertise operations...');
    const { sql } = await import('./src/config/database.js');
    
    // Test read
    const areas = await sql`SELECT * FROM area_of_expertise LIMIT 5`;
    console.log('✅ Read test passed, records:', areas.length);
    
    // Test write
    const testRecord = await sql`
      INSERT INTO area_of_expertise (name, description) 
      VALUES ('TEST_AREA', 'Test description')
      ON CONFLICT (name) DO UPDATE SET description = EXCLUDED.description
      RETURNING *
    `;
    console.log('✅ Write test passed, record:', testRecord[0]);
    
    // Clean up
    await sql`DELETE FROM area_of_expertise WHERE name = 'TEST_AREA'`;
    console.log('✅ Cleanup completed');
    
    return true;
  } catch (error) {
    console.error('❌ Area of Expertise test failed:', error);
    return false;
  }
};

// Test services operations
window.testServices = async () => {
  try {
    console.log('🧪 Testing Services operations...');
    const { sql } = await import('./src/config/database.js');
    
    // Test read
    const services = await sql`SELECT * FROM services LIMIT 5`;
    console.log('✅ Read test passed, records:', services.length);
    
    // Test write
    const testRecord = await sql`
      INSERT INTO services (name, description) 
      VALUES ('TEST_SERVICE', 'Test description')
      ON CONFLICT (name) DO UPDATE SET description = EXCLUDED.description
      RETURNING *
    `;
    console.log('✅ Write test passed, record:', testRecord[0]);
    
    // Clean up
    await sql`DELETE FROM services WHERE name = 'TEST_SERVICE'`;
    console.log('✅ Cleanup completed');
    
    return true;
  } catch (error) {
    console.error('❌ Services test failed:', error);
    return false;
  }
};

// Fix startup benefits column
window.fixStartupBenefitsNow = async () => {
  try {
    console.log('🔧 Fixing startup_benefits column...');
    const { sql } = await import('./src/config/database.js');
    
    // Check if column exists
    const columnCheck = await sql`
      SELECT column_name 
      FROM information_schema.columns 
      WHERE table_name = 'vendors' AND column_name = 'startup_benefits'
    `;

    if (columnCheck.length > 0) {
      console.log('✅ startup_benefits column already exists');
      return true;
    }

    // Add the column
    await sql`
      ALTER TABLE vendors 
      ADD COLUMN startup_benefits VARCHAR(10) DEFAULT 'No'
    `;

    console.log('✅ startup_benefits column added successfully!');
    return true;
  } catch (error) {
    console.error('❌ Fix failed:', error);
    return false;
  }
};

console.log('🔧 NeonDB Test Functions Available:');
console.log('- window.testNeonDBNow() - Complete database test');
console.log('- window.testAreaOfExpertise() - Test area of expertise');
console.log('- window.testServices() - Test services');
console.log('- window.fixStartupBenefitsNow() - Fix startup benefits column');
console.log('');
console.log('🚀 Run: window.testNeonDBNow() to start comprehensive test');
