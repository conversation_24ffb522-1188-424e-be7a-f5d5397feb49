import { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
  ArrowLeftIcon,
  PencilIcon,
  PrinterIcon,
  DocumentArrowDownIcon,
  BuildingOfficeIcon,
  EnvelopeIcon,
  PhoneIcon,
  GlobeAltIcon,
  UserIcon,
  XMarkIcon,
  EyeIcon
} from '@heroicons/react/24/outline';
import { getVendorById } from '../services/prismaVendorService';
import FileViewer from '../components/FileViewer/FileViewer';
import { getWorkingFileUrl } from '../services/fileUploadService';
import { openFileWithFallbacks } from '../utils/fileUtils';

const VendorView = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const [vendor, setVendor] = useState(null);
  const [loading, setLoading] = useState(true);
  const [selectedDocument, setSelectedDocument] = useState(null);
  const [showDocumentViewer, setShowDocumentViewer] = useState(false);
  const [companyLogoUrl, setCompanyLogoUrl] = useState(null);

  useEffect(() => {
    fetchVendor();
  }, [id]);

  // Load company logo when vendor data changes
  useEffect(() => {
    if (vendor) {
      getCompanyLogo().then(logoUrl => {
        console.log('🖼️ Company logo loaded:', logoUrl);
        setCompanyLogoUrl(logoUrl);
      });
    }
  }, [vendor]);

  const fetchVendor = async () => {
    try {
      setLoading(true);
      console.log('🔄 Fetching vendor with ID:', id);
      
      const vendorData = await getVendorById(id);
      console.log('📦 Raw vendor data:', vendorData);

      if (vendorData) {
        // Transform Prisma service data to display format
        const transformedVendor = {
          id: vendorData.id,
          name: vendorData.companyName || 'Unknown Vendor',
          email: Array.isArray(vendorData.emails) && vendorData.emails.length > 0
            ? vendorData.emails[0]
            : vendorData.email || 'N/A',
          phone: Array.isArray(vendorData.phones) && vendorData.phones.length > 0
            ? vendorData.phones[0]
            : vendorData.phone || 'N/A',
          contactPerson: vendorData.username || 'N/A',
          businessType: vendorData.companyType || 'N/A',
          joinDate: vendorData.onboardingDate || 'N/A',
          taxId: vendorData.gstNumber || 'N/A',

          website: vendorData.website || 'N/A',
          totalOrders: vendorData.totalOrders || 0,
          typeOfWork: (() => {
            const workTypes = vendorData.typeOfWork;
            if (Array.isArray(workTypes) && workTypes.length > 0) {
              return workTypes.map(type => type.name || type).join(', ');
            } else if (typeof workTypes === 'string' && workTypes) {
              try {
                const parsed = JSON.parse(workTypes);
                return Array.isArray(parsed) ? parsed.map(type => type.name || type).join(', ') : workTypes;
              } catch {
                return workTypes;
              }
            }
            return 'N/A';
          })(),
          status: vendorData.status || 'Active',
          address: vendorData.address || 'N/A',
          city: vendorData.city || '',
          state: vendorData.state || '',
          country: vendorData.country || '',
          description: vendorData.description || '',
          allEmails: Array.isArray(vendorData.emails) ? vendorData.emails : [vendorData.email].filter(Boolean),
          allPhones: Array.isArray(vendorData.phones) ? vendorData.phones : [vendorData.phone].filter(Boolean),
          files: vendorData.files || {},
          services: (() => {
            const services = vendorData.services;
            if (Array.isArray(services)) {
              return services;
            } else if (typeof services === 'string' && services) {
              try {
                return JSON.parse(services);
              } catch {
                return [services];
              }
            }
            return [];
          })(),
          pointOfContact: (() => {
            const contacts = vendorData.pointOfContact || vendorData.point_of_contact;
            if (Array.isArray(contacts)) {
              return contacts;
            } else if (typeof contacts === 'string' && contacts) {
              try {
                return JSON.parse(contacts);
              } catch {
                return [];
              }
            }
            return [];
          })(),
          startupBenefits: vendorData.startupBenefits || vendorData.startup_benefits || 'No'
        };

        console.log('✅ Transformed vendor data:', transformedVendor);
        setVendor(transformedVendor);
      } else {
        setVendor(null);
      }
    } catch (error) {
      console.error('❌ Error fetching vendor:', error);
      setVendor(null);
    } finally {
      setLoading(false);
    }
  };



  const handleDocumentClick = async (documentType, documentUrl) => {
    try {
      console.log('🔍 Attempting to open document:', { documentType, documentUrl });

      if (!documentUrl) {
        console.error('❌ No document URL provided');
        alert('Document URL is not available.');
        return;
      }

      // Debug S3 access if it's an S3 URL
      if (documentUrl.includes('s3.amazonaws.com') || documentUrl.includes('amazonaws.com')) {
        const { logS3Debug, extractS3Key } = await import('../utils/s3Debug');
        await logS3Debug(documentUrl);
      }

      // Use enhanced file opening with multiple fallback strategies
      const filename = `${documentType}.${documentUrl.split('.').pop()}`;
      const opened = await openFileWithFallbacks(documentUrl, filename);

      if (opened) {
        console.log('✅ Document opened successfully');
        return;
      }

      // If all strategies failed, fall back to modal viewer
      console.log('⚠️ All opening strategies failed, trying signed URL approach');

      let workingUrl = documentUrl;

      // If it's an S3 URL, try to generate a signed URL for private access
      if (documentUrl && (documentUrl.includes('s3.amazonaws.com') || documentUrl.includes('amazonaws.com'))) {
        try {
          // Extract the S3 key from the URL
          const { extractS3Key } = await import('../utils/s3Debug');
          const s3Key = extractS3Key(documentUrl);

          if (s3Key) {
            console.log('🔑 Extracted S3 key:', s3Key);

            // Import and use the signed URL service
            const { getSignedFileUrl } = await import('../services/s3Service');
            workingUrl = await getSignedFileUrl(s3Key);
            console.log('🔗 Generated signed URL:', workingUrl);

            // Try opening the signed URL
            const signedOpened = await openFileWithFallbacks(workingUrl, filename);
            if (signedOpened) {
              console.log('✅ Document opened with signed URL');
              return;
            }
          } else {
            console.warn('⚠️ Could not extract S3 key from URL');
          }
        } catch (signedUrlError) {
          console.warn('⚠️ Failed to generate signed URL:', signedUrlError);
        }
      } else {
        // For non-S3 URLs, try to get a working URL
        try {
          workingUrl = await getWorkingFileUrl(documentUrl);
          console.log('🔗 Resolved working URL:', workingUrl);
        } catch (resolveError) {
          console.warn('⚠️ Failed to resolve URL, using original:', resolveError);
          workingUrl = documentUrl;
        }
      }

      if (workingUrl) {
        setSelectedDocument({
          type: documentType,
          url: workingUrl,
          name: documentType
        });
        setShowDocumentViewer(true);
        console.log('✅ Document viewer opened successfully');
      } else {
        console.error('❌ Could not resolve file URL for:', documentUrl);
        alert('Sorry, this document is not available for viewing.');
      }
    } catch (error) {
      console.error('❌ Error opening document:', error);

      // Show user-friendly error message with troubleshooting info
      const errorMessage = `Failed to open ${documentType}.\n\n` +
        `Error: ${error.message}\n\n` +
        `This might be due to:\n` +
        `• File access permissions (S3 bucket settings)\n` +
        `• Network connectivity issues\n` +
        `• File may have been moved or deleted\n\n` +
        `Please contact support if this issue persists.`;

      alert(errorMessage);
    }
  };

  const closeDocumentViewer = () => {
    setShowDocumentViewer(false);
    setSelectedDocument(null);
  };

  const getCompanyLogo = async () => {
    try {
      console.log('🔍 Getting company logo for vendor:', vendor?.files);

      let logoUrl = null;

      // Check if vendor has uploaded logo files (S3 URLs)
      if (vendor?.files?.companyLogos && Array.isArray(vendor.files.companyLogos) && vendor.files.companyLogos.length > 0) {
        const logoFile = vendor.files.companyLogos[0];
        logoUrl = logoFile.url || logoFile;
        console.log('✅ Found company logo in array:', logoUrl);
      }
      // Check if vendor has a single logo file URL
      else if (vendor?.files?.companyLogoUrl) {
        logoUrl = vendor.files.companyLogoUrl;
        console.log('✅ Found company logo URL:', logoUrl);
      }
      // Check legacy logo field
      else if (vendor?.files?.logoUrl) {
        logoUrl = vendor.files.logoUrl;
        console.log('✅ Found legacy logo URL:', logoUrl);
      }

      // If it's an S3 URL, try to generate a signed URL
      if (logoUrl && (logoUrl.includes('s3.amazonaws.com') || logoUrl.includes('amazonaws.com'))) {
        try {
          const urlParts = logoUrl.split('/');
          const bucketIndex = urlParts.findIndex(part => part.includes('s3.amazonaws.com'));
          if (bucketIndex !== -1 && urlParts.length > bucketIndex + 1) {
            const s3Key = urlParts.slice(bucketIndex + 1).join('/');
            console.log('🔑 Generating signed URL for logo key:', s3Key);

            const { getSignedFileUrl } = await import('../services/s3Service');
            const signedUrl = await getSignedFileUrl(s3Key);
            console.log('🔗 Generated signed URL for logo:', signedUrl);
            return signedUrl;
          }
        } catch (signedUrlError) {
          console.warn('⚠️ Failed to generate signed URL for logo, using direct URL:', signedUrlError);
        }
      }

      return logoUrl;
    } catch (error) {
      console.error('❌ Error getting company logo:', error);
      return null;
    }
  };



  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
          <p className="mt-4 text-gray-600">Loading vendor details...</p>
        </div>
      </div>
    );
  }

  if (!vendor) {
    return (
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">Vendor Not Found</h1>
          <p className="text-gray-600 mb-8">The vendor you're looking for doesn't exist.</p>
          <button
            onClick={() => navigate('/vendors')}
            className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700"
          >
            <ArrowLeftIcon className="h-4 w-4 mr-2" />
            Back to Vendors
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      {/* Header */}
      <div className="mb-8">
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <button
              onClick={() => navigate('/vendors')}
              className="flex items-center text-gray-600 hover:text-gray-900 mb-4 transition-colors"
            >
              <ArrowLeftIcon className="h-5 w-5 mr-2" />
              Back to Vendors
            </button>
            <div className="flex items-center">
              {/* Company Logo - Positioned on the left */}
              <div className="flex-shrink-0 mr-6">
                {companyLogoUrl ? (
                  <img
                    src={companyLogoUrl}
                    alt={`${vendor.name} logo`}
                    className="w-20 h-20 rounded-lg object-cover border-2 border-gray-200 shadow-lg"
                    onError={(e) => {
                      console.log('❌ Logo failed to load, showing fallback');
                      e.target.style.display = 'none';
                      e.target.nextSibling.style.display = 'flex';
                    }}
                  />
                ) : null}
                {/* Fallback Logo */}
                <div
                  className={`w-20 h-20 rounded-lg bg-gradient-to-br from-blue-500 to-blue-600 flex items-center justify-center text-white font-bold text-2xl shadow-lg ${companyLogoUrl ? 'hidden' : 'flex'}`}
                  style={{ display: companyLogoUrl ? 'none' : 'flex' }}
                >
                  {vendor.name ? vendor.name.charAt(0).toUpperCase() : 'V'}
                </div>
              </div>

              {/* Company Info - Next to logo */}
              <div className="flex-1">
                <div className="flex items-center space-x-3 mb-2">
                  <h1 className="text-3xl font-bold text-gray-900">{vendor.name}</h1>
                  <span className={`inline-flex px-3 py-1 text-sm font-semibold rounded-full ${
                    vendor.status === 'Active' ? 'bg-green-100 text-green-800' :
                    vendor.status === 'Pending' ? 'bg-yellow-100 text-yellow-800' : 'bg-red-100 text-red-800'
                  }`}>
                    {vendor.status}
                  </span>
                </div>
                <p className="text-gray-600 text-lg mb-1">{vendor.businessType}</p>
                <p className="text-gray-500">Vendor details and performance overview</p>
              </div>
            </div>
          </div>
          <div className="flex space-x-3 ml-6">
            <button
              onClick={() => window.print()}
              className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
            >
              <PrinterIcon className="h-4 w-4 mr-2" />
              Print
            </button>
            <button
              onClick={() => navigate(`/vendors/${vendor.id}/edit`)}
              className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700"
            >
              <PencilIcon className="h-4 w-4 mr-2" />
              Edit Vendor
            </button>
          </div>
        </div>
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-8">
        <div className="bg-white rounded-lg border border-gray-200 p-4">
          <div className="text-sm font-medium text-gray-500">Status</div>
          <div className="mt-1">
            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
              vendor.status === 'Active' ? 'bg-green-100 text-green-800' :
              vendor.status === 'Pending' ? 'bg-yellow-100 text-yellow-800' :
              vendor.status === 'Inactive' ? 'bg-gray-100 text-gray-800' :
              'bg-red-100 text-red-800'
            }`}>
              {vendor.status}
            </span>
          </div>
        </div>

        <div className="bg-white rounded-lg border border-gray-200 p-4">
          <div className="text-sm font-medium text-gray-500">Total Orders</div>
          <div className="mt-1 text-2xl font-semibold text-gray-900">{vendor.totalOrders}</div>
        </div>

        <div className="bg-white rounded-lg border border-gray-200 p-4">
          <div className="text-sm font-medium text-gray-500">Services</div>
          <div className="mt-1 text-2xl font-semibold text-gray-900">
            {vendor.services ? vendor.services.length : 0}
          </div>
        </div>

        <div className="bg-white rounded-lg border border-gray-200 p-4">
          <div className="text-sm font-medium text-gray-500">Startup Benefits</div>
          <div className="mt-1">
            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
              vendor.startupBenefits === 'Yes' ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'
            }`}>
              {vendor.startupBenefits}
            </span>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Vendor Overview */}
        <div className="lg:col-span-2">
          <div className="card">
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-xl font-semibold text-gray-900">Vendor Overview</h2>
              <div className="flex items-center space-x-4">
                <span className={`status-badge ${
                  vendor.status === 'Active' ? 'status-active' :
                  vendor.status === 'Pending' ? 'status-pending' : 'status-inactive'
                }`}>
                  {vendor.status}
                </span>
              </div>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              {/* Left Column */}
              <div className="space-y-4">
                <div className="vendor-info-item">
                  <BuildingOfficeIcon className="h-5 w-5 text-gray-400 icon" />
                  <div className="content">
                    <p className="vendor-info-label">Business Type</p>
                    <p className="vendor-info-value">{vendor.businessType}</p>
                  </div>
                </div>
                
                <div className="vendor-info-item">
                  <EnvelopeIcon className="h-5 w-5 text-gray-400 icon" />
                  <div className="content">
                    <p className="vendor-info-label">Email{vendor.allEmails && vendor.allEmails.length > 1 ? 's' : ''}</p>
                    {vendor.allEmails && vendor.allEmails.length > 0 ? (
                      <div className="space-y-1">
                        {vendor.allEmails.map((email, index) => (
                          <p key={index} className="vendor-info-value">{email}</p>
                        ))}
                      </div>
                    ) : (
                      <p className="vendor-info-value">{vendor.email}</p>
                    )}
                  </div>
                </div>

                <div className="vendor-info-item">
                  <PhoneIcon className="h-5 w-5 text-gray-400 icon" />
                  <div className="content">
                    <p className="vendor-info-label">Phone{vendor.allPhones && vendor.allPhones.length > 1 ? 's' : ''}</p>
                    {vendor.allPhones && vendor.allPhones.length > 0 ? (
                      <div className="space-y-1">
                        {vendor.allPhones.map((phone, index) => (
                          <p key={index} className="vendor-info-value">{phone}</p>
                        ))}
                      </div>
                    ) : (
                      <p className="vendor-info-value">{vendor.phone}</p>
                    )}
                  </div>
                </div>
                
                <div className="vendor-info-item">
                  <UserIcon className="h-5 w-5 text-gray-400 icon" />
                  <div className="content">
                    <p className="vendor-info-label">Contact Person</p>
                    <p className="vendor-info-value">{vendor.contactPerson}</p>
                  </div>
                </div>
              </div>
              
              {/* Right Column */}
              <div className="space-y-4">
                <div className="vendor-info-item">
                  <GlobeAltIcon className="h-5 w-5 text-gray-400 icon" />
                  <div className="content">
                    <p className="vendor-info-label">Website</p>
                    {vendor.website && vendor.website !== 'N/A' ? (
                      <a href={vendor.website.startsWith('http') ? vendor.website : `https://${vendor.website}`} 
                         target="_blank" rel="noopener noreferrer" 
                         className="vendor-info-value text-blue-600 hover:text-blue-800">
                        {vendor.website}
                      </a>
                    ) : (
                      <p className="vendor-info-value">N/A</p>
                    )}
                  </div>
                </div>
                
                <div className="vendor-info-item">
                  <div className="content">
                    <p className="vendor-info-label">GST Number</p>
                    <p className="vendor-info-value font-mono">{vendor.taxId}</p>
                  </div>
                </div>

                <div className="vendor-info-item">
                  <div className="content">
                    <p className="vendor-info-label">Status</p>
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                      vendor.status === 'Active' ? 'bg-green-100 text-green-800' :
                      vendor.status === 'Pending' ? 'bg-yellow-100 text-yellow-800' :
                      vendor.status === 'Inactive' ? 'bg-gray-100 text-gray-800' :
                      'bg-red-100 text-red-800'
                    }`}>
                      {vendor.status}
                    </span>
                  </div>
                </div>
                
                <div className="vendor-info-item">
                  <div className="content">
                    <p className="vendor-info-label">Onboarding Date</p>
                    <p className="vendor-info-value">{vendor.joinDate}</p>
                  </div>
                </div>

                <div className="vendor-info-item">
                  <div className="content">
                    <p className="vendor-info-label">Username</p>
                    <p className="vendor-info-value">{vendor.contactPerson}</p>
                  </div>
                </div>

                <div className="vendor-info-item">
                  <div className="content">
                    <p className="vendor-info-label">Startup Benefits</p>
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                      vendor.startupBenefits === 'Yes' ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'
                    }`}>
                      {vendor.startupBenefits}
                    </span>
                  </div>
                </div>
                
                <div className="vendor-info-item">
                  <div className="content">
                    <p className="vendor-info-label">Total Orders</p>
                    <p className="vendor-info-value font-semibold">{vendor.totalOrders}</p>
                  </div>
                </div>

                <div className="vendor-info-item">
                  <div className="content">
                    <p className="vendor-info-label">Type of Work</p>
                    <div className="vendor-info-value">
                      {(() => {
                        const workTypes = vendor.typeOfWork;
                        if (Array.isArray(workTypes) && workTypes.length > 0) {
                          return (
                            <div className="flex flex-wrap gap-2">
                              {workTypes.map((type, index) => (
                                <span
                                  key={index}
                                  className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
                                >
                                  {type.name || type}
                                </span>
                              ))}
                            </div>
                          );
                        } else if (typeof workTypes === 'string' && workTypes && workTypes !== 'N/A') {
                          try {
                            const parsed = JSON.parse(workTypes);
                            if (Array.isArray(parsed)) {
                              return (
                                <div className="flex flex-wrap gap-2">
                                  {parsed.map((type, index) => (
                                    <span
                                      key={index}
                                      className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
                                    >
                                      {type.name || type}
                                    </span>
                                  ))}
                                </div>
                              );
                            } else {
                              return <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">{workTypes}</span>;
                            }
                          } catch {
                            return <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">{workTypes}</span>;
                          }
                        }
                        return <span className="text-gray-500">N/A</span>;
                      })()}
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Description Section */}
            {vendor.description && vendor.description !== 'N/A' && vendor.description.trim() !== '' && (
              <div className="mt-8 pt-6 border-t border-gray-200">
                <h3 className="text-sm font-medium text-gray-500 mb-3">Description</h3>
                <div className="bg-gray-50 rounded-lg p-4">
                  <p className="text-base text-gray-900 leading-relaxed break-words whitespace-pre-wrap overflow-hidden">{vendor.description}</p>
                </div>
              </div>
            )}

            {/* Services Offered Section */}
            {vendor.services && Array.isArray(vendor.services) && vendor.services.length > 0 && (
              <div className="mt-8 pt-6 border-t border-gray-200">
                <h3 className="text-sm font-medium text-gray-500 mb-3">Services Offered</h3>
                <div className="bg-gray-50 rounded-lg p-4">
                  <div className="flex flex-wrap gap-2">
                    {vendor.services.map((service, index) => (
                      <span
                        key={index}
                        className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800"
                      >
                        {service}
                      </span>
                    ))}
                  </div>
                </div>
              </div>
            )}

            {/* Point of Contact Section */}
            {vendor.pointOfContact && Array.isArray(vendor.pointOfContact) && vendor.pointOfContact.length > 0 && (
              <div className="mt-8 pt-6 border-t border-gray-200">
                <h3 className="text-sm font-medium text-gray-500 mb-3">Point of Contact</h3>
                <div className="bg-gray-50 rounded-lg p-4">
                  <div className="space-y-4">
                    {vendor.pointOfContact.map((contact, index) => (
                      <div key={index} className="bg-white rounded-lg p-4 border border-gray-200">
                        <div className="flex items-start justify-between">
                          <div className="flex-1">
                            <h4 className="text-lg font-medium text-gray-900">{contact.name}</h4>
                            <div className="mt-2 space-y-1">
                              <div className="flex items-center text-sm text-gray-600">
                                <span className="font-medium w-20">Phone:</span>
                                <span>{contact.phone}</span>
                              </div>
                              <div className="flex items-center text-sm text-gray-600">
                                <span className="font-medium w-20">Email:</span>
                                <span>{contact.email}</span>
                              </div>
                              <div className="flex items-center text-sm text-gray-600">
                                <span className="font-medium w-20">Expertise:</span>
                                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                  {(() => {
                                    // Handle both ID and name formats
                                    if (typeof contact.areaOfExpertise === 'object' && contact.areaOfExpertise.name) {
                                      return contact.areaOfExpertise.name;
                                    }
                                    return contact.areaOfExpertise || 'N/A';
                                  })()}
                                </span>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            )}

            {/* Location & Address Section */}
            <div className="mt-8 pt-6 border-t border-gray-200">
              <h3 className="text-sm font-medium text-gray-500 mb-3">Location & Address</h3>
              <div className="bg-gray-50 rounded-lg p-4 space-y-3">
                {/* Location Info */}
                {(vendor.city || vendor.state || vendor.country) && (
                  <div>
                    <p className="text-sm font-medium text-gray-700 mb-1">Location</p>
                    <p className="text-base text-gray-900 font-medium">
                      {[vendor.city, vendor.state, vendor.country].filter(Boolean).join(', ')}
                    </p>
                  </div>
                )}

                {/* Full Address */}
                {vendor.address && vendor.address !== 'N/A' && (
                  <div>
                    <p className="text-sm font-medium text-gray-700 mb-1">Full Address</p>
                    <p className="text-base text-gray-900 leading-relaxed break-words overflow-hidden">{vendor.address}</p>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Documents & Files */}
          <div className="card">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Documents & Files</h3>
            <div className="space-y-3">

              {/* Company Logos Section */}
              {vendor.files?.companyLogos && Array.isArray(vendor.files.companyLogos) && vendor.files.companyLogos.length > 0 && (
                <div className="border-b border-gray-200 pb-3 mb-3">
                  <h4 className="text-sm font-medium text-gray-700 mb-2">Company Logos</h4>
                  {vendor.files.companyLogos.map((logo, index) => (
                    <div key={index} className="document-file-item cursor-pointer hover:bg-gray-50 transition-colors rounded-lg p-3 mb-2"
                         onClick={() => handleDocumentClick(`Company Logo ${index + 1}`, logo.url || logo)}>
                      <div className="flex items-center space-x-3">
                        <div className="w-8 h-8 rounded bg-blue-100 flex items-center justify-center">
                          <img src={logo.url || logo} alt="Logo" className="w-6 h-6 rounded object-cover"
                               onError={(e) => e.target.style.display = 'none'} />
                        </div>
                        <div className="flex-1">
                          <p className="text-sm font-medium text-gray-900">{logo.originalName || `Company Logo ${index + 1}`}</p>
                          <p className="text-xs text-gray-500">Click to view • Downloadable</p>
                        </div>
                        <EyeIcon className="h-4 w-4 text-blue-600" />
                      </div>
                    </div>
                  ))}
                </div>
              )}

              {/* GST Document */}
              {vendor.files?.gstFileUrl && (
                <div className="document-file-item cursor-pointer hover:bg-gray-50 transition-colors rounded-lg p-3"
                     onClick={() => handleDocumentClick('GST Certificate', vendor.files.gstFileUrl)}>
                  <div className="flex items-center space-x-3">
                    <DocumentArrowDownIcon className="h-5 w-5 text-green-600" />
                    <div className="flex-1">
                      <p className="text-sm font-medium text-gray-900">GST Certificate</p>
                      <p className="text-xs text-gray-500">Click to view • Downloadable</p>
                    </div>
                    <EyeIcon className="h-4 w-4 text-blue-600" />
                  </div>
                </div>
              )}

              {/* NDA Document */}
              {vendor.files?.ndaFileUrl && (
                <div className="document-file-item cursor-pointer hover:bg-gray-50 transition-colors rounded-lg p-3"
                     onClick={() => handleDocumentClick('NDA Document', vendor.files.ndaFileUrl)}>
                  <div className="flex items-center space-x-3">
                    <DocumentArrowDownIcon className="h-5 w-5 text-purple-600" />
                    <div className="flex-1">
                      <p className="text-sm font-medium text-gray-900">NDA Document</p>
                      <p className="text-xs text-gray-500">Click to view • Downloadable</p>
                    </div>
                    <EyeIcon className="h-4 w-4 text-blue-600" />
                  </div>
                </div>
              )}

              {/* Agreement Document */}
              {vendor.files?.agreementFileUrl && (
                <div className="document-file-item cursor-pointer hover:bg-gray-50 transition-colors rounded-lg p-3"
                     onClick={() => handleDocumentClick('Agreement Document', vendor.files.agreementFileUrl)}>
                  <div className="flex items-center space-x-3">
                    <DocumentArrowDownIcon className="h-5 w-5 text-orange-600" />
                    <div className="flex-1">
                      <p className="text-sm font-medium text-gray-900">Agreement Document</p>
                      <p className="text-xs text-gray-500">Click to view • Downloadable</p>
                    </div>
                    <EyeIcon className="h-4 w-4 text-blue-600" />
                  </div>
                </div>
              )}

              {/* Other Documents */}
              {vendor.files?.otherDocsUrls && Array.isArray(vendor.files.otherDocsUrls) && vendor.files.otherDocsUrls.map((docUrl, index) => (
                <div key={index} className="document-file-item cursor-pointer hover:bg-gray-50 transition-colors rounded-lg p-3"
                     onClick={() => handleDocumentClick(`Other Document ${index + 1}`, docUrl)}>
                  <div className="flex items-center space-x-3">
                    <DocumentArrowDownIcon className="h-5 w-5 text-gray-600" />
                    <div className="flex-1">
                      <p className="text-sm font-medium text-gray-900">Other Document {index + 1}</p>
                      <p className="text-xs text-gray-500">Click to view • Downloadable</p>
                    </div>
                    <EyeIcon className="h-4 w-4 text-blue-600" />
                  </div>
                </div>
              ))}

              {/* No Documents Message */}
              {(!vendor.files?.gstFileUrl &&
                !vendor.files?.ndaFileUrl &&
                !vendor.files?.agreementFileUrl &&
                (!vendor.files?.companyLogos || vendor.files.companyLogos.length === 0) &&
                (!vendor.files?.otherDocsUrls || vendor.files.otherDocsUrls.length === 0)) && (
                <div className="text-center py-8">
                  <DocumentArrowDownIcon className="h-12 w-12 mx-auto mb-3 text-gray-400" />
                  <p className="text-sm text-gray-500 font-medium">No documents uploaded</p>
                  <p className="text-xs text-gray-400 mt-1">Documents will appear here when uploaded</p>

                  {/* Test Document Viewer Button */}
                  <button
                    onClick={() => handleDocumentClick('Test Document', 'https://www.w3.org/WAI/ER/tests/xhtml/testfiles/resources/pdf/dummy.pdf')}
                    className="mt-4 inline-flex items-center px-3 py-2 bg-blue-600 text-white text-xs rounded hover:bg-blue-700"
                  >
                    Test Document Viewer
                  </button>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Document Viewer Modal */}
      {showDocumentViewer && selectedDocument && (
        <div className="fixed inset-0 z-50 overflow-y-auto">
          <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            {/* Background overlay */}
            <div
              className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity"
              onClick={closeDocumentViewer}
            ></div>

            {/* Modal panel */}
            <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-4xl sm:w-full">
              {/* Header */}
              <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                <div className="flex items-center justify-between">
                  <h3 className="text-lg leading-6 font-medium text-gray-900">
                    {selectedDocument.name}
                  </h3>
                  <button
                    onClick={closeDocumentViewer}
                    className="bg-white rounded-md text-gray-400 hover:text-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                  >
                    <XMarkIcon className="h-6 w-6" />
                  </button>
                </div>
              </div>

              {/* Document content */}
              <div className="bg-gray-50 px-4 py-3 sm:px-6" style={{ height: '70vh' }}>
                {selectedDocument.url.toLowerCase().includes('.pdf') || selectedDocument.url.toLowerCase().endsWith('.pdf') ? (
                  <iframe
                    src={selectedDocument.url}
                    className="w-full h-full border-0 rounded"
                    title={selectedDocument.name}
                    onError={(e) => {
                      console.error('PDF iframe failed to load:', e);
                      e.target.style.display = 'none';
                      e.target.nextSibling.style.display = 'block';
                    }}
                  />
                ) : selectedDocument.url.match(/\.(jpg|jpeg|png|gif|bmp|webp|svg)$/i) ? (
                  <div className="flex items-center justify-center h-full">
                    <img
                      src={selectedDocument.url}
                      alt={selectedDocument.name}
                      className="max-w-full max-h-full object-contain rounded shadow-lg"
                      onError={(e) => {
                        console.error('Image failed to load:', e);
                        e.target.style.display = 'none';
                        e.target.nextSibling.style.display = 'block';
                      }}
                    />
                    <div className="text-center hidden">
                      <DocumentArrowDownIcon className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                      <p className="text-gray-600 font-medium">Image failed to load</p>
                      <p className="text-sm text-gray-500 mt-2">{selectedDocument.name}</p>
                    </div>
                  </div>
                ) : (
                  <div className="flex items-center justify-center h-full">
                    <div className="text-center">
                      <DocumentArrowDownIcon className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                      <p className="text-gray-600 font-medium">{selectedDocument.name}</p>
                      <p className="text-sm text-gray-500 mt-2">Preview not available for this file type</p>
                      <div className="mt-4 space-x-3">
                        <a
                          href={selectedDocument.url}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
                        >
                          <EyeIcon className="h-4 w-4 mr-2" />
                          Open in New Tab
                        </a>
                        <a
                          href={selectedDocument.url}
                          download
                          className="inline-flex items-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700"
                        >
                          <DocumentArrowDownIcon className="h-4 w-4 mr-2" />
                          Download File
                        </a>
                      </div>
                    </div>
                  </div>
                )}

                {/* Fallback error display for PDF */}
                <div className="hidden flex items-center justify-center h-full">
                  <div className="text-center">
                    <DocumentArrowDownIcon className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                    <p className="text-gray-600 font-medium">Unable to display document</p>
                    <p className="text-sm text-gray-500 mt-2">{selectedDocument.name}</p>
                    <div className="mt-4 space-x-3">
                      <a
                        href={selectedDocument.url}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
                      >
                        <EyeIcon className="h-4 w-4 mr-2" />
                        Open in New Tab
                      </a>
                      <a
                        href={selectedDocument.url}
                        download
                        className="inline-flex items-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700"
                      >
                        <DocumentArrowDownIcon className="h-4 w-4 mr-2" />
                        Download File
                      </a>
                    </div>
                  </div>
                </div>
              </div>

              {/* Footer */}
              <div className="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                <button
                  type="button"
                  onClick={closeDocumentViewer}
                  className="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-blue-600 text-base font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:ml-3 sm:w-auto sm:text-sm"
                >
                  Close
                </button>
                <a
                  href={selectedDocument.url}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
                >
                  Open in New Tab
                </a>
                <a
                  href={selectedDocument.url}
                  download
                  className="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-green-600 text-base font-medium text-white hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
                >
                  Download
                </a>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default VendorView;
