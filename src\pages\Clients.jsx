import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { PlusIcon, EyeIcon, PencilIcon, TrashIcon } from '@heroicons/react/24/outline';
import DataTable from '../components/DataTable/DataTable';
import FileUpload from '../components/FileUpload/FileUpload';
import S3FileUpload from '../components/FileUpload/S3FileUpload';
import { getAllStates, getCitiesByStateName, getAllCountries } from '../services/locationService';
import LocationSelector from '../components/LocationSelector/LocationSelector';
import MultiSelect from '../components/MultiSelect/MultiSelect';
import { getActiveCompanyTypes } from '../services/companyTypeService';
import { validateEmails, validatePhones, validateCompanyName, validateEmail, validatePhone, getPhonePlaceholder } from '../utils/validation';

// Country-State-City data
const countryStateCity = {
  'India': {
    'Maharashtra': ['Mumbai', 'Pune', 'Nagpur', 'Nashik', 'Aurangabad'],
    'Karnataka': ['Bangalore', 'Mysore', '<PERSON>bli', 'Mangalore', 'Belgaum'],
    'Tamil Nadu': ['Chennai', 'Coimbatore', 'Madurai', 'Tiruchirappalli', 'Salem'],
    'Gujarat': ['Ahmedabad', 'Surat', 'Vadodara', 'Rajkot', 'Bhavnagar'],
    'Delhi': ['New Delhi', 'Central Delhi', 'North Delhi', 'South Delhi', 'East Delhi']
  },
  'United States': {
    'California': ['Los Angeles', 'San Francisco', 'San Diego', 'Sacramento', 'San Jose'],
    'New York': ['New York City', 'Buffalo', 'Rochester', 'Syracuse', 'Albany'],
    'Texas': ['Houston', 'Dallas', 'Austin', 'San Antonio', 'Fort Worth'],
    'Florida': ['Miami', 'Orlando', 'Tampa', 'Jacksonville', 'Tallahassee']
  },
  'United Kingdom': {
    'England': ['London', 'Manchester', 'Birmingham', 'Liverpool', 'Leeds'],
    'Scotland': ['Edinburgh', 'Glasgow', 'Aberdeen', 'Dundee', 'Stirling'],
    'Wales': ['Cardiff', 'Swansea', 'Newport', 'Bangor', 'St. Davids']
  }
};

const Clients = () => {
  const navigate = useNavigate();
  const [showAddForm, setShowAddForm] = useState(false);
  const [deleteLoading, setDeleteLoading] = useState(false);
  const [formData, setFormData] = useState({
    onboardingDate: '',
    companyType: '',
    companyName: '',
    emails: [''],
    phones: [''],
    address: '',
    country: '',
    state: '',
    city: '',
    username: '',
    gstNumber: '',
    dpiitRegistered: '',
    validTill: '',
    website: '',
    description: '',
    creditLimit: '',
    paymentTerms: ''
  });
  const [uploadedFiles, setUploadedFiles] = useState({
    dpiitCertificate: [],
    tdsFile: [],
    gstFile: [],
    ndaFile: [],
    agreementFile: [],
    quotationFile: [],
    panCardFile: [],
    udhyamRegistrationFile: [],
    othersFile: []
  });
  const [tempClientId] = useState(() => `temp-client-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`);
  const [availableStates, setAvailableStates] = useState([]);
  const [availableCities, setAvailableCities] = useState([]);
  const [companyTypeOptions, setCompanyTypeOptions] = useState([]);
  const [loadingStates, setLoadingStates] = useState(false);
  const [loadingCities, setLoadingCities] = useState(false);
  const [locationData, setLocationData] = useState({});

  // Client data from database only
  const [clients, setClients] = useState([]);
  const [loading, setLoading] = useState(true);
  const [validationErrors, setValidationErrors] = useState({});

  // Load clients from database
  useEffect(() => {
    const loadClients = async () => {
      try {
        setLoading(true);
        console.log('🔄 Loading clients from database...');
        // Import and use client service
        const { getAllClients } = await import('../services/clientService');
        const dbClients = await getAllClients();
        console.log('✅ Clients loaded:', dbClients);

        if (dbClients && dbClients.length > 0) {
          console.log('📊 First client structure:', dbClients[0]);
        }

        setClients(dbClients || []);
      } catch (err) {
        console.error('❌ Error loading clients:', err);
        setClients([]);
      } finally {
        setLoading(false);
      }
    };

    loadClients();
    loadStates();
    loadCompanyTypes();
  }, []);

  // Load company type options
  const loadCompanyTypes = async () => {
    try {
      console.log('🔄 Loading company types...');
      const companyTypes = await getActiveCompanyTypes();
      setCompanyTypeOptions(companyTypes);
      console.log(`✅ Loaded ${companyTypes.length} company types`);
    } catch (error) {
      console.error('❌ Error loading company types:', error);
    }
  };

  // Load states from local JSON
  const loadStates = async () => {
    try {
      setLoadingStates(true);
      console.log('🔄 Loading states from local JSON...');
      const states = await getAllStates();
      setAvailableStates(states);
      console.log(`✅ Loaded ${states.length} states`);
    } catch (error) {
      console.error('❌ Error loading states:', error);
    } finally {
      setLoadingStates(false);
    }
  };

  // Load cities for selected state
  const loadCities = async (stateName) => {
    try {
      setLoadingCities(true);
      console.log(`🔄 Loading cities for state ${stateName}...`);
      const cities = await getCitiesByStateName(stateName);
      setAvailableCities(cities);
      console.log(`✅ Loaded ${cities.length} cities`);
    } catch (error) {
      console.error('❌ Error loading cities:', error);
      setAvailableCities([]);
    } finally {
      setLoadingCities(false);
    }
  };



  // Handle client deletion
  const handleDeleteClient = async (clientId) => {
    if (!window.confirm('Are you sure you want to delete this client? This action cannot be undone.')) {
      return;
    }

    try {
      setDeleteLoading(true);
      console.log('🗑️ Deleting client:', clientId);

      // Import and use database service
      const { deleteClient } = await import('../services/clientService');
      await deleteClient(clientId);

      // Remove client from local state
      setClients(prevClients => prevClients.filter(client => client.id !== clientId));

      console.log('✅ Client deleted successfully');
      alert('Client deleted successfully!');
    } catch (error) {
      console.error('❌ Error deleting client:', error);
      alert('Failed to delete client. Please try again.');
    } finally {
      setDeleteLoading(false);
    }
  };

  const columns = [
    {
      key: 'company_name',
      label: 'Company Name',
      sortable: true,
      filterable: true
    },
    {
      key: 'company_type',
      label: 'Company Type',
      sortable: true,
      filterable: true
    },
    {
      key: 'email',
      label: 'Email',
      sortable: true,
      filterable: true,
      render: (value, row) => (
        <div>
          {Array.isArray(row.emails) ? row.emails[0] : row.email}
          {Array.isArray(row.emails) && row.emails.length > 1 && (
            <span className="text-xs text-gray-500 block">+{row.emails.length - 1} more</span>
          )}
        </div>
      )
    },
    {
      key: 'phone',
      label: 'Phone',
      sortable: false,
      filterable: true,
      render: (value, row) => (
        <div>
          {Array.isArray(row.phones) ? row.phones[0] : row.phone}
          {Array.isArray(row.phones) && row.phones.length > 1 && (
            <span className="text-xs text-gray-500 block">+{row.phones.length - 1} more</span>
          )}
        </div>
      )
    },
    {
      key: 'onboarding_date',
      label: 'Onboarding Date',
      sortable: true,
      filterable: true
    },
    {
      key: 'status',
      label: 'Status',
      sortable: true,
      filterable: true,
      render: (value) => (
        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
          value === 'Active'
            ? 'bg-green-100 text-green-800'
            : 'bg-red-100 text-red-800'
        }`}>
          {value}
        </span>
      )
    },
    {
      key: 'created_at',
      label: 'Join Date',
      sortable: true,
      filterable: false
    },
    {
      key: 'actions',
      label: 'Actions',
      sortable: false,
      filterable: false,
      render: (value, row) => (
        <div className="flex space-x-2">
          <button
            onClick={() => navigate(`/clients/${row.id}`)}
            className="text-blue-600 hover:text-blue-900 p-1 hover:bg-blue-50 rounded"
            title="View Client"
          >
            <EyeIcon className="h-4 w-4" />
          </button>
          <button
            onClick={() => navigate(`/clients/${row.id}/edit`)}
            className="text-green-600 hover:text-green-900 p-1 hover:bg-green-50 rounded"
            title="Edit Client"
          >
            <PencilIcon className="h-4 w-4" />
          </button>
          <button
            onClick={() => handleDeleteClient(row.id)}
            disabled={deleteLoading}
            className="text-red-600 hover:text-red-900 p-1 hover:bg-red-50 rounded disabled:opacity-50"
            title="Delete Client"
          >
            <TrashIcon className="h-4 w-4" />
          </button>
        </div>
      )
    }
  ];

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));

    // Clear validation errors when user starts typing
    if (validationErrors[name]) {
      setValidationErrors(prev => ({
        ...prev,
        [name]: null
      }));
    }

    // Handle country change
    if (name === 'country') {
      if (value === 'India') {
        // Load states for India from local JSON
        loadStates();
      } else {
        // For other countries, use fallback data
        setAvailableStates(Object.keys(countryStateCity[value] || {}));
      }
      setAvailableCities([]);
      setFormData(prev => ({
        ...prev,
        state: '',
        city: ''
      }));
    }

    // Handle state change
    if (name === 'state') {
      if (formData.country === 'India') {
        // Load cities from local JSON for Indian states
        loadCities(value);
      } else {
        // Use fallback data for other countries
        setAvailableCities(countryStateCity[formData.country]?.[value] || []);
      }
      setFormData(prev => ({
        ...prev,
        city: ''
      }));
    }
  };



  const addEmailField = () => {
    setFormData(prev => ({
      ...prev,
      emails: [...prev.emails, '']
    }));
  };

  const removeEmailField = (index) => {
    setFormData(prev => ({
      ...prev,
      emails: prev.emails.filter((_, i) => i !== index)
    }));
  };

  const handleEmailChange = (index, value) => {
    setFormData(prev => ({
      ...prev,
      emails: prev.emails.map((email, i) => i === index ? value : email)
    }));

    // Clear validation errors for emails when user starts typing
    if (validationErrors.emails) {
      setValidationErrors(prev => ({
        ...prev,
        emails: null
      }));
    }
  };

  const addPhoneField = () => {
    setFormData(prev => ({
      ...prev,
      phones: [...prev.phones, '']
    }));
  };

  const removePhoneField = (index) => {
    setFormData(prev => ({
      ...prev,
      phones: prev.phones.filter((_, i) => i !== index)
    }));
  };

  const handlePhoneChange = (index, value) => {
    setFormData(prev => ({
      ...prev,
      phones: prev.phones.map((phone, i) => i === index ? value : phone)
    }));

    // Clear validation errors for phones when user starts typing
    if (validationErrors.phones) {
      setValidationErrors(prev => ({
        ...prev,
        phones: null
      }));
    }
  };

  const handleFileUpload = (fileType, files) => {
    if (!files || files.length === 0) return;

    const maxSize = 10 * 1024 * 1024; // 10MB in bytes
    const validFiles = [];
    const errors = [];

    Array.from(files).forEach(file => {
      // Check file size
      if (file.size > maxSize) {
        errors.push(`${file.name} exceeds 10MB limit`);
        return;
      }

      // Handle duplicate filename by appending timestamp
      const existingFiles = uploadedFiles[fileType] || [];
      const existingNames = existingFiles.map(f => f.name);
      let fileName = file.name;

      if (existingNames.includes(fileName)) {
        const timestamp = new Date().getTime();
        const fileExtension = fileName.substring(fileName.lastIndexOf('.'));
        const fileNameWithoutExt = fileName.substring(0, fileName.lastIndexOf('.'));
        fileName = `${fileNameWithoutExt}_${timestamp}${fileExtension}`;
      }

      // Create new file object with updated name
      const newFile = new File([file], fileName, { type: file.type });
      validFiles.push(newFile);
    });

    if (errors.length > 0) {
      alert(errors.join('\n'));
    }

    if (validFiles.length > 0) {
      setUploadedFiles(prev => ({
        ...prev,
        [fileType]: [...(prev[fileType] || []), ...validFiles]
      }));
    }
  };

  const removeFile = (fileType, index) => {
    setUploadedFiles(prev => ({
      ...prev,
      [fileType]: prev[fileType].filter((_, i) => i !== index)
    }));
  };

  const handleFilesChange = (files) => {
    setUploadedFiles(files);
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    // Comprehensive validation
    setValidationErrors({});
    const errors = {};

    // Validate company name
    const companyValidation = validateCompanyName(formData.companyName);
    if (!companyValidation.isValid) {
      errors.companyName = companyValidation.errors;
    }

    // Validate emails
    const emailValidation = validateEmails(formData.emails);
    if (!emailValidation.isValid) {
      errors.emails = emailValidation.errors;
    }

    // Validate phone numbers
    const phoneValidation = validatePhones(formData.phones, 'IN');
    if (!phoneValidation.isValid) {
      errors.phones = phoneValidation.errors;
    }

    // Check if there are any validation errors
    if (Object.keys(errors).length > 0) {
      setValidationErrors(errors);

      // Show first error in alert
      const firstError = Object.values(errors)[0];
      const errorMessage = Array.isArray(firstError) ? firstError[0] : firstError;
      alert(`❌ Validation Error: ${errorMessage}`);
      return;
    }

    try {
      setLoading(true);
      console.log('🚀 Submitting client form...');
      console.log('📋 Form data:', formData);
      console.log('📁 Uploaded files:', uploadedFiles);

      // Create client object
      const clientData = {
        companyName: formData.companyName,
        companyType: formData.companyType,
        onboardingDate: formData.onboardingDate,
        emails: formData.emails.filter(email => email.trim() !== ''),
        phones: formData.phones.filter(phone => phone.trim() !== ''),
        address: formData.address,
        country: formData.country,
        state: formData.state,
        city: formData.city,
        dpiitRegistered: formData.dpiitRegistered === 'yes',
        dpiitNumber: formData.dpiitRegistered === 'yes' ? formData.validTill : null,
        files: uploadedFiles,
        status: 'Active'
      };

      console.log('💾 Sending client data to service:', clientData);

      // Save to database
      const { createClient } = await import('../services/clientService');
      const result = await createClient(clientData);
      console.log('✅ Client created successfully:', result);

      // Reload clients list
      const { getAllClients } = await import('../services/clientService');
      const updatedClients = await getAllClients();
      setClients(updatedClients);

      // Reset form
      setFormData({
        onboardingDate: '',
        companyType: '',
        companyName: '',
        emails: [''],
        phones: [''],
        address: '',
        country: '',
        state: '',
        city: '',
        username: '',
        gstNumber: '',
        dpiitRegistered: '',
        validTill: '',
        website: '',
        description: '',
        creditLimit: '',
        paymentTerms: ''
      });
      setUploadedFiles({
        dpiitCertificate: [],
        tdsFile: [],
        gstFile: [],
        ndaFile: [],
        agreementFile: [],
        quotationFile: [],
        panCardFile: [],
        udhyamRegistrationFile: [],
        othersFile: []
      });
      setAvailableStates([]);
      setAvailableCities([]);
      setShowAddForm(false);

      // Success notification
      alert('✅ Client added successfully! All files have been saved locally.');
    } catch (err) {
      console.error('❌ Error creating client:', err);
      alert(`❌ Failed to create client: ${err.message || 'Please try again.'}`);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex justify-between items-center border-b border-gray-200 pb-4">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Clients</h1>
          <p className="mt-2 text-gray-600">Manage your client relationships</p>
        </div>
        <div className="flex space-x-3">
          <button
            onClick={() => setShowAddForm(true)}
            className="btn-primary flex items-center"
          >
            <PlusIcon className="h-5 w-5 mr-2" />
            Add New Client
          </button>
        </div>
      </div>

      {/* Clients Table */}
      <DataTable
        data={clients}
        columns={columns}
        title="Clients Management"
        defaultPageSize={50}
        enableExport={true}
        enableColumnToggle={true}
        enableFiltering={true}
        enableSorting={true}
      />

      {/* Add Client Modal */}
      {showAddForm && (
        <div className="fixed inset-0 z-50 overflow-y-auto">
          <div className="flex items-center justify-center min-h-screen px-4">
            <div className="fixed inset-0 bg-black opacity-50" onClick={() => setShowAddForm(false)}></div>
            <div className="relative bg-white rounded-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto">
              <form onSubmit={handleSubmit} className="p-6">
                <div className="flex items-center justify-between mb-6">
                  <h3 className="text-lg font-medium text-gray-900">Add New Client</h3>
                  <button
                    type="button"
                    onClick={() => setShowAddForm(false)}
                    className="text-gray-400 hover:text-gray-600"
                  >
                    <span className="sr-only">Close</span>
                    ×
                  </button>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {/* Basic Information */}
                  <div className="space-y-4">
                    <h4 className="text-md font-medium text-gray-900">Basic Information</h4>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Client Onboarding Date *
                      </label>
                      <input
                        type="date"
                        name="onboardingDate"
                        value={formData.onboardingDate}
                        onChange={handleInputChange}
                        required
                        className="input-field"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Type of Company *
                      </label>
                      <select
                        name="companyType"
                        value={formData.companyType}
                        onChange={handleInputChange}
                        required
                        className="input-field"
                      >
                        <option value="">Select company type</option>
                        {companyTypeOptions.map((type) => (
                          <option key={type.id} value={type.name}>
                            {type.name}
                          </option>
                        ))}
                      </select>
                      {companyTypeOptions.length === 0 && (
                        <p className="mt-1 text-sm text-gray-500">
                          Loading company types...
                        </p>
                      )}
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Company Name / Individual Name *
                      </label>
                      <input
                        type="text"
                        name="companyName"
                        value={formData.companyName}
                        onChange={handleInputChange}
                        required
                        className={`input-field ${
                          validationErrors.companyName ? 'border-red-500 focus:border-red-500 focus:ring-red-500' : ''
                        }`}
                        placeholder="Enter company or individual name"
                      />
                      {/* Company name validation errors */}
                      {validationErrors.companyName && (
                        <div className="mt-1 p-2 bg-red-50 border border-red-200 rounded">
                          {validationErrors.companyName.map((error, index) => (
                            <p key={index} className="text-red-600 text-xs">
                              {error}
                            </p>
                          ))}
                        </div>
                      )}
                    </div>

                    {/* Email Fields */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Company's Email Id *
                      </label>
                      {formData.emails.map((email, index) => (
                        <div key={index} className="mb-2">
                          <div className="flex items-center space-x-2">
                            <input
                              type="email"
                              value={email}
                              onChange={(e) => handleEmailChange(index, e.target.value)}
                              required={index === 0}
                              className={`flex-1 input-field ${
                                validationErrors.emails ? 'border-red-500 focus:border-red-500 focus:ring-red-500' : ''
                              }`}
                              placeholder={index === 0 ? "Enter primary email address" : "Enter additional email address"}
                            />
                            {index > 0 && (
                              <button
                                type="button"
                                onClick={() => removeEmailField(index)}
                                className="px-3 py-2 text-red-600 hover:text-red-800"
                              >
                                Remove
                              </button>
                            )}
                          </div>
                          {/* Individual email validation feedback */}
                          {email && email.trim() !== '' && (
                            (() => {
                              const emailValidation = validateEmail(email);
                              return !emailValidation.isValid && (
                                <p className="text-red-500 text-xs mt-1">
                                  {emailValidation.errors[0]}
                                </p>
                              );
                            })()
                          )}
                        </div>
                      ))}
                      <button
                        type="button"
                        onClick={addEmailField}
                        className="text-blue-600 hover:text-blue-800 text-sm"
                      >
                        + Add Another Email
                      </button>
                      {/* Overall email validation errors */}
                      {validationErrors.emails && (
                        <div className="mt-2 p-2 bg-red-50 border border-red-200 rounded">
                          {validationErrors.emails.map((error, index) => (
                            <p key={index} className="text-red-600 text-xs">
                              {error}
                            </p>
                          ))}
                        </div>
                      )}
                    </div>

                    {/* Phone Fields */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Company's Phone Number *
                      </label>
                      {formData.phones.map((phone, index) => (
                        <div key={index} className="mb-2">
                          <div className="flex items-center space-x-2">
                            <input
                              type="tel"
                              value={phone}
                              onChange={(e) => handlePhoneChange(index, e.target.value)}
                              required={index === 0}
                              className={`flex-1 input-field ${
                                validationErrors.phones ? 'border-red-500 focus:border-red-500 focus:ring-red-500' : ''
                              }`}
                              placeholder={index === 0 ? getPhonePlaceholder('IN') : "Enter additional phone number"}
                            />
                            {index > 0 && (
                              <button
                                type="button"
                                onClick={() => removePhoneField(index)}
                                className="px-3 py-2 text-red-600 hover:text-red-800"
                              >
                                Remove
                              </button>
                            )}
                          </div>
                          {/* Individual phone validation feedback */}
                          {phone && phone.trim() !== '' && (
                            (() => {
                              const phoneValidation = validatePhone(phone, 'IN');
                              return !phoneValidation.isValid && (
                                <p className="text-red-500 text-xs mt-1">
                                  {phoneValidation.errors[0]}
                                </p>
                              );
                            })()
                          )}
                        </div>
                      ))}
                      <button
                        type="button"
                        onClick={addPhoneField}
                        className="text-blue-600 hover:text-blue-800 text-sm"
                      >
                        + Add Another Phone
                      </button>
                      {/* Overall phone validation errors */}
                      {validationErrors.phones && (
                        <div className="mt-2 p-2 bg-red-50 border border-red-200 rounded">
                          {validationErrors.phones.map((error, index) => (
                            <p key={index} className="text-red-600 text-xs">
                              {error}
                            </p>
                          ))}
                        </div>
                      )}
                      <p className="text-xs text-gray-500 mt-1">
                        Format: {getPhonePlaceholder('IN')}
                      </p>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Company's Address *
                      </label>
                      <textarea
                        name="address"
                        value={formData.address}
                        onChange={handleInputChange}
                        required
                        rows={3}
                        className="input-field"
                        placeholder="Complete company address"
                      />
                    </div>
                  </div>

                  {/* Location and Additional Information */}
                  <div className="space-y-4">
                    <h4 className="text-md font-medium text-gray-900">Location & Additional Information</h4>

                    {/* Global Location Selector */}
                    <LocationSelector
                      value={locationData}
                      onChange={(newLocationData) => {
                        setLocationData(newLocationData);
                        // Update form data for backward compatibility
                        setFormData(prev => ({
                          ...prev,
                          country: newLocationData.countryName || '',
                          state: newLocationData.stateName || '',
                          city: newLocationData.cityName || ''
                        }));
                      }}
                      required={true}
                      defaultCountry="India"
                      className="space-y-4"
                    />

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Username
                      </label>
                      <input
                        type="text"
                        name="username"
                        value={formData.username}
                        onChange={handleInputChange}
                        className="input-field"
                        placeholder="Enter username"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        GST Number
                      </label>
                      <input
                        type="text"
                        name="gstNumber"
                        value={formData.gstNumber}
                        onChange={handleInputChange}
                        className="input-field"
                        placeholder="Enter GST number"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        DPIIT Registered *
                      </label>
                      <div className="flex space-x-4">
                        <label className="flex items-center">
                          <input
                            type="radio"
                            name="dpiitRegistered"
                            value="yes"
                            checked={formData.dpiitRegistered === 'yes'}
                            onChange={handleInputChange}
                            required
                            className="mr-2"
                          />
                          Yes
                        </label>
                        <label className="flex items-center">
                          <input
                            type="radio"
                            name="dpiitRegistered"
                            value="no"
                            checked={formData.dpiitRegistered === 'no'}
                            onChange={handleInputChange}
                            required
                            className="mr-2"
                          />
                          No
                        </label>
                      </div>
                    </div>

                    {formData.dpiitRegistered === 'yes' && (
                      <>
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">
                            Valid Till *
                          </label>
                          <input
                            type="date"
                            name="validTill"
                            value={formData.validTill}
                            onChange={handleInputChange}
                            required
                            className="input-field"
                          />
                        </div>

                        {/* DPIIT Certificate - S3 Integration */}
                        <div>
                          <S3FileUpload
                            module="clients"
                            recordId={tempClientId}
                            fileType="dpiit"
                            allowedTypes="all"
                            multiple={true}
                            maxFiles={3}
                            label="DPIIT Certificate *"
                            description="Upload DPIIT certificate (PDF, Images, Office files) - Required"
                            onFilesUploaded={(files) => {
                              setUploadedFiles(prev => ({
                                ...prev,
                                dpiitCertificate: files
                              }));
                            }}
                            onFileDeleted={(deletedFile, remainingFiles) => {
                              setUploadedFiles(prev => ({
                                ...prev,
                                dpiitCertificate: remainingFiles
                              }));
                            }}
                            existingFiles={uploadedFiles.dpiitCertificate || []}
                          />
                        </div>
                      </>
                    )}

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Website
                      </label>
                      <input
                        type="url"
                        name="website"
                        value={formData.website}
                        onChange={handleInputChange}
                        className="input-field"
                        placeholder="https://company.com"
                      />
                    </div>

                  </div>
                </div>

                {/* Description */}
                <div className="mt-6">
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Description
                  </label>
                  <textarea
                    name="description"
                    value={formData.description || ''}
                    onChange={handleInputChange}
                    rows={3}
                    className="input-field"
                    placeholder="Brief description of the client and business relationship"
                  />
                </div>

                {/* File Upload Section */}
                <div className="mt-6">
                  <h4 className="text-md font-medium text-gray-900 mb-4">File Upload Section</h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">

                    {/* TDS File - S3 Integration */}
                    <div>
                      <S3FileUpload
                        module="clients"
                        recordId={tempClientId}
                        fileType="tds"
                        allowedTypes="documents"
                        multiple={true}
                        maxFiles={3}
                        label="TDS File"
                        description="Upload TDS documents (PDF, Office files)"
                        onFilesUploaded={(files) => {
                          setUploadedFiles(prev => ({
                            ...prev,
                            tdsFile: files
                          }));
                        }}
                        onFileDeleted={(deletedFile, remainingFiles) => {
                          setUploadedFiles(prev => ({
                            ...prev,
                            tdsFile: remainingFiles
                          }));
                        }}
                        existingFiles={uploadedFiles.tdsFile || []}
                      />
                    </div>

                    {/* GST File - S3 Integration */}
                    <div>
                      <S3FileUpload
                        module="clients"
                        recordId={tempClientId}
                        fileType="gst"
                        allowedTypes="documents"
                        multiple={true}
                        maxFiles={3}
                        label="GST File"
                        description="Upload GST documents (PDF, Office files)"
                        onFilesUploaded={(files) => {
                          setUploadedFiles(prev => ({
                            ...prev,
                            gstFile: files
                          }));
                        }}
                        onFileDeleted={(deletedFile, remainingFiles) => {
                          setUploadedFiles(prev => ({
                            ...prev,
                            gstFile: remainingFiles
                          }));
                        }}
                        existingFiles={uploadedFiles.gstFile || []}
                      />
                    </div>

                    {/* NDA File - S3 Integration */}
                    <div>
                      <S3FileUpload
                        module="clients"
                        recordId={tempClientId}
                        fileType="nda"
                        allowedTypes="documents"
                        multiple={true}
                        maxFiles={3}
                        label="NDA *"
                        description="Upload NDA documents (PDF, Office files) - Required"
                        onFilesUploaded={(files) => {
                          setUploadedFiles(prev => ({
                            ...prev,
                            ndaFile: files
                          }));
                        }}
                        onFileDeleted={(deletedFile, remainingFiles) => {
                          setUploadedFiles(prev => ({
                            ...prev,
                            ndaFile: remainingFiles
                          }));
                        }}
                        existingFiles={uploadedFiles.ndaFile || []}
                      />
                    </div>

                    {/* Agreement File - S3 Integration */}
                    <div>
                      <S3FileUpload
                        module="clients"
                        recordId={tempClientId}
                        fileType="agreement"
                        allowedTypes="documents"
                        multiple={true}
                        maxFiles={3}
                        label="Agreement *"
                        description="Upload agreement documents (PDF, Office files) - Required"
                        onFilesUploaded={(files) => {
                          setUploadedFiles(prev => ({
                            ...prev,
                            agreementFile: files
                          }));
                        }}
                        onFileDeleted={(deletedFile, remainingFiles) => {
                          setUploadedFiles(prev => ({
                            ...prev,
                            agreementFile: remainingFiles
                          }));
                        }}
                        existingFiles={uploadedFiles.agreementFile || []}
                      />
                    </div>

                    {/* Quotation File - S3 Integration */}
                    <div>
                      <S3FileUpload
                        module="clients"
                        recordId={tempClientId}
                        fileType="quotation"
                        allowedTypes="all"
                        multiple={true}
                        maxFiles={3}
                        label="Quotation *"
                        description="Upload quotation documents (PDF, Images, Office files) - Required"
                        onFilesUploaded={(files) => {
                          setUploadedFiles(prev => ({
                            ...prev,
                            quotationFile: files
                          }));
                        }}
                        onFileDeleted={(deletedFile, remainingFiles) => {
                          setUploadedFiles(prev => ({
                            ...prev,
                            quotationFile: remainingFiles
                          }));
                        }}
                        existingFiles={uploadedFiles.quotationFile || []}
                      />
                    </div>

                    {/* Pan Card File - S3 Integration */}
                    <div>
                      <S3FileUpload
                        module="clients"
                        recordId={tempClientId}
                        fileType="pancard"
                        allowedTypes="all"
                        multiple={true}
                        maxFiles={2}
                        label="Pan Card *"
                        description="Upload PAN card documents (PDF, Images) - Required"
                        onFilesUploaded={(files) => {
                          setUploadedFiles(prev => ({
                            ...prev,
                            panCardFile: files
                          }));
                        }}
                        onFileDeleted={(deletedFile, remainingFiles) => {
                          setUploadedFiles(prev => ({
                            ...prev,
                            panCardFile: remainingFiles
                          }));
                        }}
                        existingFiles={uploadedFiles.panCardFile || []}
                      />
                    </div>

                    {/* Udhyam Registration File - S3 Integration */}
                    <div>
                      <S3FileUpload
                        module="clients"
                        recordId={tempClientId}
                        fileType="udhyam"
                        allowedTypes="all"
                        multiple={true}
                        maxFiles={3}
                        label="Udhyam Registration"
                        description="Upload Udhyam registration documents (PDF, Images, Office files)"
                        onFilesUploaded={(files) => {
                          setUploadedFiles(prev => ({
                            ...prev,
                            udhyamRegistrationFile: files
                          }));
                        }}
                        onFileDeleted={(deletedFile, remainingFiles) => {
                          setUploadedFiles(prev => ({
                            ...prev,
                            udhyamRegistrationFile: remainingFiles
                          }));
                        }}
                        existingFiles={uploadedFiles.udhyamRegistrationFile || []}
                      />
                    </div>

                    {/* Others File - S3 Integration */}
                    <div>
                      <S3FileUpload
                        module="clients"
                        recordId={tempClientId}
                        fileType="others"
                        allowedTypes="all"
                        multiple={true}
                        maxFiles={5}
                        label="Others"
                        description="Upload other documents (PDF, Images, Office files)"
                        onFilesUploaded={(files) => {
                          setUploadedFiles(prev => ({
                            ...prev,
                            othersFile: files
                          }));
                        }}
                        onFileDeleted={(deletedFile, remainingFiles) => {
                          setUploadedFiles(prev => ({
                            ...prev,
                            othersFile: remainingFiles
                          }));
                        }}
                        existingFiles={uploadedFiles.othersFile || []}
                      />
                    </div>
                  </div>
                </div>

                {/* Form Actions */}
                <div className="flex justify-end space-x-3 mt-8 pt-6 border-t border-gray-200">
                  <button
                    type="button"
                    onClick={() => setShowAddForm(false)}
                    className="btn-secondary"
                  >
                    Cancel
                  </button>
                  <button
                    type="submit"
                    className="btn-primary"
                  >
                    Save Client
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default Clients;
