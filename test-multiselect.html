<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MultiSelect Component Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background: #f9f9f9;
        }
        .test-section h3 {
            margin-top: 0;
            color: #555;
        }
        .result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 3px;
            font-family: monospace;
            background: #e3f2fd;
            border: 1px solid #bbdefb;
        }
        .mock-multiselect {
            position: relative;
            border: 1px solid #ddd;
            border-radius: 5px;
            background: white;
            min-height: 40px;
            padding: 8px;
            cursor: pointer;
        }
        .mock-multiselect.open {
            border-color: #007cba;
            box-shadow: 0 0 0 2px rgba(0, 124, 186, 0.2);
        }
        .selected-items {
            display: flex;
            flex-wrap: wrap;
            gap: 4px;
            margin-bottom: 8px;
        }
        .selected-item {
            background: #e3f2fd;
            color: #1976d2;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            display: flex;
            align-items: center;
            gap: 4px;
        }
        .remove-btn {
            background: none;
            border: none;
            color: #1976d2;
            cursor: pointer;
            font-size: 14px;
            padding: 0;
            width: 16px;
            height: 16px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .remove-btn:hover {
            background: rgba(25, 118, 210, 0.1);
        }
        .dropdown {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: white;
            border: 1px solid #ddd;
            border-radius: 5px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            z-index: 1000;
            max-height: 200px;
            overflow-y: auto;
            margin-top: 4px;
        }
        .search-input {
            width: 100%;
            padding: 8px;
            border: none;
            border-bottom: 1px solid #eee;
            outline: none;
        }
        .option {
            padding: 8px 12px;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .option:hover {
            background: #f5f5f5;
        }
        .option.selected {
            background: #e3f2fd;
        }
        .checkbox {
            width: 16px;
            height: 16px;
        }
        .placeholder {
            color: #999;
        }
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .stat-card {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 5px;
            text-align: center;
        }
        .stat-number {
            font-size: 24px;
            font-weight: bold;
            color: #1976d2;
        }
        .stat-label {
            color: #666;
            font-size: 14px;
        }
        button {
            background: #007cba;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #005a87;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎯 MultiSelect Component Test</h1>
        
        <div class="stats">
            <div class="stat-card">
                <div class="stat-number" id="selectedCount">0</div>
                <div class="stat-label">Selected Items</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="totalOptions">10</div>
                <div class="stat-label">Total Options</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="searchResults">10</div>
                <div class="stat-label">Search Results</div>
            </div>
        </div>

        <div class="test-section">
            <h3>📋 Type of Work MultiSelect Demo</h3>
            <p>This demonstrates how the Type of Work field will work with multiple selections:</p>
            
            <div class="mock-multiselect" id="multiselect" onclick="toggleDropdown()">
                <div class="selected-items" id="selectedItems">
                    <span class="placeholder" id="placeholder">Select types of work...</span>
                </div>
                <div class="dropdown" id="dropdown" style="display: none;">
                    <input type="text" class="search-input" id="searchInput" placeholder="Search options..." oninput="filterOptions()">
                    <div class="option" onclick="toggleSelectAll()">
                        <input type="checkbox" class="checkbox" id="selectAllCheckbox">
                        <span><strong>Select All</strong></span>
                    </div>
                    <div id="optionsList"></div>
                </div>
            </div>
            
            <div class="result" id="selectedResult">
                Selected: None
            </div>
        </div>

        <div class="test-section">
            <h3>🧪 Test Actions</h3>
            <button onclick="selectRandomItems()">Select Random Items</button>
            <button onclick="clearAll()">Clear All</button>
            <button onclick="selectAll()">Select All</button>
            <button onclick="showSelectedData()">Show Selected Data</button>
        </div>

        <div class="test-section">
            <h3>📊 Features Demonstrated</h3>
            <ul>
                <li>✅ <strong>Multiple Selection</strong>: Select multiple types of work</li>
                <li>✅ <strong>Search Functionality</strong>: Type to filter options</li>
                <li>✅ <strong>Select All/None</strong>: Bulk selection controls</li>
                <li>✅ <strong>Visual Badges</strong>: Selected items shown as badges</li>
                <li>✅ <strong>Remove Individual Items</strong>: Click × to remove</li>
                <li>✅ <strong>Responsive Design</strong>: Works on all screen sizes</li>
                <li>✅ <strong>Professional UI</strong>: Clean, modern interface</li>
            </ul>
        </div>
    </div>

    <script>
        // Sample Type of Work options
        const typeOfWorkOptions = [
            { id: 1, name: "Patent Filing", description: "Patent application and filing services" },
            { id: 2, name: "Trademark Registration", description: "Trademark registration and protection" },
            { id: 3, name: "Copyright Registration", description: "Copyright filing and protection" },
            { id: 4, name: "Design Registration", description: "Industrial design registration" },
            { id: 5, name: "IP Litigation", description: "Intellectual property litigation support" },
            { id: 6, name: "IP Consulting", description: "Strategic IP consulting services" },
            { id: 7, name: "Patent Search", description: "Prior art and patent search services" },
            { id: 8, name: "Freedom to Operate", description: "FTO analysis and clearance" },
            { id: 9, name: "IP Valuation", description: "Intellectual property valuation" },
            { id: 10, name: "Trade Secret Protection", description: "Trade secret and confidentiality services" }
        ];

        let selectedItems = [];
        let filteredOptions = [...typeOfWorkOptions];
        let isOpen = false;

        // Initialize the component
        function init() {
            renderOptions();
            updateDisplay();
        }

        // Toggle dropdown
        function toggleDropdown() {
            isOpen = !isOpen;
            const dropdown = document.getElementById('dropdown');
            const multiselect = document.getElementById('multiselect');
            
            if (isOpen) {
                dropdown.style.display = 'block';
                multiselect.classList.add('open');
                document.getElementById('searchInput').focus();
            } else {
                dropdown.style.display = 'none';
                multiselect.classList.remove('open');
            }
        }

        // Filter options based on search
        function filterOptions() {
            const searchTerm = document.getElementById('searchInput').value.toLowerCase();
            filteredOptions = typeOfWorkOptions.filter(option =>
                option.name.toLowerCase().includes(searchTerm) ||
                option.description.toLowerCase().includes(searchTerm)
            );
            renderOptions();
            document.getElementById('searchResults').textContent = filteredOptions.length;
        }

        // Render options list
        function renderOptions() {
            const optionsList = document.getElementById('optionsList');
            optionsList.innerHTML = '';

            filteredOptions.forEach(option => {
                const isSelected = selectedItems.some(item => item.id === option.id);
                const optionDiv = document.createElement('div');
                optionDiv.className = `option ${isSelected ? 'selected' : ''}`;
                optionDiv.onclick = () => toggleOption(option);
                
                optionDiv.innerHTML = `
                    <input type="checkbox" class="checkbox" ${isSelected ? 'checked' : ''}>
                    <div>
                        <div><strong>${option.name}</strong></div>
                        <div style="font-size: 12px; color: #666;">${option.description}</div>
                    </div>
                `;
                
                optionsList.appendChild(optionDiv);
            });
        }

        // Toggle option selection
        function toggleOption(option) {
            const index = selectedItems.findIndex(item => item.id === option.id);
            if (index > -1) {
                selectedItems.splice(index, 1);
            } else {
                selectedItems.push(option);
            }
            updateDisplay();
            renderOptions();
        }

        // Toggle select all
        function toggleSelectAll() {
            if (selectedItems.length === filteredOptions.length) {
                selectedItems = [];
            } else {
                selectedItems = [...filteredOptions];
            }
            updateDisplay();
            renderOptions();
        }

        // Update display
        function updateDisplay() {
            const selectedItemsDiv = document.getElementById('selectedItems');
            const placeholder = document.getElementById('placeholder');
            const selectedResult = document.getElementById('selectedResult');
            const selectedCount = document.getElementById('selectedCount');
            const selectAllCheckbox = document.getElementById('selectAllCheckbox');

            // Update selected count
            selectedCount.textContent = selectedItems.length;

            // Update select all checkbox
            if (selectAllCheckbox) {
                selectAllCheckbox.checked = selectedItems.length === filteredOptions.length && filteredOptions.length > 0;
            }

            // Update selected items display
            if (selectedItems.length === 0) {
                selectedItemsDiv.innerHTML = '<span class="placeholder">Select types of work...</span>';
            } else {
                selectedItemsDiv.innerHTML = selectedItems.map(item => `
                    <span class="selected-item">
                        ${item.name}
                        <button class="remove-btn" onclick="removeItem(${item.id})" title="Remove">×</button>
                    </span>
                `).join('');
            }

            // Update result display
            if (selectedItems.length === 0) {
                selectedResult.textContent = 'Selected: None';
            } else {
                selectedResult.textContent = `Selected: ${selectedItems.map(item => item.name).join(', ')}`;
            }
        }

        // Remove individual item
        function removeItem(itemId) {
            selectedItems = selectedItems.filter(item => item.id !== itemId);
            updateDisplay();
            renderOptions();
        }

        // Test functions
        function selectRandomItems() {
            const randomCount = Math.floor(Math.random() * 5) + 1;
            selectedItems = [];
            for (let i = 0; i < randomCount; i++) {
                const randomOption = typeOfWorkOptions[Math.floor(Math.random() * typeOfWorkOptions.length)];
                if (!selectedItems.some(item => item.id === randomOption.id)) {
                    selectedItems.push(randomOption);
                }
            }
            updateDisplay();
            renderOptions();
        }

        function clearAll() {
            selectedItems = [];
            updateDisplay();
            renderOptions();
        }

        function selectAll() {
            selectedItems = [...typeOfWorkOptions];
            updateDisplay();
            renderOptions();
        }

        function showSelectedData() {
            const data = {
                count: selectedItems.length,
                items: selectedItems,
                names: selectedItems.map(item => item.name),
                ids: selectedItems.map(item => item.id)
            };
            alert('Selected Data:\n' + JSON.stringify(data, null, 2));
        }

        // Close dropdown when clicking outside
        document.addEventListener('click', function(event) {
            const multiselect = document.getElementById('multiselect');
            if (!multiselect.contains(event.target)) {
                if (isOpen) {
                    toggleDropdown();
                }
            }
        });

        // Initialize on page load
        window.onload = init;
    </script>
</body>
</html>
