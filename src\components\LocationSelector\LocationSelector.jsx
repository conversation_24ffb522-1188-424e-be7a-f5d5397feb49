import React, { useState, useEffect } from 'react';
import { 
  getAllCountries, 
  getStatesByCountry, 
  getCitiesByState,
  searchCountries,
  searchStates,
  searchCities
} from '../../services/locationService';

const LocationSelector = ({ 
  value = {}, 
  onChange, 
  required = false,
  showCountry = true,
  showState = true,
  showCity = true,
  defaultCountry = null,
  className = '',
  disabled = false
}) => {
  const [countries, setCountries] = useState([]);
  const [states, setStates] = useState([]);
  const [cities, setCities] = useState([]);
  
  const [loadingCountries, setLoadingCountries] = useState(false);
  const [loadingStates, setLoadingStates] = useState(false);
  const [loadingCities, setLoadingCities] = useState(false);
  
  const [searchTerms, setSearchTerms] = useState({
    country: '',
    state: '',
    city: ''
  });

  // Load countries on mount
  useEffect(() => {
    loadCountries();
  }, []);

  // Load states when country changes
  useEffect(() => {
    if (value.country) {
      loadStates(value.country);
    } else {
      setStates([]);
      setCities([]);
    }
  }, [value.country]);

  // Load cities when state changes
  useEffect(() => {
    if (value.state) {
      loadCities(value.state);
    } else {
      setCities([]);
    }
  }, [value.state]);

  // Set default country if provided
  useEffect(() => {
    if (defaultCountry && !value.country && countries.length > 0) {
      const defaultCountryObj = countries.find(c => 
        c.name === defaultCountry || c.iso2 === defaultCountry || c.id == defaultCountry
      );
      if (defaultCountryObj) {
        handleChange('country', defaultCountryObj.id, defaultCountryObj.name);
      }
    }
  }, [defaultCountry, countries, value.country]);

  const loadCountries = async () => {
    try {
      setLoadingCountries(true);
      console.log('🌍 Loading countries...');
      const countriesData = await getAllCountries();
      setCountries(countriesData);
      console.log(`✅ Loaded ${countriesData.length} countries`);
    } catch (error) {
      console.error('❌ Error loading countries:', error);
    } finally {
      setLoadingCountries(false);
    }
  };

  const loadStates = async (countryId) => {
    try {
      setLoadingStates(true);
      console.log(`🏛️ Loading states for country ${countryId}...`);
      const statesData = await getStatesByCountry(countryId);
      setStates(statesData);
      console.log(`✅ Loaded ${statesData.length} states`);
    } catch (error) {
      console.error('❌ Error loading states:', error);
      setStates([]);
    } finally {
      setLoadingStates(false);
    }
  };

  const loadCities = async (stateId) => {
    try {
      setLoadingCities(true);
      console.log(`🏙️ Loading cities for state ${stateId}...`);
      const citiesData = await getCitiesByState(stateId);
      setCities(citiesData);
      console.log(`✅ Loaded ${citiesData.length} cities`);
    } catch (error) {
      console.error('❌ Error loading cities:', error);
      setCities([]);
    } finally {
      setLoadingCities(false);
    }
  };

  const handleChange = (field, id, name) => {
    const newValue = { ...value };
    
    if (field === 'country') {
      newValue.country = id;
      newValue.countryName = name;
      newValue.state = '';
      newValue.stateName = '';
      newValue.city = '';
      newValue.cityName = '';
    } else if (field === 'state') {
      newValue.state = id;
      newValue.stateName = name;
      newValue.city = '';
      newValue.cityName = '';
    } else if (field === 'city') {
      newValue.city = id;
      newValue.cityName = name;
    }
    
    onChange(newValue);
  };

  const handleSearch = async (field, searchTerm) => {
    setSearchTerms(prev => ({ ...prev, [field]: searchTerm }));
    
    if (searchTerm.length < 2) return;
    
    try {
      if (field === 'country') {
        const results = await searchCountries(searchTerm);
        setCountries(results);
      } else if (field === 'state' && value.country) {
        const results = await searchStates(value.country, searchTerm);
        setStates(results);
      } else if (field === 'city' && value.state) {
        const results = await searchCities(value.state, searchTerm);
        setCities(results);
      }
    } catch (error) {
      console.error(`❌ Error searching ${field}:`, error);
    }
  };

  const getSelectedCountry = () => countries.find(c => c.id == value.country);
  const getSelectedState = () => states.find(s => s.id == value.state);
  const getSelectedCity = () => cities.find(c => c.id == value.city);

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Country Selector */}
      {showCountry && (
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Country {required && <span className="text-red-500">*</span>}
          </label>
          <div className="relative">
            <select
              value={value.country || ''}
              onChange={(e) => {
                const country = countries.find(c => c.id == e.target.value);
                if (country) {
                  handleChange('country', country.id, country.name);
                }
              }}
              disabled={disabled || loadingCountries}
              className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 disabled:bg-gray-100"
              required={required}
            >
              <option value="">
                {loadingCountries ? 'Loading countries...' : 'Select Country'}
              </option>
              {countries.map(country => (
                <option key={country.id} value={country.id}>
                  {country.flag} {country.name}
                </option>
              ))}
            </select>
          </div>
        </div>
      )}

      {/* State Selector */}
      {showState && value.country && (
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            State/Province {required && <span className="text-red-500">*</span>}
          </label>
          <div className="relative">
            <select
              value={value.state || ''}
              onChange={(e) => {
                const state = states.find(s => s.id == e.target.value);
                if (state) {
                  handleChange('state', state.id, state.name);
                }
              }}
              disabled={disabled || loadingStates}
              className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 disabled:bg-gray-100"
              required={required}
            >
              <option value="">
                {loadingStates ? 'Loading states...' : 'Select State/Province'}
              </option>
              {states.map(state => (
                <option key={state.id} value={state.id}>
                  {state.name}
                </option>
              ))}
            </select>
          </div>
          {states.length === 0 && !loadingStates && value.country && (
            <p className="text-sm text-gray-500 mt-1">
              No states available for selected country
            </p>
          )}
        </div>
      )}

      {/* City Selector */}
      {showCity && value.state && (
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            City {required && <span className="text-red-500">*</span>}
          </label>
          <div className="relative">
            <select
              value={value.city || ''}
              onChange={(e) => {
                const city = cities.find(c => c.id == e.target.value);
                if (city) {
                  handleChange('city', city.id, city.name);
                }
              }}
              disabled={disabled || loadingCities}
              className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 disabled:bg-gray-100"
              required={required}
            >
              <option value="">
                {loadingCities ? 'Loading cities...' : 'Select City'}
              </option>
              {cities.map(city => (
                <option key={city.id} value={city.id}>
                  {city.name}
                </option>
              ))}
            </select>
          </div>
          {cities.length === 0 && !loadingCities && value.state && (
            <p className="text-sm text-gray-500 mt-1">
              No cities available for selected state
            </p>
          )}
        </div>
      )}

      {/* Selected Location Display */}
      {(value.country || value.state || value.city) && (
        <div className="text-sm text-gray-600 bg-gray-50 p-2 rounded">
          <strong>Selected:</strong>{' '}
          {[
            getSelectedCity()?.name,
            getSelectedState()?.name,
            getSelectedCountry()?.name
          ].filter(Boolean).join(', ')}
        </div>
      )}
    </div>
  );
};

export default LocationSelector;
