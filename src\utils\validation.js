/**
 * Validation utilities for forms
 * Handles email, phone, and other field validations
 */

// Email validation regex - comprehensive pattern
const EMAIL_REGEX = /^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/;

// Phone number patterns for different countries
const PHONE_PATTERNS = {
  // Indian phone numbers: +91 followed by 10 digits
  IN: /^(\+91[\s-]?)?[6-9]\d{9}$/,
  // US phone numbers: +1 followed by 10 digits
  US: /^(\+1[\s-]?)?[2-9]\d{2}[\s-]?[2-9]\d{2}[\s-]?\d{4}$/,
  // UK phone numbers: +44 followed by various patterns
  UK: /^(\+44[\s-]?)?[1-9]\d{8,10}$/,
  // Generic international format
  INTERNATIONAL: /^\+?[1-9]\d{1,14}$/
};

/**
 * Validate email address
 * @param {string} email - Email to validate
 * @returns {object} Validation result
 */
export const validateEmail = (email) => {
  const errors = [];
  
  if (!email || email.trim() === '') {
    errors.push('Email is required');
    return { isValid: false, errors };
  }
  
  const trimmedEmail = email.trim();
  
  // Check basic format
  if (!EMAIL_REGEX.test(trimmedEmail)) {
    errors.push('Please enter a valid email address');
  }
  
  // Check length
  if (trimmedEmail.length > 254) {
    errors.push('Email address is too long (maximum 254 characters)');
  }
  
  // Check for common issues
  if (trimmedEmail.includes('..')) {
    errors.push('Email cannot contain consecutive dots');
  }
  
  if (trimmedEmail.startsWith('.') || trimmedEmail.endsWith('.')) {
    errors.push('Email cannot start or end with a dot');
  }
  
  return {
    isValid: errors.length === 0,
    errors,
    cleanValue: trimmedEmail.toLowerCase()
  };
};

/**
 * Validate phone number
 * @param {string} phone - Phone number to validate
 * @param {string} country - Country code (IN, US, UK, or INTERNATIONAL)
 * @returns {object} Validation result
 */
export const validatePhone = (phone, country = 'IN') => {
  const errors = [];
  
  if (!phone || phone.trim() === '') {
    errors.push('Phone number is required');
    return { isValid: false, errors };
  }
  
  const trimmedPhone = phone.trim();
  
  // Remove common separators for validation
  const cleanPhone = trimmedPhone.replace(/[\s\-\(\)\.]/g, '');
  
  // Check if it contains only valid characters
  if (!/^[\+\d\s\-\(\)\.]+$/.test(trimmedPhone)) {
    errors.push('Phone number contains invalid characters');
  }
  
  // Validate based on country
  const pattern = PHONE_PATTERNS[country] || PHONE_PATTERNS.INTERNATIONAL;
  
  if (!pattern.test(cleanPhone)) {
    switch (country) {
      case 'IN':
        errors.push('Please enter a valid Indian phone number (10 digits starting with 6-9)');
        break;
      case 'US':
        errors.push('Please enter a valid US phone number (10 digits)');
        break;
      case 'UK':
        errors.push('Please enter a valid UK phone number');
        break;
      default:
        errors.push('Please enter a valid phone number');
    }
  }
  
  return {
    isValid: errors.length === 0,
    errors,
    cleanValue: cleanPhone,
    formattedValue: formatPhoneNumber(cleanPhone, country)
  };
};

/**
 * Format phone number for display
 * @param {string} phone - Clean phone number
 * @param {string} country - Country code
 * @returns {string} Formatted phone number
 */
export const formatPhoneNumber = (phone, country = 'IN') => {
  if (!phone) return '';
  
  const cleanPhone = phone.replace(/\D/g, '');
  
  switch (country) {
    case 'IN':
      // Format as +91 XXXXX XXXXX
      if (cleanPhone.length === 10) {
        return `+91 ${cleanPhone.slice(0, 5)} ${cleanPhone.slice(5)}`;
      } else if (cleanPhone.length === 12 && cleanPhone.startsWith('91')) {
        return `+91 ${cleanPhone.slice(2, 7)} ${cleanPhone.slice(7)}`;
      }
      break;
    case 'US':
      // Format as +1 (XXX) XXX-XXXX
      if (cleanPhone.length === 10) {
        return `+1 (${cleanPhone.slice(0, 3)}) ${cleanPhone.slice(3, 6)}-${cleanPhone.slice(6)}`;
      } else if (cleanPhone.length === 11 && cleanPhone.startsWith('1')) {
        return `+1 (${cleanPhone.slice(1, 4)}) ${cleanPhone.slice(4, 7)}-${cleanPhone.slice(7)}`;
      }
      break;
    default:
      // Basic international format
      if (cleanPhone.startsWith('+')) {
        return cleanPhone;
      }
      return `+${cleanPhone}`;
  }
  
  return phone;
};

/**
 * Validate multiple emails
 * @param {string[]} emails - Array of emails to validate
 * @returns {object} Validation result
 */
export const validateEmails = (emails) => {
  const errors = [];
  const validEmails = [];
  const duplicates = [];
  
  if (!emails || emails.length === 0) {
    errors.push('At least one email is required');
    return { isValid: false, errors };
  }
  
  // Check for empty first email
  if (!emails[0] || emails[0].trim() === '') {
    errors.push('Primary email is required');
  }
  
  const seenEmails = new Set();
  
  emails.forEach((email, index) => {
    if (email && email.trim() !== '') {
      const validation = validateEmail(email);
      
      if (!validation.isValid) {
        errors.push(`Email ${index + 1}: ${validation.errors.join(', ')}`);
      } else {
        const cleanEmail = validation.cleanValue;
        
        // Check for duplicates
        if (seenEmails.has(cleanEmail)) {
          duplicates.push(cleanEmail);
        } else {
          seenEmails.add(cleanEmail);
          validEmails.push(cleanEmail);
        }
      }
    }
  });
  
  if (duplicates.length > 0) {
    errors.push(`Duplicate emails found: ${duplicates.join(', ')}`);
  }
  
  return {
    isValid: errors.length === 0,
    errors,
    validEmails,
    duplicates
  };
};

/**
 * Validate multiple phone numbers
 * @param {string[]} phones - Array of phone numbers to validate
 * @param {string} country - Country code
 * @returns {object} Validation result
 */
export const validatePhones = (phones, country = 'IN') => {
  const errors = [];
  const validPhones = [];
  const duplicates = [];
  
  if (!phones || phones.length === 0) {
    errors.push('At least one phone number is required');
    return { isValid: false, errors };
  }
  
  // Check for empty first phone
  if (!phones[0] || phones[0].trim() === '') {
    errors.push('Primary phone number is required');
  }
  
  const seenPhones = new Set();
  
  phones.forEach((phone, index) => {
    if (phone && phone.trim() !== '') {
      const validation = validatePhone(phone, country);
      
      if (!validation.isValid) {
        errors.push(`Phone ${index + 1}: ${validation.errors.join(', ')}`);
      } else {
        const cleanPhone = validation.cleanValue;
        
        // Check for duplicates
        if (seenPhones.has(cleanPhone)) {
          duplicates.push(phone);
        } else {
          seenPhones.add(cleanPhone);
          validPhones.push({
            original: phone,
            clean: cleanPhone,
            formatted: validation.formattedValue
          });
        }
      }
    }
  });
  
  if (duplicates.length > 0) {
    errors.push(`Duplicate phone numbers found: ${duplicates.join(', ')}`);
  }
  
  return {
    isValid: errors.length === 0,
    errors,
    validPhones,
    duplicates
  };
};

/**
 * Get phone number placeholder based on country
 * @param {string} country - Country code
 * @returns {string} Placeholder text
 */
export const getPhonePlaceholder = (country = 'IN') => {
  switch (country) {
    case 'IN':
      return '+91 XXXXX XXXXX or 9876543210';
    case 'US':
      return '+1 (XXX) XXX-XXXX or 1234567890';
    case 'UK':
      return '+44 XXXX XXXXXX';
    default:
      return '+XX XXXXXXXXXX';
  }
};

/**
 * Validate company name
 * @param {string} name - Company name to validate
 * @returns {object} Validation result
 */
export const validateCompanyName = (name) => {
  const errors = [];
  
  if (!name || name.trim() === '') {
    errors.push('Company name is required');
    return { isValid: false, errors };
  }
  
  const trimmedName = name.trim();
  
  if (trimmedName.length < 2) {
    errors.push('Company name must be at least 2 characters long');
  }
  
  if (trimmedName.length > 100) {
    errors.push('Company name must be less than 100 characters');
  }
  
  // Check for valid characters (letters, numbers, spaces, common punctuation)
  if (!/^[a-zA-Z0-9\s\.\,\-\&\(\)\'\"]+$/.test(trimmedName)) {
    errors.push('Company name contains invalid characters');
  }
  
  return {
    isValid: errors.length === 0,
    errors,
    cleanValue: trimmedName
  };
};
