import express from 'express';
import cors from 'cors';
import multer from 'multer';
import { S3Client, PutObjectCommand, DeleteObjectCommand, GetObjectCommand } from '@aws-sdk/client-s3';
import { getSignedUrl } from '@aws-sdk/s3-request-presigner';
import { v4 as uuidv4 } from 'uuid';
import dotenv from 'dotenv';
import path from 'path';
import { fileURLToPath } from 'url';

// Load environment variables
dotenv.config();

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const app = express();
const PORT = process.env.PORT || 3001;

// S3 Client Configuration (Server-side only)
const s3Client = new S3Client({
  region: process.env.AWS_REGION || 'us-east-1',
  credentials: {
    accessKeyId: process.env.AWS_ACCESS_KEY_ID,
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY
  }
});

const BUCKET_NAME = process.env.AWS_S3_BUCKET_NAME || 'innoventory3solutions';
const BASE_URL = process.env.AWS_S3_BASE_URL || 'https://innoventory3solutions.s3.us-east-1.amazonaws.com';

// File type validation
const ALLOWED_FILE_TYPES = {
  images: ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'],
  documents: ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'],
  spreadsheets: ['application/vnd.ms-excel', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'],
  all: ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp', 'application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'application/vnd.ms-excel', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet']
};

const MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB

// Middleware
app.use(cors());
app.use(express.json());

// Configure multer for memory storage
const upload = multer({
  storage: multer.memoryStorage(),
  limits: {
    fileSize: MAX_FILE_SIZE
  },
  fileFilter: (req, file, cb) => {
    const allowedTypes = req.query.allowedTypes || 'all';
    const validTypes = ALLOWED_FILE_TYPES[allowedTypes] || ALLOWED_FILE_TYPES.all;
    
    if (validTypes.includes(file.mimetype)) {
      cb(null, true);
    } else {
      cb(new Error(`File type ${file.mimetype} not allowed. Allowed types: ${validTypes.join(', ')}`));
    }
  }
});

/**
 * Generate a unique file key for S3
 */
const generateFileKey = (module, recordId, fileType, originalName) => {
  const timestamp = Date.now();
  const uuid = uuidv4().split('-')[0];
  const extension = originalName.split('.').pop();
  const sanitizedName = originalName.replace(/[^a-zA-Z0-9.-]/g, '_');
  
  return `${module}/${recordId}/${fileType}-${timestamp}-${uuid}-${sanitizedName}`;
};

/**
 * Upload file to S3
 */
const uploadToS3 = async (file, key) => {
  const command = new PutObjectCommand({
    Bucket: BUCKET_NAME,
    Key: key,
    Body: file.buffer,
    ContentType: file.mimetype,
    ContentDisposition: 'inline',
    CacheControl: 'max-age=31536000'
    // Note: ACL removed because bucket doesn't allow ACLs
    // Files will be accessible via signed URLs or bucket policy
  });

  await s3Client.send(command);

  return {
    key,
    url: `${BASE_URL}/${key}`,
    size: file.size,
    type: file.mimetype,
    name: file.originalname
  };
};

/**
 * Delete file from S3
 */
const deleteFromS3 = async (key) => {
  const command = new DeleteObjectCommand({
    Bucket: BUCKET_NAME,
    Key: key
  });

  await s3Client.send(command);
};

// Upload endpoint
app.post('/api/upload', upload.single('file'), async (req, res) => {
  try {
    const { module, recordId, fileType } = req.query;
    
    if (!module || !recordId || !fileType) {
      return res.status(400).json({
        success: false,
        error: 'Missing required parameters: module, recordId, fileType'
      });
    }

    if (!req.file) {
      return res.status(400).json({
        success: false,
        error: 'No file uploaded'
      });
    }

    console.log('📤 Uploading file:', {
      module,
      recordId,
      fileType,
      fileName: req.file.originalname,
      fileSize: req.file.size,
      fileType: req.file.mimetype
    });

    // Generate unique key
    const key = generateFileKey(module, recordId, fileType, req.file.originalname);
    
    // Upload to S3
    const result = await uploadToS3(req.file, key);
    
    console.log('✅ File uploaded successfully:', result);

    res.status(200).json({
      success: true,
      file: result
    });

  } catch (error) {
    console.error('❌ Upload error:', error);
    res.status(500).json({
      success: false,
      error: error.message || 'Upload failed'
    });
  }
});

// Delete endpoint
app.delete('/api/upload', async (req, res) => {
  try {
    const { key } = req.query;
    
    if (!key) {
      return res.status(400).json({
        success: false,
        error: 'Missing file key'
      });
    }

    console.log('🗑️ Deleting file:', key);
    
    await deleteFromS3(key);
    
    console.log('✅ File deleted successfully');

    res.status(200).json({
      success: true,
      message: 'File deleted successfully'
    });

  } catch (error) {
    console.error('❌ Delete error:', error);
    res.status(500).json({
      success: false,
      error: error.message || 'Delete failed'
    });
  }
});

// Health check endpoint
app.get('/api/health', (req, res) => {
  res.json({ 
    success: true, 
    message: 'Upload server is running',
    timestamp: new Date().toISOString()
  });
});

// Generate signed URL for private file access
app.get('/api/signed-url', async (req, res) => {
  try {
    const { key, expiresIn = 3600 } = req.query;

    if (!key) {
      return res.status(400).json({
        success: false,
        error: 'S3 key is required'
      });
    }

    console.log('🔗 Generating signed URL for key:', key);

    // Create a GetObjectCommand for the file
    const command = new GetObjectCommand({
      Bucket: BUCKET_NAME,
      Key: key
    });

    // Generate signed URL
    const signedUrl = await getSignedUrl(s3Client, command, {
      expiresIn: parseInt(expiresIn)
    });

    console.log('✅ Signed URL generated successfully');

    res.json({
      success: true,
      signedUrl,
      expiresIn: parseInt(expiresIn),
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('❌ Error generating signed URL:', error);
    res.status(500).json({
      success: false,
      error: error.message || 'Failed to generate signed URL'
    });
  }
});

// Serve static files from dist directory (for production)
if (process.env.NODE_ENV === 'production') {
  app.use(express.static(path.join(__dirname, 'dist')));
  
  app.get('*', (req, res) => {
    res.sendFile(path.join(__dirname, 'dist', 'index.html'));
  });
}

// Error handling middleware
app.use((error, req, res, next) => {
  if (error instanceof multer.MulterError) {
    if (error.code === 'LIMIT_FILE_SIZE') {
      return res.status(400).json({
        success: false,
        error: `File too large. Maximum size is ${MAX_FILE_SIZE / (1024 * 1024)}MB`
      });
    }
  }
  
  console.error('Server error:', error);
  res.status(500).json({
    success: false,
    error: error.message || 'Internal server error'
  });
});

app.listen(PORT, () => {
  console.log(`🚀 Upload server running on port ${PORT}`);
  console.log(`📁 S3 Bucket: ${BUCKET_NAME}`);
  console.log(`🌐 Base URL: ${BASE_URL}`);
  console.log(`🔧 Environment: ${process.env.NODE_ENV || 'development'}`);
});
