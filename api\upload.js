import { S3Client, PutObjectCommand, DeleteObjectCommand } from '@aws-sdk/client-s3';
import { v4 as uuidv4 } from 'uuid';
import formidable from 'formidable';
import fs from 'fs';

// S3 Client Configuration
const s3Client = new S3Client({
  region: process.env.AWS_REGION || 'us-east-1',
  credentials: {
    accessKeyId: process.env.AWS_ACCESS_KEY_ID,
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY
  }
});

const BUCKET_NAME = process.env.AWS_S3_BUCKET_NAME || 'innoventory3solutions';
const BASE_URL = process.env.AWS_S3_BASE_URL || 'https://innoventory3solutions.s3.us-east-1.amazonaws.com';

// File type validation
const ALLOWED_FILE_TYPES = {
  images: ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'],
  documents: ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'],
  spreadsheets: ['application/vnd.ms-excel', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'],
  all: ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp', 'application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'application/vnd.ms-excel', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet']
};

const MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB

/**
 * Generate a unique file key for S3
 */
const generateFileKey = (module, recordId, fileType, originalName) => {
  const timestamp = Date.now();
  const uuid = uuidv4().split('-')[0];
  const extension = originalName.split('.').pop();
  const sanitizedName = originalName.replace(/[^a-zA-Z0-9.-]/g, '_');
  
  return `${module}/${recordId}/${fileType}-${timestamp}-${uuid}-${sanitizedName}`;
};

/**
 * Upload file to S3
 */
const uploadToS3 = async (filePath, key, mimetype) => {
  const fileContent = fs.readFileSync(filePath);

  const command = new PutObjectCommand({
    Bucket: BUCKET_NAME,
    Key: key,
    Body: fileContent,
    ContentType: mimetype,
    ContentDisposition: 'inline',
    CacheControl: 'max-age=31536000'
    // Note: ACL removed because bucket doesn't allow ACLs
    // Files will be accessible via signed URLs or bucket policy
  });

  await s3Client.send(command);

  return {
    key,
    url: `${BASE_URL}/${key}`,
    size: fileContent.length,
    type: mimetype
  };
};

/**
 * Delete file from S3
 */
const deleteFromS3 = async (key) => {
  const command = new DeleteObjectCommand({
    Bucket: BUCKET_NAME,
    Key: key
  });

  await s3Client.send(command);
};

/**
 * Parse form data
 */
const parseForm = (req) => {
  return new Promise((resolve, reject) => {
    const form = formidable({
      maxFileSize: MAX_FILE_SIZE,
      keepExtensions: true,
      multiples: false,
      uploadDir: '/tmp', // Use /tmp directory in Vercel
      filename: (name, ext, part, form) => {
        // Generate a unique filename to avoid conflicts
        return `upload_${Date.now()}_${Math.random().toString(36).substring(2)}${ext}`;
      }
    });

    form.parse(req, (err, fields, files) => {
      if (err) {
        console.error('❌ Form parsing error:', err);
        reject(err);
      } else {
        console.log('📋 Form parsed successfully:', {
          fieldsCount: Object.keys(fields).length,
          filesCount: Object.keys(files).length,
          fileKeys: Object.keys(files)
        });
        resolve({ fields, files });
      }
    });
  });
};

/**
 * Vercel API handler
 */
export default async function handler(req, res) {
  // Set CORS headers
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'POST, DELETE, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type');

  if (req.method === 'OPTIONS') {
    res.status(200).end();
    return;
  }

  if (req.method === 'POST') {
    try {
      const { module, recordId, fileType, allowedTypes = 'all' } = req.query;
      
      if (!module || !recordId || !fileType) {
        return res.status(400).json({
          success: false,
          error: 'Missing required parameters: module, recordId, fileType'
        });
      }

      // Parse form data
      const { files } = await parseForm(req);
      const file = files.file;

      if (!file) {
        return res.status(400).json({
          success: false,
          error: 'No file uploaded'
        });
      }

      // Handle both single file and array of files
      const uploadFile = Array.isArray(file) ? file[0] : file;

      // Get file info with fallbacks
      const fileName = uploadFile.originalFilename || uploadFile.name || 'unknown';
      const fileSize = uploadFile.size || 0;
      let mimeType = uploadFile.mimetype || uploadFile.type;

      // If mimetype is still undefined, try to determine from file extension
      if (!mimeType) {
        const extension = fileName.split('.').pop()?.toLowerCase();
        const mimeTypeMap = {
          'jpg': 'image/jpeg',
          'jpeg': 'image/jpeg',
          'png': 'image/png',
          'gif': 'image/gif',
          'webp': 'image/webp',
          'pdf': 'application/pdf',
          'doc': 'application/msword',
          'docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
          'xls': 'application/vnd.ms-excel',
          'xlsx': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        };
        mimeType = mimeTypeMap[extension] || 'application/octet-stream';
      }

      console.log('📤 File details:', {
        fileName,
        fileSize,
        mimeType,
        originalMimeType: uploadFile.mimetype,
        allowedTypes
      });

      // Validate file type
      const validTypes = ALLOWED_FILE_TYPES[allowedTypes] || ALLOWED_FILE_TYPES.all;
      if (!validTypes.includes(mimeType)) {
        return res.status(400).json({
          success: false,
          error: `File type ${mimeType} not allowed. Allowed types: ${validTypes.join(', ')}`
        });
      }

      console.log('📤 Uploading file to S3:', {
        module,
        recordId,
        fileType,
        fileName,
        fileSize,
        mimeType
      });

      // Generate unique key
      const key = generateFileKey(module, recordId, fileType, fileName);

      // Upload to S3
      const result = await uploadToS3(uploadFile.filepath, key, mimeType);

      // Clean up temp file
      if (uploadFile.filepath && fs.existsSync(uploadFile.filepath)) {
        fs.unlinkSync(uploadFile.filepath);
      }

      console.log('✅ File uploaded successfully:', result);

      res.status(200).json({
        success: true,
        file: {
          ...result,
          name: fileName,
          originalName: fileName
        }
      });

    } catch (error) {
      console.error('❌ Upload error:', error);
      res.status(500).json({
        success: false,
        error: error.message || 'Upload failed'
      });
    }
  } 
  
  else if (req.method === 'DELETE') {
    try {
      const { key } = req.query;
      
      if (!key) {
        return res.status(400).json({
          success: false,
          error: 'Missing file key'
        });
      }

      console.log('🗑️ Deleting file:', key);
      
      await deleteFromS3(key);
      
      console.log('✅ File deleted successfully');

      res.status(200).json({
        success: true,
        message: 'File deleted successfully'
      });

    } catch (error) {
      console.error('❌ Delete error:', error);
      res.status(500).json({
        success: false,
        error: error.message || 'Delete failed'
      });
    }
  } 
  
  else {
    res.status(405).json({
      success: false,
      error: 'Method not allowed'
    });
  }
}

// Disable body parser for file uploads
export const config = {
  api: {
    bodyParser: false,
  },
};
