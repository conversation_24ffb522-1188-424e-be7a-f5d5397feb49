/**
 * IMMEDIATE DATABASE FIX
 * This script will directly add the missing startup_benefits column to NeonDB
 */

// Import database connection
import { sql } from './src/config/database.js';

async function fixDatabaseNow() {
  try {
    console.log('🚨 EMERGENCY DATABASE FIX STARTING...');
    console.log('');

    // Step 1: Test connection
    console.log('1️⃣ Testing database connection...');
    const connectionTest = await sql`SELECT NOW() as current_time`;
    console.log('✅ Database connection successful:', connectionTest[0].current_time);
    console.log('');

    // Step 2: Check current table structure
    console.log('2️⃣ Checking current vendors table structure...');
    const currentColumns = await sql`
      SELECT column_name, data_type, column_default
      FROM information_schema.columns 
      WHERE table_name = 'vendors'
      ORDER BY ordinal_position
    `;
    
    console.log('📋 Current vendors table columns:');
    currentColumns.forEach(col => {
      console.log(`   - ${col.column_name} (${col.data_type})`);
    });
    console.log('');

    // Step 3: Check if startup_benefits column exists
    console.log('3️⃣ Checking for startup_benefits column...');
    const startupBenefitsExists = currentColumns.some(col => col.column_name === 'startup_benefits');
    
    if (startupBenefitsExists) {
      console.log('✅ startup_benefits column already exists!');
      console.log('');
    } else {
      console.log('❌ startup_benefits column is missing!');
      console.log('');

      // Step 4: Add the missing column
      console.log('4️⃣ Adding startup_benefits column...');
      await sql`
        ALTER TABLE vendors 
        ADD COLUMN startup_benefits VARCHAR(10) DEFAULT 'No'
      `;
      console.log('✅ startup_benefits column added successfully!');
      console.log('');
    }

    // Step 5: Check if point_of_contact column exists
    console.log('5️⃣ Checking for point_of_contact column...');
    const pocExists = currentColumns.some(col => col.column_name === 'point_of_contact');
    
    if (pocExists) {
      console.log('✅ point_of_contact column already exists!');
      console.log('');
    } else {
      console.log('❌ point_of_contact column is missing!');
      console.log('');

      // Add point_of_contact column
      console.log('6️⃣ Adding point_of_contact column...');
      await sql`
        ALTER TABLE vendors 
        ADD COLUMN point_of_contact JSONB DEFAULT '[]'
      `;
      console.log('✅ point_of_contact column added successfully!');
      console.log('');
    }

    // Step 6: Verify the fix
    console.log('7️⃣ Verifying the fix...');
    const updatedColumns = await sql`
      SELECT column_name, data_type, column_default
      FROM information_schema.columns 
      WHERE table_name = 'vendors'
      AND column_name IN ('startup_benefits', 'point_of_contact')
      ORDER BY column_name
    `;

    console.log('📋 New columns added:');
    updatedColumns.forEach(col => {
      console.log(`   ✅ ${col.column_name} (${col.data_type}) DEFAULT ${col.column_default}`);
    });
    console.log('');

    // Step 7: Test vendor creation (dry run)
    console.log('8️⃣ Testing vendor table structure...');
    const vendorCount = await sql`SELECT COUNT(*) as count FROM vendors`;
    console.log(`📊 Current vendor count: ${vendorCount[0].count}`);

    // Test that we can select with new columns
    const testQuery = await sql`
      SELECT id, company_name, startup_benefits, point_of_contact 
      FROM vendors 
      LIMIT 1
    `;
    console.log('✅ Can successfully query new columns!');
    console.log('');

    // Success message
    console.log('🎉 DATABASE FIX COMPLETED SUCCESSFULLY!');
    console.log('');
    console.log('✅ What was fixed:');
    console.log('   - Added startup_benefits column (VARCHAR(10) DEFAULT \'No\')');
    console.log('   - Added point_of_contact column (JSONB DEFAULT \'[]\')');
    console.log('   - Verified all columns are working');
    console.log('');
    console.log('🚀 You can now:');
    console.log('   - Create vendors with startup benefits');
    console.log('   - Add point of contact information');
    console.log('   - Use all vendor form features without errors');
    console.log('');

    return true;

  } catch (error) {
    console.error('💥 DATABASE FIX FAILED!');
    console.error('');
    console.error('❌ Error details:', error);
    console.error('');
    console.error('🔧 Possible solutions:');
    console.error('   1. Check database connection');
    console.error('   2. Verify database permissions (need ALTER TABLE)');
    console.error('   3. Check if NeonDB is accessible');
    console.error('   4. Try running the fix again');
    console.error('');
    return false;
  }
}

// Run the fix immediately
console.log('🚨 STARTING EMERGENCY DATABASE FIX...');
console.log('This will add the missing startup_benefits column to your NeonDB');
console.log('');

fixDatabaseNow()
  .then((success) => {
    if (success) {
      console.log('🎯 FIX COMPLETED! Try creating a vendor now.');
      process.exit(0);
    } else {
      console.log('❌ FIX FAILED! Check the error messages above.');
      process.exit(1);
    }
  })
  .catch((error) => {
    console.error('💥 UNEXPECTED ERROR:', error);
    process.exit(1);
  });
