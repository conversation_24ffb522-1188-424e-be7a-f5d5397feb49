// Test Upload Functionality
// This script tests the upload system to identify issues

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

console.log('🧪 Testing Upload Functionality');
console.log('================================');

// Test 1: Check environment variables
console.log('\n1. 📋 Environment Variables:');
console.log('AWS_ACCESS_KEY_ID:', process.env.AWS_ACCESS_KEY_ID ? '✅ Set' : '❌ Missing');
console.log('AWS_SECRET_ACCESS_KEY:', process.env.AWS_SECRET_ACCESS_KEY ? '✅ Set' : '❌ Missing');
console.log('AWS_S3_BUCKET_NAME:', process.env.AWS_S3_BUCKET_NAME || 'innoventory3solutions');
console.log('AWS_REGION:', process.env.AWS_REGION || 'us-east-1');

// Test 2: Check upload files exist
console.log('\n2. 📁 Upload Handler Files:');
const uploadFiles = [
  'api/upload.js',
  'src/api/upload.js', 
  'server.js',
  'vite-upload-plugin.js'
];

uploadFiles.forEach(file => {
  const exists = fs.existsSync(path.join(__dirname, file));
  console.log(`${file}: ${exists ? '✅ Exists' : '❌ Missing'}`);
});

// Test 3: Check package dependencies
console.log('\n3. 📦 Dependencies:');
try {
  const packageJson = JSON.parse(fs.readFileSync(path.join(__dirname, 'package.json'), 'utf8'));
  const requiredDeps = [
    '@aws-sdk/client-s3',
    '@aws-sdk/s3-request-presigner',
    'multer',
    'formidable'
  ];
  
  requiredDeps.forEach(dep => {
    const installed = packageJson.dependencies[dep] || packageJson.devDependencies[dep];
    console.log(`${dep}: ${installed ? `✅ ${installed}` : '❌ Missing'}`);
  });
} catch (error) {
  console.log('❌ Error reading package.json:', error.message);
}

// Test 4: Test AWS SDK import
console.log('\n4. 🔧 AWS SDK Test:');
try {
  const { S3Client, PutObjectCommand } = await import('@aws-sdk/client-s3');
  console.log('✅ AWS SDK imports successfully');
  
  // Test S3 client creation
  const s3Client = new S3Client({
    region: process.env.AWS_REGION || 'us-east-1',
    credentials: {
      accessKeyId: process.env.AWS_ACCESS_KEY_ID,
      secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY
    }
  });
  console.log('✅ S3 Client created successfully');
} catch (error) {
  console.log('❌ AWS SDK error:', error.message);
}

// Test 5: Test file creation for upload
console.log('\n5. 📄 Test File Creation:');
try {
  const testContent = 'This is a test file for upload functionality';
  const testFilePath = path.join(__dirname, 'test-upload-file.txt');
  fs.writeFileSync(testFilePath, testContent);
  console.log('✅ Test file created:', testFilePath);
  
  // Clean up
  fs.unlinkSync(testFilePath);
  console.log('✅ Test file cleaned up');
} catch (error) {
  console.log('❌ File creation error:', error.message);
}

// Test 6: Check Vercel configuration
console.log('\n6. 🚀 Vercel Configuration:');
try {
  const vercelConfig = JSON.parse(fs.readFileSync(path.join(__dirname, 'vercel.json'), 'utf8'));
  console.log('✅ vercel.json exists');
  console.log('Functions configured:', Object.keys(vercelConfig.functions || {}));
  console.log('Rewrites configured:', vercelConfig.rewrites?.length || 0);
} catch (error) {
  console.log('❌ Vercel config error:', error.message);
}

// Test 7: Recommendations
console.log('\n7. 💡 Recommendations:');
console.log('================================');

if (!process.env.AWS_ACCESS_KEY_ID || !process.env.AWS_SECRET_ACCESS_KEY) {
  console.log('❌ Missing AWS credentials - upload will fail');
  console.log('   → Set AWS_ACCESS_KEY_ID and AWS_SECRET_ACCESS_KEY in .env file');
}

console.log('✅ For development: Use `npm run dev` to start Vite with upload plugin');
console.log('✅ For production: Deploy to Vercel with proper environment variables');
console.log('✅ Test upload with: Open test-upload.html in browser');

console.log('\n🔧 Quick Fix Commands:');
console.log('================================');
console.log('1. Start development server: npm run dev');
console.log('2. Test upload endpoint: Open http://localhost:5173/test-upload.html');
console.log('3. Check logs in browser console for detailed error messages');

console.log('\n📋 Upload Endpoint URLs:');
console.log('================================');
console.log('Development: http://localhost:5173/api/upload');
console.log('Production: https://your-domain.vercel.app/api/upload');

console.log('\n✅ Upload functionality test completed!');
