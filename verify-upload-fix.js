// Verify Upload Fix Script
// This script verifies that all upload system fixes are properly implemented

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

console.log('🔍 Verifying Upload System Fix');
console.log('==============================\n');

let allChecksPass = true;

// Helper function to check if file contains specific content
const checkFileContains = (filePath, searchText, description) => {
  try {
    const content = fs.readFileSync(path.join(__dirname, filePath), 'utf8');
    const contains = content.includes(searchText);
    console.log(`${contains ? '✅' : '❌'} ${description}`);
    if (!contains) {
      console.log(`   Missing: "${searchText}" in ${filePath}`);
      allChecksPass = false;
    }
    return contains;
  } catch (error) {
    console.log(`❌ ${description} - File not found: ${filePath}`);
    allChecksPass = false;
    return false;
  }
};

// Helper function to check if file exists
const checkFileExists = (filePath, description) => {
  const exists = fs.existsSync(path.join(__dirname, filePath));
  console.log(`${exists ? '✅' : '❌'} ${description}`);
  if (!exists) {
    allChecksPass = false;
  }
  return exists;
};

console.log('1. 🔧 Checking S3 ACL Fix');
console.log('-------------------------');
checkFileContains('src/api/upload.js', 'ACL: \'public-read\'', 'S3 public-read ACL in src/api/upload.js');
checkFileContains('api/upload.js', 'ACL: \'public-read\'', 'S3 public-read ACL in api/upload.js');
checkFileContains('vite-upload-plugin.js', 'ACL: \'public-read\'', 'S3 public-read ACL in vite-upload-plugin.js');
checkFileContains('server.js', 'ACL: \'public-read\'', 'S3 public-read ACL in server.js');

console.log('\n2. 📁 Checking New Configuration Files');
console.log('--------------------------------------');
checkFileExists('src/config/uploadConfig.js', 'Upload configuration file exists');
checkFileExists('src/components/UploadTest.jsx', 'Upload test component exists');
checkFileExists('src/components/S3AccessTest.jsx', 'S3 access test component exists');
checkFileExists('src/utils/s3Debug.js', 'S3 debug utilities exist');

console.log('\n3. 🔌 Checking Vite Plugin Configuration');
console.log('----------------------------------------');
checkFileContains('vite.config.js', 'uploadPlugin()', 'Vite upload plugin configured');
checkFileContains('vite.config.js', 'import uploadPlugin', 'Upload plugin imported in vite.config.js');

console.log('\n4. 🛣️ Checking Route Configuration');
console.log('----------------------------------');
checkFileContains('src/App.jsx', '/test-upload', 'Upload test route configured');
checkFileContains('src/App.jsx', '/test-s3', 'S3 test route configured');
checkFileContains('src/App.jsx', 'UploadTest', 'UploadTest component imported');

console.log('\n5. 🔧 Checking Service Updates');
console.log('------------------------------');
checkFileContains('src/services/s3Service.js', 'uploadConfig', 'S3 service uses centralized config');
checkFileContains('src/services/s3Service.js', 'getDebugInfo', 'Debug info integration');

console.log('\n6. 📋 Checking Environment Configuration');
console.log('---------------------------------------');
checkFileExists('.env', '.env file exists');
if (fs.existsSync(path.join(__dirname, '.env'))) {
  const envContent = fs.readFileSync(path.join(__dirname, '.env'), 'utf8');
  const hasAwsKey = envContent.includes('AWS_ACCESS_KEY_ID');
  const hasAwsSecret = envContent.includes('AWS_SECRET_ACCESS_KEY');
  const hasS3Bucket = envContent.includes('AWS_S3_BUCKET_NAME');
  
  console.log(`${hasAwsKey ? '✅' : '❌'} AWS_ACCESS_KEY_ID configured`);
  console.log(`${hasAwsSecret ? '✅' : '❌'} AWS_SECRET_ACCESS_KEY configured`);
  console.log(`${hasS3Bucket ? '✅' : '❌'} AWS_S3_BUCKET_NAME configured`);
  
  if (!hasAwsKey || !hasAwsSecret || !hasS3Bucket) {
    allChecksPass = false;
  }
}

console.log('\n7. 📄 Checking Documentation');
console.log('----------------------------');
checkFileExists('UPLOAD_SYSTEM_FIX.md', 'Upload fix documentation exists');
checkFileExists('S3_ACCESS_FIX_SUMMARY.md', 'S3 access fix summary exists');
checkFileExists('test-upload.html', 'Standalone upload test page exists');

console.log('\n8. 🧪 Checking Test Files');
console.log('-------------------------');
checkFileExists('test-upload-functionality.js', 'Upload functionality test script exists');
checkFileExists('verify-upload-fix.js', 'Upload fix verification script exists');

console.log('\n9. 🔍 Checking Package Dependencies');
console.log('----------------------------------');
try {
  const packageJson = JSON.parse(fs.readFileSync(path.join(__dirname, 'package.json'), 'utf8'));
  const requiredDeps = [
    '@aws-sdk/client-s3',
    '@aws-sdk/s3-request-presigner',
    'multer',
    'formidable',
    'dotenv'
  ];
  
  requiredDeps.forEach(dep => {
    const installed = packageJson.dependencies[dep] || packageJson.devDependencies[dep];
    console.log(`${installed ? '✅' : '❌'} ${dep} ${installed ? `(${installed})` : '- Missing'}`);
    if (!installed) {
      allChecksPass = false;
    }
  });
} catch (error) {
  console.log('❌ Error reading package.json');
  allChecksPass = false;
}

console.log('\n10. 🚀 Checking Vercel Configuration');
console.log('------------------------------------');
checkFileExists('vercel.json', 'Vercel configuration exists');
if (fs.existsSync(path.join(__dirname, 'vercel.json'))) {
  checkFileContains('vercel.json', 'api/upload.js', 'Upload API function configured in Vercel');
}

// Final Summary
console.log('\n' + '='.repeat(50));
console.log('📊 VERIFICATION SUMMARY');
console.log('='.repeat(50));

if (allChecksPass) {
  console.log('🎉 ALL CHECKS PASSED! ✅');
  console.log('\n✅ Upload system fix is properly implemented');
  console.log('✅ All required files are in place');
  console.log('✅ Configuration is correct');
  console.log('\n🚀 Next Steps:');
  console.log('1. Start development server: npm run dev');
  console.log('2. Test upload at: http://localhost:5173/test-upload');
  console.log('3. Verify file access after upload');
  console.log('4. Deploy to production with environment variables');
} else {
  console.log('⚠️ SOME CHECKS FAILED! ❌');
  console.log('\n❌ Upload system fix has issues that need to be addressed');
  console.log('\n🔧 Required Actions:');
  console.log('1. Review the failed checks above');
  console.log('2. Fix any missing files or configurations');
  console.log('3. Re-run this verification script');
  console.log('4. Test upload functionality once all checks pass');
}

console.log('\n📋 Test URLs (after starting dev server):');
console.log('- Upload Test: http://localhost:5173/test-upload');
console.log('- S3 Access Test: http://localhost:5173/test-s3');
console.log('- Standalone Test: Open test-upload.html in browser');

console.log('\n📞 Support:');
console.log('- Check browser console for detailed error messages');
console.log('- Review UPLOAD_SYSTEM_FIX.md for complete documentation');
console.log('- Use diagnostic script: node test-upload-functionality.js');

process.exit(allChecksPass ? 0 : 1);
