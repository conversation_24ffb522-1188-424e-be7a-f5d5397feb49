import { sql } from '../config/database.js';

// Get all services
export const getAllServices = async (includeInactive = false) => {
  try {
    console.log('🔄 Fetching all services from database...');

    let query;
    if (includeInactive) {
      query = sql`
        SELECT 
          id,
          name,
          description,
          category,
          "isActive",
          status,
          created_at,
          updated_at
        FROM services 
        ORDER BY name ASC
      `;
    } else {
      query = sql`
        SELECT 
          id,
          name,
          description,
          category,
          "isActive",
          status,
          created_at,
          updated_at
        FROM services 
        WHERE "isActive" = true AND (status IS NULL OR status != 'Inactive')
        ORDER BY name ASC
      `;
    }

    const services = await query;
    console.log('✅ Services fetched successfully:', services.length);
    
    return services.map(service => ({
      id: service.id,
      name: service.name,
      description: service.description || '',
      category: service.category || '',
      isActive: service.isActive !== false && service.status !== 'Inactive',
      status: service.status || (service.isActive ? 'Active' : 'Inactive'),
      createdAt: service.created_at,
      updatedAt: service.updated_at
    }));

  } catch (error) {
    console.error('❌ Error fetching services:', error);
    throw error;
  }
};

// Get active services (for dropdowns)
export const getActiveServices = async () => {
  try {
    console.log('🔄 Fetching active services from database...');

    const services = await sql`
      SELECT 
        id,
        name,
        description,
        category,
        "isActive",
        status
      FROM services 
      WHERE ("isActive" = true OR "isActive" IS NULL)
        AND (status IS NULL OR status != 'Inactive')
      ORDER BY name ASC
    `;

    console.log('✅ Active services found in database:', services.length, 'entries');
    return services.map(service => ({
      id: service.id,
      name: service.name,
      description: service.description || '',
      category: service.category || '',
      isActive: service.isActive !== false && service.status !== 'Inactive'
    }));

  } catch (error) {
    console.error('❌ Error fetching active services:', error);
    throw error;
  }
};

// Get service by ID
export const getServiceById = async (id) => {
  try {
    console.log('🔄 Fetching service by ID:', id);

    const services = await sql`
      SELECT 
        id,
        name,
        description,
        category,
        "isActive",
        status,
        created_at,
        updated_at
      FROM services 
      WHERE id = ${id}
    `;

    if (services.length === 0) {
      throw new Error('Service not found');
    }

    const service = services[0];
    console.log('✅ Service found:', service.name);

    return {
      id: service.id,
      name: service.name,
      description: service.description || '',
      category: service.category || '',
      isActive: service.isActive !== false && service.status !== 'Inactive',
      status: service.status || (service.isActive ? 'Active' : 'Inactive'),
      createdAt: service.created_at,
      updatedAt: service.updated_at
    };

  } catch (error) {
    console.error('❌ Error fetching service by ID:', error);
    throw error;
  }
};

// Create new service
export const createService = async (serviceData) => {
  try {
    console.log('🔄 Creating new service:', serviceData);

    const { name, description, category, isActive = true } = serviceData;

    // Check if service name already exists
    const existingServices = await sql`
      SELECT id FROM services WHERE LOWER(name) = LOWER(${name})
    `;

    if (existingServices.length > 0) {
      throw new Error('Service with this name already exists');
    }

    const result = await sql`
      INSERT INTO services (name, description, category, "isActive", status, created_at, updated_at)
      VALUES (
        ${name},
        ${description || null},
        ${category || null},
        ${isActive},
        ${isActive ? 'Active' : 'Inactive'},
        CURRENT_TIMESTAMP,
        CURRENT_TIMESTAMP
      )
      RETURNING id, name, description, category, "isActive", status, created_at, updated_at
    `;

    const newService = result[0];
    console.log('✅ Service created successfully:', newService.name);

    return {
      id: newService.id,
      name: newService.name,
      description: newService.description || '',
      category: newService.category || '',
      isActive: newService.isActive,
      status: newService.status,
      createdAt: newService.created_at,
      updatedAt: newService.updated_at
    };

  } catch (error) {
    console.error('❌ Error creating service:', error);
    throw error;
  }
};

// Update service
export const updateService = async (id, serviceData) => {
  try {
    console.log('🔄 Updating service:', id, serviceData);

    const { name, description, category, isActive } = serviceData;

    // Check if service exists
    const existingServices = await sql`
      SELECT id FROM services WHERE id = ${id}
    `;

    if (existingServices.length === 0) {
      throw new Error('Service not found');
    }

    // Check if name is being changed and if new name already exists
    if (name) {
      const duplicateServices = await sql`
        SELECT id FROM services WHERE LOWER(name) = LOWER(${name}) AND id != ${id}
      `;

      if (duplicateServices.length > 0) {
        throw new Error('Service with this name already exists');
      }
    }

    const result = await sql`
      UPDATE services 
      SET 
        name = COALESCE(${name}, name),
        description = COALESCE(${description}, description),
        category = COALESCE(${category}, category),
        "isActive" = COALESCE(${isActive}, "isActive"),
        status = COALESCE(${isActive !== undefined ? (isActive ? 'Active' : 'Inactive') : null}, status),
        updated_at = CURRENT_TIMESTAMP
      WHERE id = ${id}
      RETURNING id, name, description, category, "isActive", status, created_at, updated_at
    `;

    const updatedService = result[0];
    console.log('✅ Service updated successfully:', updatedService.name);

    return {
      id: updatedService.id,
      name: updatedService.name,
      description: updatedService.description || '',
      category: updatedService.category || '',
      isActive: updatedService.isActive,
      status: updatedService.status,
      createdAt: updatedService.created_at,
      updatedAt: updatedService.updated_at
    };

  } catch (error) {
    console.error('❌ Error updating service:', error);
    throw error;
  }
};

// Delete service (soft delete)
export const deleteService = async (id) => {
  try {
    console.log('🔄 Deleting service with ID:', id);

    // Validate ID
    if (!id || isNaN(parseInt(id))) {
      throw new Error('Invalid service ID provided');
    }

    // Check if service exists
    const existingServices = await sql`
      SELECT id, name, "isActive", status FROM services WHERE id = ${parseInt(id)}
    `;

    if (existingServices.length === 0) {
      throw new Error('Service not found');
    }

    const service = existingServices[0];
    console.log('📋 Found service to delete:', service);

    // Check if already inactive
    if (!service.isActive || service.status === 'Inactive') {
      console.log('⚠️ Service is already inactive');
      return { success: true, message: 'Service was already inactive' };
    }

    // Soft delete by setting isActive to false
    const updateResult = await sql`
      UPDATE services
      SET
        "isActive" = false,
        status = 'Inactive',
        updated_at = CURRENT_TIMESTAMP
      WHERE id = ${parseInt(id)}
      RETURNING id, name, "isActive", status
    `;

    console.log('✅ Service updated:', updateResult[0]);
    console.log('✅ Service deleted successfully:', service.name);

    return {
      success: true,
      message: 'Service deleted successfully',
      service: updateResult[0]
    };

  } catch (error) {
    console.error('❌ Error deleting service:', error);
    throw error;
  }
};

// Hard delete service (permanently remove from database)
export const hardDeleteService = async (id) => {
  try {
    console.log('🔄 Hard deleting service with ID:', id);

    // Validate ID
    if (!id || isNaN(parseInt(id))) {
      throw new Error('Invalid service ID provided');
    }

    // Check if service exists
    const existingServices = await sql`
      SELECT id, name FROM services WHERE id = ${parseInt(id)}
    `;

    if (existingServices.length === 0) {
      throw new Error('Service not found');
    }

    const service = existingServices[0];
    console.log('📋 Found service to hard delete:', service);

    // Check if service is being used by any vendors
    // Note: This would require checking the vendors table if services are stored as references
    // For now, we'll proceed with the deletion

    // Permanently delete from database
    const deleteResult = await sql`
      DELETE FROM services WHERE id = ${parseInt(id)}
      RETURNING id, name
    `;

    console.log('✅ Service permanently deleted:', deleteResult[0]);

    return {
      success: true,
      message: 'Service permanently deleted',
      service: deleteResult[0]
    };

  } catch (error) {
    console.error('❌ Error hard deleting service:', error);
    throw error;
  }
};

// Search services
export const searchServices = async (searchTerm) => {
  try {
    const services = await sql`
      SELECT
        id,
        name,
        description,
        category,
        "isActive",
        status,
        created_at,
        updated_at
      FROM services
      WHERE
        name ILIKE ${`%${searchTerm}%`} OR
        description ILIKE ${`%${searchTerm}%`} OR
        category ILIKE ${`%${searchTerm}%`}
      ORDER BY created_at DESC
    `;
    return services;
  } catch (error) {
    console.error('Error searching services:', error);
    throw error;
  }
};
