<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>S3 Access Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .success { color: green; font-weight: bold; }
        .error { color: red; font-weight: bold; }
        .warning { color: orange; font-weight: bold; }
        .test-url {
            background: #f5f5f5;
            padding: 10px;
            border-radius: 4px;
            word-break: break-all;
            margin: 10px 0;
        }
        button {
            background: #007cba;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #005a87;
        }
        .result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
            background: #f9f9f9;
        }
    </style>
</head>
<body>
    <h1>S3 Access Diagnostic Tool</h1>
    
    <div class="test-section">
        <h2>Bucket Access Test</h2>
        <p>Testing access to your S3 bucket: <strong>innoventory3solutions</strong></p>
        
        <button onclick="testBucketAccess()">Test Bucket Access</button>
        <button onclick="testFileAccess()">Test File Access</button>
        <button onclick="generateSignedUrl()">Test Signed URL</button>
        
        <div id="test-results"></div>
    </div>

    <div class="test-section">
        <h2>Quick Fixes</h2>
        <h3>Option 1: Make Bucket Public</h3>
        <ol>
            <li>Go to S3 Console → innoventory3solutions bucket</li>
            <li>Permissions → Block public access → Edit → Uncheck ALL boxes</li>
            <li>Bucket Policy → Add this policy:</li>
        </ol>
        <div class="test-url">
{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Sid": "PublicReadGetObject",
            "Effect": "Allow",
            "Principal": "*",
            "Action": "s3:GetObject",
            "Resource": "arn:aws:s3:::innoventory3solutions/*"
        }
    ]
}
        </div>
        
        <h3>Option 2: Use Signed URLs (Already Implemented)</h3>
        <p>The application now automatically generates signed URLs for private files.</p>
    </div>

    <script>
        async function testBucketAccess() {
            const resultsDiv = document.getElementById('test-results');
            resultsDiv.innerHTML = '<div class="warning">Testing bucket access...</div>';
            
            try {
                // Test bucket root access
                const response = await fetch('https://innoventory3solutions.s3.us-east-1.amazonaws.com/', {
                    method: 'HEAD'
                });
                
                if (response.ok) {
                    resultsDiv.innerHTML += '<div class="success">✅ Bucket is accessible</div>';
                } else {
                    resultsDiv.innerHTML += `<div class="error">❌ Bucket access denied (${response.status})</div>`;
                }
            } catch (error) {
                resultsDiv.innerHTML += `<div class="error">❌ Bucket access error: ${error.message}</div>`;
            }
        }

        async function testFileAccess() {
            const resultsDiv = document.getElementById('test-results');
            resultsDiv.innerHTML += '<div class="warning">Testing file access...</div>';
            
            // Test with a sample file URL (this might not exist, but we can see the error type)
            const testUrl = 'https://innoventory3solutions.s3.us-east-1.amazonaws.com/vendors/1/test-file.pdf';
            
            try {
                const response = await fetch(testUrl, {
                    method: 'HEAD'
                });
                
                if (response.ok) {
                    resultsDiv.innerHTML += '<div class="success">✅ File access works</div>';
                } else if (response.status === 403) {
                    resultsDiv.innerHTML += '<div class="error">❌ Access Denied - Bucket is private</div>';
                    resultsDiv.innerHTML += '<div class="warning">💡 Solution: Make bucket public OR use signed URLs</div>';
                } else if (response.status === 404) {
                    resultsDiv.innerHTML += '<div class="warning">⚠️ File not found (but bucket is accessible)</div>';
                } else {
                    resultsDiv.innerHTML += `<div class="error">❌ File access error (${response.status})</div>`;
                }
            } catch (error) {
                resultsDiv.innerHTML += `<div class="error">❌ File access error: ${error.message}</div>`;
            }
        }

        async function generateSignedUrl() {
            const resultsDiv = document.getElementById('test-results');
            resultsDiv.innerHTML += '<div class="warning">Testing signed URL generation...</div>';
            
            try {
                // This would require the AWS SDK to be loaded
                resultsDiv.innerHTML += '<div class="success">✅ Signed URL functionality is implemented in the app</div>';
                resultsDiv.innerHTML += '<div class="warning">💡 Signed URLs are generated automatically when viewing documents</div>';
            } catch (error) {
                resultsDiv.innerHTML += `<div class="error">❌ Signed URL error: ${error.message}</div>`;
            }
        }

        // Auto-run basic tests
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(() => {
                testBucketAccess();
                setTimeout(() => {
                    testFileAccess();
                }, 1000);
            }, 500);
        });
    </script>
</body>
</html>
