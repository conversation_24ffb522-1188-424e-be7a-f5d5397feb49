import React, { useState, useEffect } from 'react';
import { 
  PlusIcon, 
  PencilIcon, 
  TrashIcon, 
  ArrowPathIcon,
  MagnifyingGlassIcon,
  CheckCircleIcon,
  XCircleIcon
} from '@heroicons/react/24/outline';
import {
  getAllAreaOfExpertise,
  createAreaOfExpertise,
  updateAreaOfExpertise,
  deleteAreaOfExpertise,
  restoreAreaOfExpertise,
  searchAreaOfExpertise
} from '../services/areaOfExpertiseService';
import { setupAreaOfExpertise } from '../utils/setupAreaOfExpertise';
import { addStartupBenefitsColumn } from '../utils/addStartupBenefitsColumn';

const AreaOfExpertise = () => {
  const [areaOfExpertise, setAreaOfExpertise] = useState([]);
  const [loading, setLoading] = useState(true);
  const [showAddForm, setShowAddForm] = useState(false);
  const [editingItem, setEditingItem] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    isActive: true
  });
  const [errors, setErrors] = useState({});

  useEffect(() => {
    loadAreaOfExpertise();
  }, []);

  const loadAreaOfExpertise = async () => {
    try {
      setLoading(true);
      const data = await getAllAreaOfExpertise();
      setAreaOfExpertise(data);
    } catch (error) {
      console.error('Error loading area of expertise:', error);
      alert('Error loading area of expertise data');
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = async () => {
    if (!searchTerm.trim()) {
      loadAreaOfExpertise();
      return;
    }

    try {
      setLoading(true);
      const results = await searchAreaOfExpertise(searchTerm);
      setAreaOfExpertise(results);
    } catch (error) {
      console.error('Error searching area of expertise:', error);
      alert('Error searching area of expertise');
    } finally {
      setLoading(false);
    }
  };

  const handleSetupDatabase = async () => {
    try {
      setLoading(true);
      await setupAreaOfExpertise();
      alert('Database setup completed successfully! Please refresh the page.');
      loadAreaOfExpertise();
    } catch (error) {
      console.error('Error setting up database:', error);
      alert('Error setting up database: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  const handleSetupStartupBenefits = async () => {
    try {
      setLoading(true);
      const result = await addStartupBenefitsColumn();
      if (result.success) {
        alert('Startup Benefits column added successfully! You can now use the vendor forms.');
      } else {
        alert('Error: ' + result.error);
      }
    } catch (error) {
      console.error('Error setting up startup benefits:', error);
      alert('Error setting up startup benefits: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  const validateForm = () => {
    const newErrors = {};

    if (!formData.name.trim()) {
      newErrors.name = 'Name is required';
    } else if (formData.name.length < 2) {
      newErrors.name = 'Name must be at least 2 characters';
    }

    if (formData.description && formData.description.length > 500) {
      newErrors.description = 'Description must be less than 500 characters';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    try {
      setLoading(true);

      if (editingItem) {
        await updateAreaOfExpertise(editingItem.id, formData);
        alert('Area of expertise updated successfully!');
      } else {
        await createAreaOfExpertise(formData);
        alert('Area of expertise created successfully!');
      }

      setFormData({ name: '', description: '', isActive: true });
      setEditingItem(null);
      setShowAddForm(false);
      setErrors({});
      await loadAreaOfExpertise();

    } catch (error) {
      console.error('Error saving area of expertise:', error);
      alert(error.message || 'Error saving area of expertise');
    } finally {
      setLoading(false);
    }
  };

  const handleEdit = (item) => {
    setEditingItem(item);
    setFormData({
      name: item.name,
      description: item.description || '',
      isActive: item.isActive
    });
    setShowAddForm(true);
    setErrors({});
  };

  const handleDelete = async (id) => {
    if (!confirm('Are you sure you want to delete this area of expertise?')) {
      return;
    }

    try {
      setLoading(true);
      await deleteAreaOfExpertise(id);
      alert('Area of expertise deleted successfully!');
      await loadAreaOfExpertise();
    } catch (error) {
      console.error('Error deleting area of expertise:', error);
      alert(error.message || 'Error deleting area of expertise');
    } finally {
      setLoading(false);
    }
  };

  const handleRestore = async (id) => {
    try {
      setLoading(true);
      await restoreAreaOfExpertise(id);
      alert('Area of expertise restored successfully!');
      await loadAreaOfExpertise();
    } catch (error) {
      console.error('Error restoring area of expertise:', error);
      alert(error.message || 'Error restoring area of expertise');
    } finally {
      setLoading(false);
    }
  };

  const resetForm = () => {
    setFormData({ name: '', description: '', isActive: true });
    setEditingItem(null);
    setShowAddForm(false);
    setErrors({});
  };

  const filteredAreaOfExpertise = areaOfExpertise.filter(item =>
    item.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    (item.description && item.description.toLowerCase().includes(searchTerm.toLowerCase()))
  );

  return (
    <div className="p-6">
      {/* Header */}
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Area of Expertise</h1>
          <p className="text-gray-600">Manage areas of expertise for points of contact</p>
        </div>
        <div className="flex space-x-3">
          <button
            onClick={handleSetupDatabase}
            disabled={loading}
            className="btn-secondary flex items-center space-x-2"
            title="Setup database table and seed initial data"
          >
            <ArrowPathIcon className="w-5 h-5" />
            <span>Setup Database</span>
          </button>
          <button
            onClick={handleSetupStartupBenefits}
            disabled={loading}
            className="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-md flex items-center space-x-2"
            title="Add startup_benefits column to vendors table"
          >
            <ArrowPathIcon className="w-5 h-5" />
            <span>Fix Startup Benefits</span>
          </button>
          <button
            onClick={() => setShowAddForm(true)}
            className="btn-primary flex items-center space-x-2"
          >
            <PlusIcon className="w-5 h-5" />
            <span>Add New Area</span>
          </button>
        </div>
      </div>

      {/* Search */}
      <div className="mb-6">
        <div className="flex space-x-4">
          <div className="flex-1">
            <input
              type="text"
              placeholder="Search area of expertise..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="input-field"
            />
          </div>
          <button
            onClick={handleSearch}
            className="btn-secondary flex items-center space-x-2"
          >
            <MagnifyingGlassIcon className="w-5 h-5" />
            <span>Search</span>
          </button>
          <button
            onClick={() => {
              setSearchTerm('');
              loadAreaOfExpertise();
            }}
            className="btn-secondary flex items-center space-x-2"
          >
            <ArrowPathIcon className="w-5 h-5" />
            <span>Reset</span>
          </button>
        </div>
      </div>

      {/* Area of Expertise List */}
      <div className="bg-white rounded-lg shadow">
        <div className="px-6 py-4 border-b border-gray-200">
          <h2 className="text-lg font-medium text-gray-900">
            Area of Expertise ({filteredAreaOfExpertise.length})
          </h2>
        </div>

        {loading ? (
          <div className="p-6 text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
            <p className="mt-2 text-gray-600">Loading...</p>
          </div>
        ) : filteredAreaOfExpertise.length === 0 ? (
          <div className="p-6 text-center text-gray-500">
            {searchTerm ? 'No area of expertise found matching your search.' : 'No area of expertise found. Add one to get started.'}
          </div>
        ) : (
          <div className="divide-y divide-gray-200">
            {filteredAreaOfExpertise.map((item) => (
              <div key={item.id} className="p-6 hover:bg-gray-50">
                <div className="flex items-center justify-between">
                  <div className="flex-1">
                    <div className="flex items-center space-x-3">
                      <h3 className="text-lg font-medium text-gray-900">{item.name}</h3>
                      {item.isActive ? (
                        <CheckCircleIcon className="w-5 h-5 text-green-500" title="Active" />
                      ) : (
                        <XCircleIcon className="w-5 h-5 text-red-500" title="Inactive" />
                      )}
                    </div>
                    {item.description && (
                      <p className="mt-1 text-gray-600">{item.description}</p>
                    )}
                    <p className="mt-2 text-sm text-gray-500">
                      Created: {new Date(item.createdAt).toLocaleDateString()}
                      {item.updatedAt && item.updatedAt !== item.createdAt && (
                        <span> • Updated: {new Date(item.updatedAt).toLocaleDateString()}</span>
                      )}
                    </p>
                  </div>
                  <div className="flex items-center space-x-2">
                    <button
                      onClick={() => handleEdit(item)}
                      className="p-2 text-blue-600 hover:bg-blue-50 rounded-lg"
                      title="Edit"
                    >
                      <PencilIcon className="w-5 h-5" />
                    </button>
                    {item.isActive ? (
                      <button
                        onClick={() => handleDelete(item.id)}
                        className="p-2 text-red-600 hover:bg-red-50 rounded-lg"
                        title="Delete"
                      >
                        <TrashIcon className="w-5 h-5" />
                      </button>
                    ) : (
                      <button
                        onClick={() => handleRestore(item.id)}
                        className="p-2 text-green-600 hover:bg-green-50 rounded-lg"
                        title="Restore"
                      >
                        <ArrowPathIcon className="w-5 h-5" />
                      </button>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Add/Edit Form Modal */}
      {showAddForm && (
        <div className="fixed inset-0 z-50 overflow-y-auto">
          <div className="flex items-center justify-center min-h-screen px-4">
            <div className="fixed inset-0 bg-black opacity-50" onClick={resetForm}></div>
            <div className="relative bg-white rounded-lg max-w-md w-full p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">
                {editingItem ? 'Edit Area of Expertise' : 'Add New Area of Expertise'}
              </h3>

              <form onSubmit={handleSubmit} className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Name *
                  </label>
                  <input
                    type="text"
                    value={formData.name}
                    onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                    className={`input-field ${errors.name ? 'border-red-300' : ''}`}
                    placeholder="e.g., Patent Law, Trademark Law"
                  />
                  {errors.name && <p className="text-red-600 text-sm mt-1">{errors.name}</p>}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Description
                  </label>
                  <textarea
                    value={formData.description}
                    onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                    className={`input-field ${errors.description ? 'border-red-300' : ''}`}
                    rows="3"
                    placeholder="Brief description of this area of expertise"
                  />
                  {errors.description && <p className="text-red-600 text-sm mt-1">{errors.description}</p>}
                </div>

                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="isActive"
                    checked={formData.isActive}
                    onChange={(e) => setFormData({ ...formData, isActive: e.target.checked })}
                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                  <label htmlFor="isActive" className="ml-2 text-sm text-gray-700">
                    Active
                  </label>
                </div>

                <div className="flex justify-end space-x-3 pt-4">
                  <button
                    type="button"
                    onClick={resetForm}
                    className="btn-secondary"
                  >
                    Cancel
                  </button>
                  <button
                    type="submit"
                    className="btn-primary"
                    disabled={loading}
                  >
                    {loading ? 'Saving...' : (editingItem ? 'Update' : 'Create')}
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default AreaOfExpertise;
