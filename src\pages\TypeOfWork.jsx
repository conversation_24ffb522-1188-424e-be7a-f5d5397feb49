import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { PlusIcon, PencilIcon, TrashIcon, CheckCircleIcon, XCircleIcon, EyeIcon } from '@heroicons/react/24/outline';
import DataTable from '../components/DataTable/DataTable';

const TypeOfWork = () => {
  const navigate = useNavigate();
  const [showAddForm, setShowAddForm] = useState(false);
  const [showViewModal, setShowViewModal] = useState(false);
  const [typeOfWorkData, setTypeOfWorkData] = useState([]);
  const [loading, setLoading] = useState(true);
  const [formData, setFormData] = useState({
    name: '',
    description: ''
  });
  const [editingId, setEditingId] = useState(null);



  // Transform database data to match expected format
  const transformTypeOfWorkData = (data) => {
    return data.map(item => ({
      ...item,
      status: item.isActive !== undefined ? (item.isActive ? 'Active' : 'Inactive') : (item.status || 'Active'),
      createdDate: item.createdAt ? new Date(item.createdAt).toLocaleDateString() : (item.createdDate || new Date().toLocaleDateString())
    }));
  };

  // Load type of work from database
  const loadTypeOfWork = async () => {
    try {
      setLoading(true);
      console.log('🔄 Loading type of work from database...');
      // Import and use database service
      const { getAllTypeOfWork } = await import('../services/typeOfWorkService');
      const dbTypeOfWork = await getAllTypeOfWork();
      console.log('✅ Type of work loaded:', dbTypeOfWork);

      setTypeOfWorkData(transformTypeOfWorkData(dbTypeOfWork || []));
    } catch (err) {
      console.error('❌ Error loading type of work:', err);
      setTypeOfWorkData([]);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadTypeOfWork();
  }, []);



  // Toggle status function
  const toggleStatus = async (id, currentStatus) => {
    try {
      const newStatus = currentStatus === 'Active' ? 'Inactive' : 'Active';
      console.log(`🔄 Toggling status for ${id} from ${currentStatus} to ${newStatus}`);

      // Update in database using the status update function
      const { updateTypeOfWorkStatus } = await import('../services/typeOfWorkService');
      const result = await updateTypeOfWorkStatus(id, newStatus);
      console.log(`✅ Status updated successfully:`, result);

      // Reload the entire data to ensure consistency
      await loadTypeOfWork();
      alert(`✅ Status updated to ${newStatus} successfully!`);
    } catch (error) {
      console.error('❌ Error updating status:', error);
      alert(`❌ Error updating status: ${error.message || 'Please try again.'}`);
    }
  };

  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();

    // Validation
    if (!formData.name || formData.name.trim() === '') {
      alert('❌ Work Type Name is required');
      return;
    }

    if (!formData.description || formData.description.trim() === '') {
      alert('❌ Description is required');
      return;
    }

    if (formData.name.trim().length < 3) {
      alert('❌ Work Type Name must be at least 3 characters long');
      return;
    }

    if (formData.description.trim().length < 10) {
      alert('❌ Description must be at least 10 characters long');
      return;
    }

    try {
      setLoading(true);
      console.log('🚀 Submitting type of work form...');
      console.log('📋 Form data:', formData);

      if (editingId) {
        // Update existing type of work
        console.log(`✏️ Updating type of work with ID: ${editingId}`);
        const { updateTypeOfWork } = await import('../services/typeOfWorkService');
        const result = await updateTypeOfWork(editingId, formData);
        console.log('✅ Type of work updated successfully:', result);
        alert('✅ Type of work updated successfully!');
      } else {
        // Create new type of work
        console.log('🆕 Creating new type of work');
        const { createTypeOfWork } = await import('../services/typeOfWorkService');
        const result = await createTypeOfWork(formData);
        console.log('✅ Type of work created successfully:', result);
        alert('✅ Type of work created successfully!');
      }

      // Reset form and reload data
      setFormData({ name: '', description: '' });
      setShowAddForm(false);
      setEditingId(null);
      await loadTypeOfWork();
    } catch (error) {
      console.error('❌ Error saving type of work:', error);
      alert(`❌ Error saving type of work: ${error.message || 'Please try again.'}`);
    } finally {
      setLoading(false);
    }
  };



  // Handle delete
  const handleDelete = async (id, name) => {
    const confirmMessage = `Are you sure you want to delete "${name}"?\n\nThis action cannot be undone and will permanently remove this type of work from the system.`;

    if (confirm(confirmMessage)) {
      try {
        console.log(`🗑️ Deleting type of work: ${name} (ID: ${id})`);
        const { deleteTypeOfWork } = await import('../services/typeOfWorkService');
        await deleteTypeOfWork(id);
        console.log('✅ Type of work deleted successfully');

        await loadTypeOfWork();
        alert(`✅ "${name}" has been deleted successfully!`);
      } catch (error) {
        console.error('❌ Error deleting type of work:', error);
        alert(`❌ Error deleting type of work: ${error.message || 'Please try again.'}`);
      }
    }
  };

  // Handle edit
  const handleEdit = (item) => {
    setFormData({
      name: item.name,
      description: item.description
    });
    setEditingId(item.id);
    setShowAddForm(true);
  };

  const columns = [
    {
      key: 'name',
      label: 'Work Type',
      sortable: true,
      render: (value) => (
        <div className="font-medium text-gray-900">{value}</div>
      )
    },
    {
      key: 'description',
      label: 'Description',
      sortable: false,
      render: (value) => (
        <div className="max-w-xs text-gray-600">{value}</div>
      )
    },
    {
      key: 'status',
      label: 'Status',
      sortable: true,
      render: (value) => (
        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
          value === 'Active'
            ? 'bg-green-100 text-green-800'
            : 'bg-red-100 text-red-800'
        }`}>
          {value}
        </span>
      )
    },
    {
      key: 'createdDate',
      label: 'Created Date',
      sortable: true,
      render: (value) => (
        <span className="text-gray-600">{value}</span>
      )
    },
    {
      key: 'actions',
      label: 'Actions',
      sortable: false,
      filterable: false,
      render: (_, row) => (
        <div className="flex space-x-2">
          <button
            onClick={() => {
              setFormData({
                name: row.name,
                description: row.description
              });
              setEditingId(row.id);
              setShowViewModal(true);
            }}
            className="text-gray-600 hover:text-gray-800"
            title="View"
          >
            <EyeIcon className="h-4 w-4" />
          </button>
          <button
            onClick={() => handleEdit(row)}
            className="text-blue-600 hover:text-blue-800"
            title="Edit"
          >
            <PencilIcon className="h-4 w-4" />
          </button>
          <button
            onClick={() => toggleStatus(row.id, row.status)}
            className={`${
              row.status === 'Active'
                ? 'text-green-600 hover:text-green-800'
                : 'text-gray-400 hover:text-gray-600'
            }`}
            title={`${row.status} - Click to toggle to ${row.status === 'Active' ? 'Inactive' : 'Active'}`}
          >
            {row.status === 'Active' ? (
              <CheckCircleIcon className="h-4 w-4" />
            ) : (
              <XCircleIcon className="h-4 w-4" />
            )}
          </button>
          <button
            onClick={() => handleDelete(row.id, row.name)}
            className="text-red-600 hover:text-red-800"
            title="Delete"
          >
            <TrashIcon className="h-4 w-4" />
          </button>
        </div>
      )
    }
  ];

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex justify-between items-center border-b border-gray-200 pb-4">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Type of Work</h1>
          <p className="mt-2 text-gray-600">Manage different types of work and services</p>
        </div>
        <div className="flex space-x-3">
          <button
            onClick={() => setShowAddForm(true)}
            className="btn-primary flex items-center"
          >
            <PlusIcon className="h-5 w-5 mr-2" />
            Add New Work Type
          </button>
        </div>
      </div>

      {/* Status Legend */}
      <div className="bg-gray-50 rounded-lg p-3 mb-4">
        <div className="flex items-center space-x-6 text-sm text-gray-600">
          <span className="font-medium">Action Icons:</span>
          <div className="flex items-center space-x-2">
            <EyeIcon className="w-4 h-4 text-gray-600" />
            <span>View</span>
          </div>
          <div className="flex items-center space-x-2">
            <PencilIcon className="w-4 h-4 text-blue-600" />
            <span>Edit</span>
          </div>
          <div className="flex items-center space-x-2">
            <CheckCircleIcon className="w-4 h-4 text-green-600" />
            <span>Toggle Active</span>
          </div>
          <div className="flex items-center space-x-2">
            <XCircleIcon className="w-4 h-4 text-gray-400" />
            <span>Toggle Inactive</span>
          </div>
          <div className="flex items-center space-x-2">
            <TrashIcon className="w-4 h-4 text-red-600" />
            <span>Delete</span>
          </div>
        </div>
      </div>

      {/* Data Table */}
      <div className="card">
        <DataTable
          data={typeOfWorkData}
          columns={columns}
          title="Type of Work"
          searchPlaceholder="Search work types..."
          itemsPerPage={50}
        />
      </div>

      {/* Add/Edit Form Modal */}
      {showAddForm && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <div className="mt-3">
              <h3 className="text-lg font-medium text-gray-900 mb-4">
                {editingId ? 'Edit Work Type' : 'Add New Work Type'}
              </h3>

              <form onSubmit={handleSubmit} className="space-y-4">
                {/* Work Type Name */}
                <div>
                  <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-1">
                    Work Type Name *
                  </label>
                  <input
                    type="text"
                    id="name"
                    value={formData.name}
                    onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="Enter work type name"
                    required
                  />
                </div>

                {/* Description */}
                <div>
                  <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-1">
                    Description *
                  </label>
                  <textarea
                    id="description"
                    value={formData.description}
                    onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                    rows={3}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="Enter description"
                    required
                  />
                </div>

                {/* Created Date Info */}
                <div className="text-sm text-gray-500">
                  <p>Created Date: {new Date().toLocaleDateString()} (Auto-generated)</p>
                  <p>Status: Active (Default)</p>
                </div>

                {/* Form Actions */}
                <div className="flex justify-end space-x-3 pt-4">
                  <button
                    type="button"
                    onClick={() => {
                      setShowAddForm(false);
                      setEditingId(null);
                      setFormData({ name: '', description: '' });
                    }}
                    className="btn-secondary"
                  >
                    Cancel
                  </button>
                  <button
                    type="submit"
                    disabled={loading}
                    className="btn-primary disabled:opacity-50"
                  >
                    {loading ? 'Saving...' : (editingId ? 'Update' : 'Save')}
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      )}

      {/* View Modal */}
      {showViewModal && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <div className="mt-3">
              <h3 className="text-lg font-medium text-gray-900 mb-4">
                View Work Type
              </h3>

              <div className="space-y-4">
                {/* Work Type Name */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Work Type Name
                  </label>
                  <div className="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-50">
                    {formData.name}
                  </div>
                </div>

                {/* Description */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Description
                  </label>
                  <div className="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-50 min-h-[80px]">
                    {formData.description}
                  </div>
                </div>

                {/* Status and Date Info */}
                <div className="text-sm text-gray-600 bg-gray-50 p-3 rounded-md">
                  <p><strong>Status:</strong> {typeOfWorkData.find(item => item.id === editingId)?.status || 'Active'}</p>
                  <p><strong>Created Date:</strong> {typeOfWorkData.find(item => item.id === editingId)?.createdDate || 'N/A'}</p>
                </div>

                {/* Modal Actions */}
                <div className="flex justify-end space-x-3 pt-4">
                  <button
                    type="button"
                    onClick={() => {
                      setShowViewModal(false);
                      setEditingId(null);
                      setFormData({ name: '', description: '' });
                    }}
                    className="btn-secondary"
                  >
                    Close
                  </button>
                  <button
                    type="button"
                    onClick={() => {
                      setShowViewModal(false);
                      setShowAddForm(true);
                    }}
                    className="btn-primary"
                  >
                    Edit
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default TypeOfWork;
