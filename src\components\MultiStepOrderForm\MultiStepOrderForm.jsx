import { useState, useEffect } from 'react';
import { ChevronLeftIcon, ChevronRightIcon, CheckIcon } from '@heroicons/react/24/outline';
import S3FileUpload from '../FileUpload/S3FileUpload';

const MultiStepOrderForm = ({ onSubmit, onCancel, clients = [], vendors = [], activeWorkTypes = [] }) => {
  const [currentStep, setCurrentStep] = useState(1);
  const [formData, setFormData] = useState({
    // Customer Part
    orderOnboardingDate: new Date().toISOString().split('T')[0],
    orderReferenceNumber: '',
    client: '',
    typeOfWork: '',
    dateOfWorkCompletionExpected: '',
    dateOfPaymentExpected: '',
    totalInvoiceValue: '',
    totalValueGstGovtFees: '',

    // Vendor Part
    dateOfOnboardingVendor: '',
    vendorName: '',
    currentStatus: 'Yet to start',
    statusComments: '',
    dateOfStatusChange: new Date().toISOString().split('T')[0],
    dateOfWorkCompletionExpectedFromVendor: '',
    amountToBePaidToVendor: '',
    amountPaidToVendor: '',

    // Order Part
    dateOfCompletionOfOrder: '',
    countryToBeImplementedIn: '',
    applicationDairyNumber: '',
    dateOfFilingAtPO: '',
    lawyerReferenceNumber: ''
  });

  const [uploadedFiles, setUploadedFiles] = useState({
    orderFriendlyImage: [],
    documentsProvidedByOrder: [],
    invoiceForCustomer: [],
    documentsProvidedByVendor: [],
    invoiceFromVendor: [],
    workDocuments: []
  });

  const [statusHistory, setStatusHistory] = useState([]);

  const [tempOrderId] = useState(() => `temp-order-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`);

  const steps = [
    { id: 1, name: 'Customer', description: 'Select customer details' },
    { id: 2, name: 'Vendor', description: 'Choose vendor and terms' },
    { id: 3, name: 'Order', description: 'Order details and completion' }
  ];

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));

    // Auto-populate client details when client is selected
    if (name === 'client' && value) {
      const selectedClient = clients.find(c => c.company_name === value);
      setFormData(prev => ({
        ...prev,
        clientDetails: selectedClient
      }));
    }

    // Auto-populate vendor details when vendor is selected
    if (name === 'vendorName' && value) {
      const selectedVendor = vendors.find(v => v.company_name === value);
      setFormData(prev => ({
        ...prev,
        vendorDetails: selectedVendor
      }));
    }
  };

  const handleStatusChange = (newStatus) => {
    const now = new Date().toISOString().split('T')[0];

    // Update form data with new status and date
    setFormData(prev => ({
      ...prev,
      currentStatus: newStatus,
      dateOfStatusChange: now,
      statusComments: '' // Reset comments for new status
    }));

    // Add to status history
    setStatusHistory(prev => [...prev, {
      status: newStatus,
      date: now,
      comments: '', // Will be filled when user adds comments
      timestamp: new Date().toISOString()
    }]);
  };

  const nextStep = () => {
    if (currentStep < 3) {
      setCurrentStep(currentStep + 1);
    }
  };

  const prevStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    onSubmit(formData);
  };

  const isStepValid = (step) => {
    switch (step) {
      case 1:
        return (
          formData.orderOnboardingDate !== '' &&
          formData.client !== '' &&
          formData.typeOfWork !== '' &&
          formData.dateOfWorkCompletionExpected !== '' &&
          formData.dateOfPaymentExpected !== '' &&
          formData.totalInvoiceValue !== '' &&
          uploadedFiles.orderFriendlyImage.length > 0 &&
          uploadedFiles.documentsProvidedByOrder.length > 0 &&
          uploadedFiles.invoiceForCustomer.length > 0
        );
      case 2:
        return (
          formData.dateOfOnboardingVendor !== '' &&
          formData.vendorName !== '' &&
          formData.currentStatus !== '' &&
          formData.statusComments !== ''
        );
      case 3:
        return (
          formData.dateOfCompletionOfOrder !== '' &&
          formData.countryToBeImplementedIn !== ''
        );
      default:
        return false;
    }
  };

  const generateOrderReference = () => {
    const timestamp = Date.now();
    const random = Math.random().toString(36).substr(2, 4).toUpperCase();
    return `IS-${timestamp}-${random}`;
  };

  useEffect(() => {
    if (!formData.orderReferenceNumber) {
      setFormData(prev => ({
        ...prev,
        orderReferenceNumber: generateOrderReference()
      }));
    }
  }, []);

  return (
    <div className="max-w-4xl mx-auto">
      {/* Progress Steps */}
      <div className="mb-8">
        <div className="flex items-center justify-between">
          {steps.map((step, index) => (
            <div key={step.id} className="flex items-center">
              <div className={`flex items-center justify-center w-10 h-10 rounded-full border-2 ${
                currentStep > step.id 
                  ? 'bg-green-600 border-green-600 text-white' 
                  : currentStep === step.id 
                    ? 'bg-blue-600 border-blue-600 text-white' 
                    : 'bg-gray-100 border-gray-300 text-gray-500'
              }`}>
                {currentStep > step.id ? (
                  <CheckIcon className="w-6 h-6" />
                ) : (
                  <span className="text-sm font-medium">{step.id}</span>
                )}
              </div>
              <div className="ml-3">
                <p className={`text-sm font-medium ${
                  currentStep >= step.id ? 'text-gray-900' : 'text-gray-500'
                }`}>
                  {step.name}
                </p>
                <p className="text-xs text-gray-500">{step.description}</p>
              </div>
              {index < steps.length - 1 && (
                <div className={`flex-1 mx-4 h-0.5 ${
                  currentStep > step.id ? 'bg-green-600' : 'bg-gray-300'
                }`} />
              )}
            </div>
          ))}
        </div>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Step 1: Customer Part */}
        {currentStep === 1 && (
          <div className="card">
            <h2 className="text-xl font-semibold text-gray-900 mb-6">Customer Part</h2>

            <div className="space-y-6">
              {/* Basic Order Information */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Order Onboarding Date *
                  </label>
                  <input
                    type="date"
                    name="orderOnboardingDate"
                    value={formData.orderOnboardingDate}
                    onChange={handleInputChange}
                    required
                    className="input-field"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Order Reference Number (Auto Generated)
                  </label>
                  <input
                    type="text"
                    name="orderReferenceNumber"
                    value={formData.orderReferenceNumber}
                    readOnly
                    className="input-field bg-gray-50"
                    placeholder="Auto-generated"
                  />
                </div>
              </div>

              {/* Order Friendly Image */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Order Friendly Image *
                </label>
                <S3FileUpload
                  module="orders"
                  recordId={tempOrderId}
                  fileType="friendly"
                  allowedTypes="images"
                  multiple={false}
                  maxFiles={1}
                  label="Upload Order Friendly Image"
                  description="Upload an image that represents this order"
                  onFilesUploaded={(files) => {
                    setUploadedFiles(prev => ({
                      ...prev,
                      orderFriendlyImage: files
                    }));
                  }}
                  onFileDeleted={(deletedFile, remainingFiles) => {
                    setUploadedFiles(prev => ({
                      ...prev,
                      orderFriendlyImage: remainingFiles
                    }));
                  }}
                  existingFiles={uploadedFiles.orderFriendlyImage || []}
                />
              </div>

              {/* Customer and Type of Work */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Customer *
                  </label>
                  <select
                    name="client"
                    value={formData.client}
                    onChange={handleInputChange}
                    required
                    className="input-field"
                  >
                    <option value="">Select customer</option>
                    {clients.map((client) => (
                      <option key={client.id} value={client.company_name}>
                        {client.company_name}
                      </option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Type of Work *
                  </label>
                  <select
                    name="typeOfWork"
                    value={formData.typeOfWork}
                    onChange={handleInputChange}
                    required
                    className="input-field"
                  >
                    <option value="">Select type of work</option>
                    {activeWorkTypes.map((workType) => (
                      <option key={workType.id} value={workType.name}>
                        {workType.name}
                      </option>
                    ))}
                  </select>
                </div>
              </div>

              {/* Dates */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Date of Work Completion Expected *
                  </label>
                  <input
                    type="date"
                    name="dateOfWorkCompletionExpected"
                    value={formData.dateOfWorkCompletionExpected}
                    onChange={handleInputChange}
                    required
                    className="input-field"
                    placeholder="dd-mm-yyyy"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Date of Payment Expected *
                  </label>
                  <input
                    type="date"
                    name="dateOfPaymentExpected"
                    value={formData.dateOfPaymentExpected}
                    onChange={handleInputChange}
                    required
                    className="input-field"
                    placeholder="dd-mm-yyyy"
                  />
                </div>
              </div>

              {/* File Uploads */}
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Documents Provided (as part of order) *
                  </label>
                  <S3FileUpload
                    module="orders"
                    recordId={tempOrderId}
                    fileType="orderdocs"
                    allowedTypes="all"
                    multiple={true}
                    maxFiles={5}
                    label="Upload Order Documents"
                    description="Upload documents provided as part of this order"
                    onFilesUploaded={(files) => {
                      setUploadedFiles(prev => ({
                        ...prev,
                        documentsProvidedByOrder: files
                      }));
                    }}
                    onFileDeleted={(deletedFile, remainingFiles) => {
                      setUploadedFiles(prev => ({
                        ...prev,
                        documentsProvidedByOrder: remainingFiles
                      }));
                    }}
                    existingFiles={uploadedFiles.documentsProvidedByOrder || []}
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Invoice for the Customer *
                  </label>
                  <S3FileUpload
                    module="orders"
                    recordId={tempOrderId}
                    fileType="customerinvoice"
                    allowedTypes="all"
                    multiple={true}
                    maxFiles={3}
                    label="Upload Customer Invoice"
                    description="Upload invoice documents for the customer"
                    onFilesUploaded={(files) => {
                      setUploadedFiles(prev => ({
                        ...prev,
                        invoiceForCustomer: files
                      }));
                    }}
                    onFileDeleted={(deletedFile, remainingFiles) => {
                      setUploadedFiles(prev => ({
                        ...prev,
                        invoiceForCustomer: remainingFiles
                      }));
                    }}
                    existingFiles={uploadedFiles.invoiceForCustomer || []}
                  />
                </div>
              </div>

              {/* Financial Information */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Total Invoice Value (INR) *
                  </label>
                  <input
                    type="number"
                    name="totalInvoiceValue"
                    value={formData.totalInvoiceValue}
                    onChange={handleInputChange}
                    required
                    className="input-field"
                    placeholder="Enter total invoice value in INR"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Total value of GST + Govt fees (INR)
                  </label>
                  <input
                    type="number"
                    name="totalValueGstGovtFees"
                    value={formData.totalValueGstGovtFees}
                    onChange={handleInputChange}
                    className="input-field"
                    placeholder="Enter GST + Government fees in INR"
                  />
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Step 2: Vendor Part */}
        {currentStep === 2 && (
          <div className="card">
            <h2 className="text-xl font-semibold text-gray-900 mb-6">Vendor Part</h2>

            <div className="space-y-6">
              {/* Date of Onboarding Vendor and Vendor Name */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Date of Onboarding vendor for this order *
                  </label>
                  <input
                    type="date"
                    name="dateOfOnboardingVendor"
                    value={formData.dateOfOnboardingVendor}
                    onChange={handleInputChange}
                    required
                    className="input-field"
                    placeholder="dd-mm-yyyy"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Vendor Name *
                  </label>
                  <select
                    name="vendorName"
                    value={formData.vendorName}
                    onChange={handleInputChange}
                    required
                    className="input-field"
                  >
                    <option value="">Select vendor</option>
                    {vendors.map((vendor) => (
                      <option key={vendor.id} value={vendor.company_name}>
                        {vendor.company_name}
                      </option>
                    ))}
                  </select>
                </div>
              </div>

              {/* Status and Comments */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Current Status *
                  </label>
                  <select
                    name="currentStatus"
                    value={formData.currentStatus}
                    onChange={(e) => handleStatusChange(e.target.value)}
                    required
                    className="input-field"
                  >
                    <option value="">Select current status</option>
                    <option value="Yet to start">Yet to start</option>
                    <option value="Pending with client">Pending with client</option>
                    <option value="Pending with Vendor">Pending with Vendor</option>
                    <option value="blocked">blocked</option>
                    <option value="completed">completed</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Date of change of status
                  </label>
                  <input
                    type="date"
                    name="dateOfStatusChange"
                    value={formData.dateOfStatusChange}
                    readOnly
                    className="input-field bg-gray-50"
                    placeholder="dd-mm-yyyy"
                  />
                  <p className="text-xs text-gray-500 mt-1">Auto-updated when status changes</p>
                </div>
              </div>

              {/* Comments */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Comment *
                </label>
                <textarea
                  name="statusComments"
                  value={formData.statusComments}
                  onChange={handleInputChange}
                  required
                  rows={3}
                  className="input-field"
                  placeholder="Required when status changes. All comments will be stored for showing on order page."
                />
                <p className="text-xs text-gray-500 mt-1">
                  Anytime the change happens to the status, we need to ask for the comments. All the comments will be stored for showing on order page.
                </p>
              </div>

              {/* Date of work completion expected from vendor */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Date of work completion expected from vendor
                </label>
                <input
                  type="date"
                  name="dateOfWorkCompletionExpectedFromVendor"
                  value={formData.dateOfWorkCompletionExpectedFromVendor}
                  onChange={handleInputChange}
                  className="input-field"
                  placeholder="dd-mm-yyyy"
                />
              </div>

              {/* File Uploads */}
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Documents Provided (as part of order) by vendor
                  </label>
                  <S3FileUpload
                    module="orders"
                    recordId={tempOrderId}
                    fileType="vendordocs"
                    allowedTypes="all"
                    multiple={true}
                    maxFiles={5}
                    label="Upload Vendor Documents"
                    description="Upload documents provided by vendor as part of this order"
                    onFilesUploaded={(files) => {
                      setUploadedFiles(prev => ({
                        ...prev,
                        documentsProvidedByVendor: files
                      }));
                    }}
                    onFileDeleted={(deletedFile, remainingFiles) => {
                      setUploadedFiles(prev => ({
                        ...prev,
                        documentsProvidedByVendor: remainingFiles
                      }));
                    }}
                    existingFiles={uploadedFiles.documentsProvidedByVendor || []}
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Invoice from the Vendor
                  </label>
                  <S3FileUpload
                    module="orders"
                    recordId={tempOrderId}
                    fileType="vendorinvoice"
                    allowedTypes="all"
                    multiple={true}
                    maxFiles={3}
                    label="Upload Vendor Invoice"
                    description="Upload invoice documents from the vendor"
                    onFilesUploaded={(files) => {
                      setUploadedFiles(prev => ({
                        ...prev,
                        invoiceFromVendor: files
                      }));
                    }}
                    onFileDeleted={(deletedFile, remainingFiles) => {
                      setUploadedFiles(prev => ({
                        ...prev,
                        invoiceFromVendor: remainingFiles
                      }));
                    }}
                    existingFiles={uploadedFiles.invoiceFromVendor || []}
                  />
                </div>
              </div>

              {/* Amount Information */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Amount to be Paid to vendor (INR)
                  </label>
                  <input
                    type="number"
                    name="amountToBePaidToVendor"
                    value={formData.amountToBePaidToVendor}
                    onChange={handleInputChange}
                    className="input-field"
                    placeholder="Enter amount to be paid in INR"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Amount Paid to vendor (INR)
                  </label>
                  <input
                    type="number"
                    name="amountPaidToVendor"
                    value={formData.amountPaidToVendor}
                    onChange={handleInputChange}
                    className="input-field"
                    placeholder="Enter amount paid in INR"
                  />
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Step 3: Order Part */}
        {currentStep === 3 && (
          <div className="card">
            <h2 className="text-xl font-semibold text-gray-900 mb-6">Order Part</h2>

            <div className="space-y-6">
              {/* Date of Completion and Country */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Date of Completion of order *
                  </label>
                  <input
                    type="date"
                    name="dateOfCompletionOfOrder"
                    value={formData.dateOfCompletionOfOrder}
                    onChange={handleInputChange}
                    required
                    className="input-field"
                    placeholder="dd-mm-yyyy"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Country to be Implemented in *
                  </label>
                  <input
                    type="text"
                    name="countryToBeImplementedIn"
                    value={formData.countryToBeImplementedIn}
                    onChange={handleInputChange}
                    required
                    className="input-field"
                    placeholder="Enter country to be implemented in"
                  />
                </div>
              </div>

              {/* Work Documents */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Work Documents
                </label>
                <S3FileUpload
                  module="orders"
                  recordId={tempOrderId}
                  fileType="workdocs"
                  allowedTypes="all"
                  multiple={true}
                  maxFiles={10}
                  label="Upload Work Documents"
                  description="Upload work-related documents for this order"
                  onFilesUploaded={(files) => {
                    setUploadedFiles(prev => ({
                      ...prev,
                      workDocuments: files
                    }));
                  }}
                  onFileDeleted={(deletedFile, remainingFiles) => {
                    setUploadedFiles(prev => ({
                      ...prev,
                      workDocuments: remainingFiles
                    }));
                  }}
                  existingFiles={uploadedFiles.workDocuments || []}
                />
              </div>

              {/* Application/Dairy Number and Date of Filing */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Application/Dairy Number
                  </label>
                  <input
                    type="text"
                    name="applicationDairyNumber"
                    value={formData.applicationDairyNumber}
                    onChange={handleInputChange}
                    className="input-field"
                    placeholder="Enter application/dairy number"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Date of Filing at PO
                  </label>
                  <input
                    type="date"
                    name="dateOfFilingAtPO"
                    value={formData.dateOfFilingAtPO}
                    onChange={handleInputChange}
                    className="input-field"
                    placeholder="dd-mm-yyyy"
                  />
                </div>
              </div>

              {/* Lawyer Reference Number */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Lawyer Reference Number
                </label>
                <input
                  type="text"
                  name="lawyerReferenceNumber"
                  value={formData.lawyerReferenceNumber}
                  onChange={handleInputChange}
                  className="input-field"
                  placeholder="Enter lawyer reference number"
                />
              </div>

              {/* Complete Order Summary */}
              <div className="mt-8 p-6 bg-gray-50 rounded-lg">
                <h3 className="text-lg font-medium text-gray-900 mb-4">Complete Order Summary</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 text-sm">
                  {/* Customer Information */}
                  <div className="space-y-2">
                    <h4 className="font-medium text-gray-900">Customer Details</h4>
                    <div>
                      <span className="text-gray-600">Customer:</span>
                      <span className="ml-2 font-medium">{formData.client || 'Not selected'}</span>
                    </div>
                    <div>
                      <span className="text-gray-600">Type of Work:</span>
                      <span className="ml-2">{formData.typeOfWork || 'Not selected'}</span>
                    </div>
                    <div>
                      <span className="text-gray-600">Total Value:</span>
                      <span className="ml-2 font-medium">
                        {formData.totalInvoiceValue ? `₹${parseFloat(formData.totalInvoiceValue).toLocaleString()}` : 'Not specified'}
                      </span>
                    </div>
                  </div>

                  {/* Vendor Information */}
                  <div className="space-y-2">
                    <h4 className="font-medium text-gray-900">Vendor Details</h4>
                    <div>
                      <span className="text-gray-600">Vendor:</span>
                      <span className="ml-2 font-medium">{formData.vendorName || 'Not selected'}</span>
                    </div>
                    <div>
                      <span className="text-gray-600">Status:</span>
                      <span className="ml-2">{formData.currentStatus}</span>
                    </div>
                    <div>
                      <span className="text-gray-600">Amount to Pay:</span>
                      <span className="ml-2">
                        {formData.amountToBePaidToVendor ? `₹${parseFloat(formData.amountToBePaidToVendor).toLocaleString()}` : 'Not specified'}
                      </span>
                    </div>
                  </div>

                  {/* Order Information */}
                  <div className="space-y-2">
                    <h4 className="font-medium text-gray-900">Order Details</h4>
                    <div>
                      <span className="text-gray-600">Reference:</span>
                      <span className="ml-2 font-mono text-xs">{formData.orderReferenceNumber}</span>
                    </div>
                    <div>
                      <span className="text-gray-600">Country:</span>
                      <span className="ml-2">{formData.countryToBeImplementedIn || 'Not specified'}</span>
                    </div>
                    <div>
                      <span className="text-gray-600">Completion Date:</span>
                      <span className="ml-2">{formData.dateOfCompletionOfOrder || 'Not specified'}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Navigation Buttons */}
        <div className="flex justify-between items-center pt-6 border-t border-gray-200">
          <div>
            {currentStep > 1 && (
              <button
                type="button"
                onClick={prevStep}
                className="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                <ChevronLeftIcon className="h-4 w-4 mr-2" />
                Previous
              </button>
            )}
          </div>

          <div className="flex space-x-3">
            <button
              type="button"
              onClick={onCancel}
              className="btn-secondary"
            >
              Cancel
            </button>

            {currentStep < 3 ? (
              <button
                type="button"
                onClick={nextStep}
                disabled={!isStepValid(currentStep)}
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Next
                <ChevronRightIcon className="h-4 w-4 ml-2" />
              </button>
            ) : (
              <button
                type="submit"
                disabled={!isStepValid(currentStep)}
                className="btn-primary disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Create Order
              </button>
            )}
          </div>
        </div>
      </form>
    </div>
  );
};

export default MultiStepOrderForm;
