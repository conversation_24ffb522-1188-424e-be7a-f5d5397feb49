import React, { useState } from 'react';
import { 
  runFullDiagnostic, 
  quickFix, 
  testDatabaseConnection,
  testAreaOfExpertiseOperations 
} from '../../utils/databaseDiagnostics';

const DatabaseDiagnostic = () => {
  const [results, setResults] = useState(null);
  const [loading, setLoading] = useState(false);

  const runDiagnostic = async () => {
    setLoading(true);
    try {
      const diagnosticResults = await runFullDiagnostic();
      setResults(diagnosticResults);
    } catch (error) {
      console.error('Diagnostic failed:', error);
      setResults({ error: error.message });
    } finally {
      setLoading(false);
    }
  };

  const runQuickFix = async () => {
    setLoading(true);
    try {
      await quickFix();
      alert('Quick fix completed! Please refresh the page.');
      // Re-run diagnostic to show updated status
      await runDiagnostic();
    } catch (error) {
      console.error('Quick fix failed:', error);
      alert('Quick fix failed: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  const testConnection = async () => {
    setLoading(true);
    try {
      const result = await testDatabaseConnection();
      alert(result.success ? 'Connection successful!' : 'Connection failed: ' + result.error);
    } catch (error) {
      alert('Connection test failed: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  const setupAreaOfExpertise = async () => {
    setLoading(true);
    try {
      const result = await testAreaOfExpertiseOperations();
      alert(result.success ? 'Area of Expertise setup completed!' : 'Setup failed: ' + result.error);
      // Re-run diagnostic
      await runDiagnostic();
    } catch (error) {
      alert('Setup failed: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="bg-white rounded-lg shadow-md p-6 max-w-4xl mx-auto">
      <h2 className="text-2xl font-bold text-gray-900 mb-6">🔧 Database Diagnostic Tool</h2>
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
        <button
          onClick={testConnection}
          disabled={loading}
          className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-md disabled:opacity-50"
        >
          Test Connection
        </button>
        
        <button
          onClick={setupAreaOfExpertise}
          disabled={loading}
          className="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-md disabled:opacity-50"
        >
          Setup Area of Expertise
        </button>
        
        <button
          onClick={runQuickFix}
          disabled={loading}
          className="bg-orange-500 hover:bg-orange-600 text-white px-4 py-2 rounded-md disabled:opacity-50"
        >
          Quick Fix
        </button>
        
        <button
          onClick={runDiagnostic}
          disabled={loading}
          className="bg-purple-500 hover:bg-purple-600 text-white px-4 py-2 rounded-md disabled:opacity-50"
        >
          Full Diagnostic
        </button>
      </div>

      {loading && (
        <div className="text-center py-4">
          <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
          <p className="mt-2 text-gray-600">Running diagnostic...</p>
        </div>
      )}

      {results && (
        <div className="mt-6">
          <h3 className="text-lg font-semibold mb-4">Diagnostic Results:</h3>
          
          {results.error ? (
            <div className="bg-red-50 border border-red-200 rounded-md p-4">
              <p className="text-red-800">Error: {results.error}</p>
            </div>
          ) : (
            <div className="space-y-4">
              {/* Connection Status */}
              <div className={`p-4 rounded-md ${results.connection?.success ? 'bg-green-50 border-green-200' : 'bg-red-50 border-red-200'} border`}>
                <h4 className="font-medium">Database Connection</h4>
                <p className={results.connection?.success ? 'text-green-800' : 'text-red-800'}>
                  {results.connection?.success ? '✅ Connected' : '❌ Failed: ' + results.connection?.error}
                </p>
                {results.connection?.data && (
                  <p className="text-sm text-gray-600 mt-1">
                    Connected to: {results.connection.data.db_version?.split(' ')[0]}
                  </p>
                )}
              </div>

              {/* Tables Status */}
              <div className={`p-4 rounded-md ${results.tables?.success ? 'bg-green-50 border-green-200' : 'bg-red-50 border-red-200'} border`}>
                <h4 className="font-medium">Tables Status</h4>
                {results.tables?.success ? (
                  <div>
                    <p className="text-green-800">✅ Tables checked</p>
                    <p className="text-sm text-gray-600">
                      Found: {results.tables.tables?.join(', ') || 'None'}
                    </p>
                    <div className="mt-2 space-y-1">
                      <p className={`text-sm ${results.tables.hasVendors ? 'text-green-600' : 'text-red-600'}`}>
                        Vendors table: {results.tables.hasVendors ? '✅' : '❌'}
                      </p>
                      <p className={`text-sm ${results.tables.hasAreaOfExpertise ? 'text-green-600' : 'text-red-600'}`}>
                        Area of Expertise table: {results.tables.hasAreaOfExpertise ? '✅' : '❌'}
                      </p>
                    </div>
                  </div>
                ) : (
                  <p className="text-red-800">❌ Failed: {results.tables?.error}</p>
                )}
              </div>

              {/* Area of Expertise Status */}
              <div className={`p-4 rounded-md ${results.areaOfExpertise?.success ? 'bg-green-50 border-green-200' : 'bg-red-50 border-red-200'} border`}>
                <h4 className="font-medium">Area of Expertise</h4>
                {results.areaOfExpertise?.success ? (
                  <div>
                    <p className="text-green-800">✅ Working</p>
                    <p className="text-sm text-gray-600">
                      Records: {results.areaOfExpertise.recordCount || 0}
                    </p>
                    {results.areaOfExpertise.data && results.areaOfExpertise.data.length > 0 && (
                      <div className="mt-2">
                        <p className="text-sm font-medium">Available areas:</p>
                        <div className="flex flex-wrap gap-1 mt-1">
                          {results.areaOfExpertise.data.slice(0, 5).map((area, index) => (
                            <span key={index} className="inline-block bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded">
                              {area.name}
                            </span>
                          ))}
                          {results.areaOfExpertise.data.length > 5 && (
                            <span className="text-xs text-gray-500">+{results.areaOfExpertise.data.length - 5} more</span>
                          )}
                        </div>
                      </div>
                    )}
                  </div>
                ) : (
                  <p className="text-red-800">❌ Failed: {results.areaOfExpertise?.error}</p>
                )}
              </div>

              {/* Vendors Status */}
              <div className={`p-4 rounded-md ${results.vendors?.success ? 'bg-green-50 border-green-200' : 'bg-red-50 border-red-200'} border`}>
                <h4 className="font-medium">Vendors Table</h4>
                {results.vendors?.success ? (
                  <div>
                    <p className="text-green-800">✅ Working</p>
                    <p className="text-sm text-gray-600">
                      Vendor count: {results.vendors.vendorCount || 0}
                    </p>
                    <p className={`text-sm ${results.vendors.hasPointOfContact ? 'text-green-600' : 'text-red-600'}`}>
                      Point of Contact column: {results.vendors.hasPointOfContact ? '✅' : '❌'}
                    </p>
                  </div>
                ) : (
                  <p className="text-red-800">❌ Failed: {results.vendors?.error}</p>
                )}
              </div>
            </div>
          )}
        </div>
      )}

      <div className="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-md">
        <h4 className="font-medium text-blue-900">Console Commands Available:</h4>
        <div className="mt-2 space-y-1 text-sm text-blue-800 font-mono">
          <p>window.dbDiagnostic() - Full diagnostic</p>
          <p>window.dbQuickFix() - Quick fix common issues</p>
          <p>window.testDbConnection() - Test connection</p>
          <p>window.setupAreaOfExpertise() - Setup area of expertise</p>
        </div>
      </div>
    </div>
  );
};

export default DatabaseDiagnostic;
