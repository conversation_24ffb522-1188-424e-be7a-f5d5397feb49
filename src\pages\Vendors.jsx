import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { PlusIcon, EyeIcon, PencilIcon, TrashIcon, XMarkIcon } from '@heroicons/react/24/outline';
import DataTable from '../components/DataTable/DataTable';
import FileUpload from '../components/FileUpload/FileUpload';
import S3FileUpload from '../components/FileUpload/S3FileUpload';
import { getAllVendors, createVendor, deleteVendor, getVendorStats } from '../services/vendorService';
import { getActiveTypeOfWork } from '../services/typeOfWorkService';
import { getAllStates, getCitiesByStateName, getAllCountries } from '../services/locationService';
import LocationSelector from '../components/LocationSelector/LocationSelector';
import MultiSelect from '../components/MultiSelect/MultiSelect';
import PointOfContact from '../components/PointOfContact/PointOfContact';
import { getActiveCompanyTypes } from '../services/companyTypeService';
import { getActiveServices } from '../services/servicesService';
import { validateEmails, validatePhones, validateCompanyName, getPhonePlaceholder } from '../utils/validation';

// Removed demo data - using only database data

const Vendors = () => {
  const navigate = useNavigate();
  const [showAddForm, setShowAddForm] = useState(false);
  const [formData, setFormData] = useState({
    companyName: '',
    companyType: '',
    onboardingDate: '',
    emails: [''],
    phones: [''],
    address: '',
    country: '',
    state: '',
    city: '',
    username: '',
    gstNumber: '',
    description: '',
    services: [],
    website: '',
    typeOfWork: [],
    status: 'Pending',
    pointOfContact: [],
    startupBenefits: 'No'
  });
  const [uploadedFiles, setUploadedFiles] = useState({
    gstFile: null,
    ndaFile: null,
    agreementFile: null,
    companyLogos: []
  });
  const [tempVendorId] = useState(() => `temp-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`);
  const [availableStates, setAvailableStates] = useState([]);
  const [availableCities, setAvailableCities] = useState([]);
  const [loadingStates, setLoadingStates] = useState(false);
  const [loadingCities, setLoadingCities] = useState(false);
  const [locationData, setLocationData] = useState({});
  const [vendorsList, setVendorsList] = useState([]);
  const [typeOfWorkOptions, setTypeOfWorkOptions] = useState([]);
  const [companyTypeOptions, setCompanyTypeOptions] = useState([]);
  const [availableServices, setAvailableServices] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [validationErrors, setValidationErrors] = useState({});



  // Load vendors from database
  const loadVendors = async () => {
    try {
      setLoading(true);
      console.log('🔄 Loading vendors from database...');
      console.log('🔗 Database connection test starting...');
      const dbVendors = await getAllVendors();
      console.log('✅ Database connection successful!');
      console.log('✅ Vendors loaded:', dbVendors.length, 'records');

      console.log('✅ Vendors loaded:', dbVendors.length, 'records');

      setVendorsList(dbVendors || []);
      setError(null);
    } catch (err) {
      console.error('❌ Database connection failed:', err);
      console.error('❌ Error details:', err.message);
      setError('Failed to load vendors from database: ' + err.message);
      setVendorsList([]);
    } finally {
      setLoading(false);
    }
  };



  // Load type of work options
  const loadTypeOfWork = async () => {
    try {
      const workTypes = await getActiveTypeOfWork();
      setTypeOfWorkOptions(workTypes);
    } catch (err) {
      console.error('Error loading type of work:', err);
    }
  };

  // Load company type options
  const loadCompanyTypes = async () => {
    try {
      const companyTypes = await getActiveCompanyTypes();
      setCompanyTypeOptions(companyTypes);
    } catch (err) {
      console.error('Error loading company types:', err);
    }
  };

  // Load services
  const loadServices = async () => {
    try {
      const services = await getActiveServices();
      setAvailableServices(services);
    } catch (err) {
      console.error('Error loading services:', err);
    }
  };

  // Load states from government API
  const loadStates = async () => {
    try {
      setLoadingStates(true);
      console.log('🔄 Loading states from government API...');
      const states = await getAllStates();
      setAvailableStates(states);
      console.log(`✅ Loaded ${states.length} states`);
    } catch (error) {
      console.error('❌ Error loading states:', error);
    } finally {
      setLoadingStates(false);
    }
  };

  // Load cities for selected state
  const loadCities = async (stateName) => {
    try {
      setLoadingCities(true);
      console.log(`🔄 Loading cities for state ${stateName}...`);
      const cities = await getCitiesByStateName(stateName);
      setAvailableCities(cities);
      console.log(`✅ Loaded ${cities.length} cities`);
    } catch (error) {
      console.error('❌ Error loading cities:', error);
      setAvailableCities([]);
    } finally {
      setLoadingCities(false);
    }
  };

  // Load data on component mount
  useEffect(() => {
    loadVendors();
    loadTypeOfWork();
    loadCompanyTypes();
    loadServices();
    loadStates();
  }, []);

  // Country, State, City data
  const countryStateCity = {
    'India': {
      'Maharashtra': ['Mumbai', 'Pune', 'Nagpur', 'Nashik', 'Aurangabad'],
      'Karnataka': ['Bangalore', 'Mysore', 'Hubli', 'Mangalore', 'Belgaum'],
      'Tamil Nadu': ['Chennai', 'Coimbatore', 'Madurai', 'Tiruchirappalli', 'Salem'],
      'Delhi': ['New Delhi', 'Central Delhi', 'North Delhi', 'South Delhi', 'East Delhi'],
      'Gujarat': ['Ahmedabad', 'Surat', 'Vadodara', 'Rajkot', 'Bhavnagar'],
      'Rajasthan': ['Jaipur', 'Jodhpur', 'Udaipur', 'Kota', 'Bikaner'],
      'West Bengal': ['Kolkata', 'Howrah', 'Durgapur', 'Asansol', 'Siliguri'],
      'Uttar Pradesh': ['Lucknow', 'Kanpur', 'Ghaziabad', 'Agra', 'Varanasi']
    },
    'United States': {
      'California': ['Los Angeles', 'San Francisco', 'San Diego', 'Sacramento', 'San Jose'],
      'New York': ['New York City', 'Buffalo', 'Rochester', 'Syracuse', 'Albany'],
      'Texas': ['Houston', 'Dallas', 'Austin', 'San Antonio', 'Fort Worth'],
      'Florida': ['Miami', 'Orlando', 'Tampa', 'Jacksonville', 'Tallahassee']
    },
    'United Kingdom': {
      'England': ['London', 'Manchester', 'Birmingham', 'Liverpool', 'Leeds'],
      'Scotland': ['Edinburgh', 'Glasgow', 'Aberdeen', 'Dundee', 'Stirling'],
      'Wales': ['Cardiff', 'Swansea', 'Newport', 'Wrexham', 'Barry']
    }
  };




  const columns = [
    {
      key: 'company_name',
      label: 'Vendor Name',
      sortable: true,
      filterable: true
    },
    {
      key: 'emails',
      label: 'Email',
      sortable: true,
      filterable: true,
      render: (value) => value && value.length > 0 ? value[0] : 'N/A'
    },
    {
      key: 'phones',
      label: 'Phone',
      sortable: false,
      filterable: true,
      render: (value) => value && value.length > 0 ? value[0] : 'N/A'
    },
    {
      key: 'username',
      label: 'Contact Person',
      sortable: true,
      filterable: true
    },
    {
      key: 'company_type',
      label: 'Business Type',
      sortable: true,
      filterable: true
    },
    {
      key: 'typeOfWork',
      label: 'Type of Work',
      sortable: true,
      filterable: true,
      render: (value) => {
        if (Array.isArray(value) && value.length > 0) {
          return (
            <div className="flex flex-wrap gap-1">
              {value.slice(0, 2).map((type, index) => (
                <span
                  key={index}
                  className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
                >
                  {type.name || type}
                </span>
              ))}
              {value.length > 2 && (
                <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-600">
                  +{value.length - 2}
                </span>
              )}
            </div>
          );
        } else if (typeof value === 'string' && value && value !== 'N/A') {
          try {
            const parsed = JSON.parse(value);
            if (Array.isArray(parsed)) {
              return (
                <div className="flex flex-wrap gap-1">
                  {parsed.slice(0, 2).map((type, index) => (
                    <span
                      key={index}
                      className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
                    >
                      {type.name || type}
                    </span>
                  ))}
                  {parsed.length > 2 && (
                    <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-600">
                      +{parsed.length - 2}
                    </span>
                  )}
                </div>
              );
            } else {
              return (
                <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                  {value}
                </span>
              );
            }
          } catch {
            return (
              <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                {value}
              </span>
            );
          }
        }
        return <span className="text-gray-500">N/A</span>;
      }
    },
    {
      key: 'status',
      label: 'Status',
      sortable: true,
      filterable: true,
      render: (value) => (
        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
          value === 'Active'
            ? 'bg-green-100 text-green-800'
            : 'bg-yellow-100 text-yellow-800'
        }`}>
          {value}
        </span>
      )
    },
    {
      key: 'onboardingDate',
      label: 'Join Date',
      sortable: true,
      filterable: false
    },
    {
      key: 'totalOrders',
      label: 'Total Orders',
      sortable: true,
      filterable: false
    },
    {
      key: 'actions',
      label: 'Actions',
      sortable: false,
      filterable: false,
      render: (value, row) => (
        <div className="flex space-x-2">
          <button
            onClick={() => navigate(`/vendors/${row.id}`)}
            className="text-blue-600 hover:text-blue-900 p-1 hover:bg-blue-50 rounded"
            title="View Vendor"
          >
            <EyeIcon className="h-4 w-4" />
          </button>
          <button
            onClick={() => navigate(`/vendors/${row.id}/edit`)}
            className="text-green-600 hover:text-green-900 p-1 hover:bg-green-50 rounded"
            title="Edit Vendor"
          >
            <PencilIcon className="h-4 w-4" />
          </button>
          <button
            onClick={() => handleDeleteVendor(row.id)}
            className="text-red-600 hover:text-red-900 p-1 hover:bg-red-50 rounded"
            title="Delete Vendor"
          >
            <TrashIcon className="h-4 w-4" />
          </button>
        </div>
      )
    }
  ];

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));

    // Handle country change
    if (name === 'country') {
      if (value === 'India') {
        // Load states for India from government API
        loadStates();
      } else {
        // For other countries, use fallback data
        setAvailableStates(Object.keys(countryStateCity[value] || {}));
      }
      setAvailableCities([]);
      setFormData(prev => ({
        ...prev,
        state: '',
        city: ''
      }));
    }

    // Handle state change
    if (name === 'state') {
      if (formData.country === 'India') {
        // Load cities from local JSON for Indian states
        loadCities(value);
      } else {
        // Use fallback data for other countries
        setAvailableCities(countryStateCity[formData.country]?.[value] || []);
      }
      setFormData(prev => ({
        ...prev,
        city: ''
      }));
    }
  };

  const handleEmailChange = (index, value) => {
    const newEmails = [...formData.emails];
    newEmails[index] = value;
    setFormData(prev => ({
      ...prev,
      emails: newEmails
    }));
  };

  const addEmail = () => {
    setFormData(prev => ({
      ...prev,
      emails: [...prev.emails, '']
    }));
  };

  const removeEmail = (index) => {
    if (formData.emails.length > 1) {
      setFormData(prev => ({
        ...prev,
        emails: prev.emails.filter((_, i) => i !== index)
      }));
    }
  };

  const handlePhoneChange = (index, value) => {
    const newPhones = [...formData.phones];
    newPhones[index] = value;
    setFormData(prev => ({
      ...prev,
      phones: newPhones
    }));
  };

  const addPhone = () => {
    setFormData(prev => ({
      ...prev,
      phones: [...prev.phones, '']
    }));
  };

  const removePhone = (index) => {
    if (formData.phones.length > 1) {
      setFormData(prev => ({
        ...prev,
        phones: prev.phones.filter((_, i) => i !== index)
      }));
    }
  };

  const handleFileChange = (fileType, files) => {
    console.log('📁 File change for', fileType, ':', files);

    if (fileType === 'companyLogos') {
      // Handle multiple files for company logos
      setUploadedFiles(prev => ({
        ...prev,
        [fileType]: files || []
      }));
    } else {
      // Handle single file for other types
      setUploadedFiles(prev => ({
        ...prev,
        [fileType]: files && files.length > 0 ? files[0] : null
      }));
    }
  };

  // Handle company logo file uploads (multiple files)
  const handleCompanyLogoUpload = (files) => {
    const allowedExtensions = [
      '.pdf', '.jpg', '.jpeg', '.png', '.gif', '.bmp', '.svg', '.webp',
      '.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx', '.txt', '.rtf'
    ];
    const maxFileSize = 10 * 1024 * 1024; // 10MB in bytes

    const validFiles = [];
    const errors = [];

    Array.from(files).forEach(file => {
      // Check file size
      if (file.size > maxFileSize) {
        errors.push(`${file.name}: File size exceeds 10MB limit`);
        return;
      }

      // Check file extension
      const fileExtension = '.' + file.name.split('.').pop().toLowerCase();
      if (!allowedExtensions.includes(fileExtension)) {
        errors.push(`${file.name}: File type not allowed`);
        return;
      }

      // Handle duplicate file names by appending timestamp
      const timestamp = Date.now();
      const nameWithoutExt = file.name.substring(0, file.name.lastIndexOf('.'));
      const extension = file.name.substring(file.name.lastIndexOf('.'));

      // Check if file name already exists
      const existingFile = uploadedFiles.companyLogos.find(
        existingFile => existingFile.originalName === file.name
      );

      const finalFileName = existingFile
        ? `${nameWithoutExt}_${timestamp}${extension}`
        : file.name;

      validFiles.push({
        file: file,
        originalName: file.name,
        displayName: finalFileName,
        size: file.size,
        uploadDate: new Date().toISOString()
      });
    });

    if (errors.length > 0) {
      alert('Some files could not be uploaded:\n' + errors.join('\n'));
    }

    if (validFiles.length > 0) {
      setUploadedFiles(prev => ({
        ...prev,
        companyLogos: [...prev.companyLogos, ...validFiles]
      }));
    }
  };

  // Remove company logo file
  const removeCompanyLogo = (index) => {
    setUploadedFiles(prev => ({
      ...prev,
      companyLogos: prev.companyLogos.filter((_, i) => i !== index)
    }));
  };

  // Download company logo file
  const downloadCompanyLogo = (logoFile) => {
    const url = URL.createObjectURL(logoFile.file);
    const link = document.createElement('a');
    link.href = url;
    link.download = logoFile.displayName;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  };



  // Handle service selection
  const handleServiceChange = (e) => {
    const { value, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      services: checked
        ? [...prev.services, value]
        : prev.services.filter(service => service !== value)
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    // Comprehensive validation
    setValidationErrors({});
    const errors = {};

    // Validate company name
    const companyValidation = validateCompanyName(formData.companyName);
    if (!companyValidation.isValid) {
      errors.companyName = companyValidation.errors;
    }

    // Validate emails
    const emailValidation = validateEmails(formData.emails);
    if (!emailValidation.isValid) {
      errors.emails = emailValidation.errors;
    }

    // Validate phone numbers
    const phoneValidation = validatePhones(formData.phones, 'IN');
    if (!phoneValidation.isValid) {
      errors.phones = phoneValidation.errors;
    }

    // Check if there are any validation errors
    if (Object.keys(errors).length > 0) {
      setValidationErrors(errors);

      // Show first error in alert
      const firstError = Object.values(errors)[0];
      const errorMessage = Array.isArray(firstError) ? firstError[0] : firstError;
      alert(`❌ Validation Error: ${errorMessage}`);
      return;
    }

    try {
      setLoading(true);
      console.log('🚀 Submitting vendor form...');
      console.log('📋 Form data:', formData);
      console.log('📁 Uploaded files:', uploadedFiles);

      // Create new vendor object
      const vendorData = {
        ...formData,
        files: uploadedFiles
      };

      console.log('💾 Sending vendor data to service:', vendorData);

      // Save to database
      const result = await createVendor(vendorData);
      console.log('✅ Vendor created successfully:', result);

      // Reload vendors list
      await loadVendors();

      // Reset form
      setFormData({
        companyName: '',
        companyType: '',
        onboardingDate: '',
        emails: [''],
        phones: [''],
        address: '',
        country: '',
        state: '',
        city: '',
        username: '',
        gstNumber: '',
        description: '',
        services: [],
        website: '',
        typeOfWork: [],
        status: 'Pending',
        pointOfContact: [],
        startupBenefits: 'No'
      });
      setUploadedFiles({
        gstFile: null,
        ndaFile: null,
        agreementFile: null,
        companyLogos: []
      });
      setAvailableStates([]);
      setAvailableCities([]);
      setShowAddForm(false);

      // Show success message
      alert('✅ Vendor added successfully! All files have been saved locally and data stored in database.');
    } catch (err) {
      console.error('❌ Error creating vendor:', err);
      alert(`❌ Failed to create vendor: ${err.message || 'Please try again.'}`);
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteVendor = async (vendorId) => {
    if (window.confirm('Are you sure you want to delete this vendor?')) {
      try {
        setLoading(true);
        await deleteVendor(vendorId);
        await loadVendors();
        alert('Vendor deleted successfully!');
      } catch (err) {
        console.error('Error deleting vendor:', err);
        alert('Failed to delete vendor. Please try again.');
      } finally {
        setLoading(false);
      }
    }
  };

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex justify-between items-center border-b border-gray-200 pb-4">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Vendors</h1>
          <p className="mt-2 text-gray-600">Manage your vendor network</p>
        </div>
        <div className="flex space-x-3">
          <button
            onClick={() => setShowAddForm(true)}
            className="btn-primary flex items-center"
          >
            <PlusIcon className="h-5 w-5 mr-2" />
            Add New Vendor
          </button>
        </div>
      </div>

      {/* Vendors Table */}
      <DataTable
        data={vendorsList}
        columns={columns}
        title="Vendors Management"
        defaultPageSize={20}
        enableExport={true}
        enableColumnToggle={true}
        enableFiltering={true}
        enableSorting={true}
      />

      {/* Add Vendor Modal */}
      {showAddForm && (
        <div className="fixed inset-0 z-50 overflow-y-auto">
          <div className="flex items-center justify-center min-h-screen px-4">
            <div className="fixed inset-0 bg-black opacity-50" onClick={() => setShowAddForm(false)}></div>
            <div className="relative bg-white rounded-lg max-w-6xl w-full max-h-[90vh] overflow-y-auto">
              <form onSubmit={handleSubmit} className="p-6">
                <div className="flex items-center justify-between mb-6">
                  <h3 className="text-lg font-medium text-gray-900">Vendor Onboarding</h3>
                  <button
                    type="button"
                    onClick={() => setShowAddForm(false)}
                    className="text-gray-400 hover:text-gray-600"
                  >
                    <span className="sr-only">Close</span>
                    ×
                  </button>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {/* Basic Information */}
                  <div className="space-y-4">
                    <h4 className="text-md font-medium text-gray-900">Basic Information</h4>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Vendor Onboarding Date *
                      </label>
                      <input
                        type="date"
                        name="onboardingDate"
                        value={formData.onboardingDate}
                        onChange={handleInputChange}
                        required
                        className="input-field"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Company Type *
                      </label>
                      <select
                        name="companyType"
                        value={formData.companyType}
                        onChange={handleInputChange}
                        required
                        className="input-field"
                      >
                        <option value="">Select company type</option>
                        {companyTypeOptions.map((type) => (
                          <option key={type.id} value={type.name}>
                            {type.name}
                          </option>
                        ))}
                      </select>
                      {companyTypeOptions.length === 0 && (
                        <p className="mt-1 text-sm text-gray-500">
                          Loading company types...
                        </p>
                      )}
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        {formData.companyType === 'Individual' ? 'Individual Name' : 'Company Name'} *
                      </label>
                      <input
                        type="text"
                        name="companyName"
                        value={formData.companyName}
                        onChange={handleInputChange}
                        required
                        className={`input-field ${validationErrors.companyName ? 'border-red-500 focus:border-red-500 focus:ring-red-500' : ''}`}
                        placeholder={formData.companyType === 'Individual' ? 'Enter individual name' : 'Enter company name'}
                      />
                      {validationErrors.companyName && (
                        <div className="mt-1">
                          {validationErrors.companyName.map((error, index) => (
                            <p key={index} className="text-sm text-red-600">{error}</p>
                          ))}
                        </div>
                      )}
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Username *
                      </label>
                      <input
                        type="text"
                        name="username"
                        value={formData.username}
                        onChange={handleInputChange}
                        required
                        className="input-field"
                        placeholder="Enter username"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        GST Number
                      </label>
                      <input
                        type="text"
                        name="gstNumber"
                        value={formData.gstNumber}
                        onChange={handleInputChange}
                        className="input-field"
                        placeholder="Enter GST number"
                      />
                    </div>

                    <div>
                      <MultiSelect
                        label="Type of Work"
                        options={typeOfWorkOptions}
                        value={formData.typeOfWork}
                        onChange={(selectedTypes) => {
                          setFormData(prev => ({
                            ...prev,
                            typeOfWork: selectedTypes
                          }));
                        }}
                        placeholder="Select types of work..."
                        required={true}
                        searchable={true}
                        showSelectAll={true}
                        className="w-full"
                      />
                      {typeOfWorkOptions.length === 0 && (
                        <p className="text-sm text-gray-500 italic mt-1">
                          No work types available. Please add work types from the Type of Work page.
                        </p>
                      )}
                    </div>

                    {/* Company Logo Upload - S3 Integration */}
                    <div>
                      <S3FileUpload
                        module="vendors"
                        recordId={tempVendorId}
                        fileType="logo"
                        allowedTypes="all"
                        multiple={true}
                        maxFiles={5}
                        label="Company Logo"
                        description="Upload company logos (Images, PDFs, Documents)"
                        onFilesUploaded={(files) => {
                          setUploadedFiles(prev => ({
                            ...prev,
                            companyLogos: files
                          }));
                        }}
                        onFileDeleted={(deletedFile, remainingFiles) => {
                          setUploadedFiles(prev => ({
                            ...prev,
                            companyLogos: remainingFiles
                          }));
                        }}
                        existingFiles={uploadedFiles.companyLogos || []}
                      />
                    </div>
                  </div>

                  {/* Contact Information */}
                  <div className="space-y-4">
                    <h4 className="text-md font-medium text-gray-900">Contact Information</h4>

                    {/* Multiple Email Addresses */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Company's Email ID *
                      </label>
                      {formData.emails.map((email, index) => (
                        <div key={index} className="flex items-center space-x-2 mb-2">
                          <div className="flex-1">
                            <input
                              type="email"
                              value={email}
                              onChange={(e) => handleEmailChange(index, e.target.value)}
                              required={index === 0}
                              className={`input-field w-full ${
                                validationErrors.emails ? 'border-red-500 focus:border-red-500 focus:ring-red-500' : ''
                              }`}
                              placeholder={index === 0 ? "<EMAIL>" : `Email ${index + 1}`}
                            />
                          </div>
                          {formData.emails.length > 1 && (
                            <button
                              type="button"
                              onClick={() => removeEmail(index)}
                              className="text-red-600 hover:text-red-800 px-2 py-1"
                              title="Remove this email"
                            >
                              Remove
                            </button>
                          )}
                        </div>
                      ))}
                      {validationErrors.emails && (
                        <div className="mt-1">
                          {validationErrors.emails.map((error, index) => (
                            <p key={index} className="text-sm text-red-600">{error}</p>
                          ))}
                        </div>
                      )}
                      <button
                        type="button"
                        onClick={addEmail}
                        className="text-blue-600 hover:text-blue-800 text-sm"
                      >
                        + Add Another Email
                      </button>
                    </div>

                    {/* Multiple Phone Numbers */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Company's Phone No *
                      </label>
                      {formData.phones.map((phone, index) => (
                        <div key={index} className="flex items-center space-x-2 mb-2">
                          <div className="flex-1">
                            <input
                              type="tel"
                              value={phone}
                              onChange={(e) => handlePhoneChange(index, e.target.value)}
                              required={index === 0}
                              className={`input-field w-full ${
                                validationErrors.phones ? 'border-red-500 focus:border-red-500 focus:ring-red-500' : ''
                              }`}
                              placeholder={index === 0 ? getPhonePlaceholder('IN') : `Phone ${index + 1}`}
                            />
                          </div>
                          {formData.phones.length > 1 && (
                            <button
                              type="button"
                              onClick={() => removePhone(index)}
                              className="text-red-600 hover:text-red-800 px-2 py-1"
                              title="Remove this phone number"
                            >
                              Remove
                            </button>
                          )}
                        </div>
                      ))}
                      {validationErrors.phones && (
                        <div className="mt-1">
                          {validationErrors.phones.map((error, index) => (
                            <p key={index} className="text-sm text-red-600">{error}</p>
                          ))}
                        </div>
                      )}
                      <button
                        type="button"
                        onClick={addPhone}
                        className="text-blue-600 hover:text-blue-800 text-sm"
                      >
                        + Add Another Phone
                      </button>
                    </div>
                  </div>
                </div>

                {/* Address Information */}
                <div className="mt-6">
                  <h4 className="text-md font-medium text-gray-900 mb-4">Address Information</h4>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="md:col-span-2">
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Company's Address *
                      </label>
                      <textarea
                        name="address"
                        value={formData.address}
                        onChange={handleInputChange}
                        required
                        rows={3}
                        className="input-field"
                        placeholder="Complete company address"
                      />
                    </div>

                    {/* Global Location Selector */}
                    <LocationSelector
                      value={locationData}
                      onChange={(newLocationData) => {
                        setLocationData(newLocationData);
                        // Update form data for backward compatibility
                        setFormData(prev => ({
                          ...prev,
                          country: newLocationData.countryName || '',
                          state: newLocationData.stateName || '',
                          city: newLocationData.cityName || ''
                        }));
                      }}
                      required={true}
                      defaultCountry="India"
                      className="space-y-4"
                    />

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Description
                      </label>
                      <textarea
                        name="description"
                        value={formData.description}
                        onChange={handleInputChange}
                        rows={3}
                        className="input-field"
                        placeholder="Brief description of vendor services/products"
                      />
                    </div>
                  </div>
                </div>

                {/* Services Offered Section */}
                <div className="mt-6">
                  <h4 className="text-md font-medium text-gray-900 mb-4">Services Offered</h4>
                  <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                    {availableServices.map((service) => (
                      <label key={service.id} className="flex items-center space-x-2">
                        <input
                          type="checkbox"
                          value={service.name}
                          checked={formData.services.includes(service.name)}
                          onChange={handleServiceChange}
                          className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                        />
                        <span className="text-sm text-gray-700">{service.name}</span>
                      </label>
                    ))}
                  </div>
                  {availableServices.length === 0 && (
                    <p className="text-sm text-gray-500 italic">
                      No services available. Please add services from the Services management page.
                    </p>
                  )}
                </div>

                {/* Point of Contact Section */}
                <div className="mt-6">
                  <PointOfContact
                    value={formData.pointOfContact}
                    onChange={(contacts) => {
                      setFormData(prev => ({
                        ...prev,
                        pointOfContact: contacts
                      }));
                    }}
                    required={false}
                    className="w-full"
                  />
                </div>

                {/* Additional Information Section */}
                <div className="mt-6">
                  <h4 className="text-md font-medium text-gray-900 mb-4">Additional Information</h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Startup Benefits *
                      </label>
                      <select
                        name="startupBenefits"
                        value={formData.startupBenefits}
                        onChange={handleInputChange}
                        required
                        className="input-field"
                      >
                        <option value="No">No</option>
                        <option value="Yes">Yes</option>
                      </select>
                      <p className="text-xs text-gray-500 mt-1">
                        Does this vendor qualify for startup benefits?
                      </p>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Status
                      </label>
                      <select
                        name="status"
                        value={formData.status}
                        onChange={handleInputChange}
                        className="input-field"
                      >
                        <option value="Pending">Pending</option>
                        <option value="Active">Active</option>
                        <option value="Inactive">Inactive</option>
                        <option value="Suspended">Suspended</option>
                      </select>
                    </div>
                  </div>
                </div>

                {/* File Upload Section */}
                <div className="mt-6">
                  <h4 className="text-md font-medium text-gray-900 mb-4">Documents & Files</h4>

                  <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                    {/* GST File Upload - S3 Integration */}
                    <div>
                      <S3FileUpload
                        module="vendors"
                        recordId={tempVendorId}
                        fileType="gst"
                        allowedTypes="documents"
                        multiple={false}
                        label="GST File Upload"
                        description="Upload GST registration certificate"
                        onFilesUploaded={(files) => {
                          setUploadedFiles(prev => ({
                            ...prev,
                            gstFile: files[0] || null
                          }));
                        }}
                        onFileDeleted={(deletedFile, remainingFiles) => {
                          setUploadedFiles(prev => ({
                            ...prev,
                            gstFile: null
                          }));
                        }}
                        existingFiles={uploadedFiles.gstFile ? [uploadedFiles.gstFile] : []}
                      />
                    </div>

                    {/* NDA File Upload - S3 Integration */}
                    <div>
                      <S3FileUpload
                        module="vendors"
                        recordId={tempVendorId}
                        fileType="nda"
                        allowedTypes="documents"
                        multiple={false}
                        label="NDA File Upload *"
                        description="Upload signed NDA document (Required)"
                        onFilesUploaded={(files) => {
                          setUploadedFiles(prev => ({
                            ...prev,
                            ndaFile: files[0] || null
                          }));
                        }}
                        onFileDeleted={(deletedFile, remainingFiles) => {
                          setUploadedFiles(prev => ({
                            ...prev,
                            ndaFile: null
                          }));
                        }}
                        existingFiles={uploadedFiles.ndaFile ? [uploadedFiles.ndaFile] : []}
                      />
                    </div>

                    {/* Agreement File Upload - S3 Integration */}
                    <div>
                      <S3FileUpload
                        module="vendors"
                        recordId={tempVendorId}
                        fileType="agreement"
                        allowedTypes="documents"
                        multiple={false}
                        label="Agreement File Upload"
                        description="Upload vendor agreement document"
                        onFilesUploaded={(files) => {
                          setUploadedFiles(prev => ({
                            ...prev,
                            agreementFile: files[0] || null
                          }));
                        }}
                        onFileDeleted={(deletedFile, remainingFiles) => {
                          setUploadedFiles(prev => ({
                            ...prev,
                            agreementFile: null
                          }));
                        }}
                        existingFiles={uploadedFiles.agreementFile ? [uploadedFiles.agreementFile] : []}
                      />
                    </div>
                  </div>
                </div>

                {/* Form Actions */}
                <div className="flex justify-end space-x-3 mt-8 pt-6 border-t border-gray-200">
                  <button
                    type="button"
                    onClick={() => setShowAddForm(false)}
                    className="btn-secondary"
                  >
                    Cancel
                  </button>
                  <button
                    type="submit"
                    className="btn-primary"
                  >
                    Save Vendor
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default Vendors;
