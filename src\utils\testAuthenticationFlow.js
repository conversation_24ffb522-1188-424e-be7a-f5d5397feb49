/**
 * Test Authentication Flow
 * Tests the complete login to dashboard flow
 */

import { login, isAuthenticated, getCurrentUser, logout } from '../services/enhancedAuthService.js';

/**
 * Test the complete authentication flow
 */
export const testAuthenticationFlow = async () => {
  try {
    console.log('🧪 Testing Complete Authentication Flow...');
    console.log('');

    // Step 1: Clear any existing authentication
    console.log('1️⃣ Clearing existing authentication...');
    await logout();
    console.log('✅ Authentication cleared');

    // Step 2: Test authentication check (should be false)
    console.log('');
    console.log('2️⃣ Testing authentication check (should be false)...');
    const isAuthBefore = isAuthenticated();
    const userBefore = getCurrentUser();
    console.log('🔍 Is authenticated before login:', isAuthBefore);
    console.log('🔍 User before login:', userBefore);

    // Step 3: Test admin login
    console.log('');
    console.log('3️⃣ Testing admin login...');
    const loginResult = await login({
      username: 'admin',
      password: 'admin123'
    });

    if (loginResult.success) {
      console.log('✅ Admin login successful');
      console.log('👤 Logged in user:', loginResult.user);
    } else {
      console.error('❌ Admin login failed:', loginResult.error);
      return { success: false, error: 'Admin login failed' };
    }

    // Step 4: Test authentication check (should be true)
    console.log('');
    console.log('4️⃣ Testing authentication check (should be true)...');
    const isAuthAfter = isAuthenticated();
    const userAfter = getCurrentUser();
    console.log('🔍 Is authenticated after login:', isAuthAfter);
    console.log('🔍 User after login:', userAfter);

    // Step 5: Test localStorage data
    console.log('');
    console.log('5️⃣ Testing localStorage data...');
    const authData = localStorage.getItem('innoventory_auth');
    if (authData) {
      const parsed = JSON.parse(authData);
      console.log('💾 Auth data in localStorage:', {
        user: parsed.user,
        hasToken: !!parsed.token,
        expiresAt: new Date(parsed.expiresAt).toLocaleString(),
        isExpired: parsed.expiresAt <= Date.now()
      });
    } else {
      console.log('❌ No auth data found in localStorage');
    }

    // Step 6: Test role checking
    console.log('');
    console.log('6️⃣ Testing role checking...');
    if (userAfter) {
      console.log('👤 User role/userType:', userAfter.role, '/', userAfter.userType);
      console.log('🔐 Should have admin access:', userAfter.userType === 'admin' || userAfter.role === 'admin');
    }

    // Step 7: Simulate ProtectedRoute check
    console.log('');
    console.log('7️⃣ Simulating ProtectedRoute authentication check...');
    
    // This is what ProtectedRoute does
    const protectedRouteAuth = isAuthenticated();
    const protectedRouteUser = getCurrentUser();
    
    console.log('🛡️ ProtectedRoute would see:');
    console.log('   - Is authenticated:', protectedRouteAuth);
    console.log('   - User data:', protectedRouteUser);
    console.log('   - Should allow access:', protectedRouteAuth && protectedRouteUser);

    // Step 8: Test dashboard access simulation
    console.log('');
    console.log('8️⃣ Testing dashboard access simulation...');
    
    if (protectedRouteAuth && protectedRouteUser) {
      console.log('✅ Dashboard access would be GRANTED');
      console.log('🎯 User would be redirected to: /dashboard');
    } else {
      console.log('❌ Dashboard access would be DENIED');
      console.log('🎯 User would be redirected to: /login');
    }

    console.log('');
    console.log('🎉 AUTHENTICATION FLOW TEST COMPLETED!');
    console.log('');
    console.log('📊 Test Results:');
    console.log(`   ✅ Login: ${loginResult.success ? 'PASS' : 'FAIL'}`);
    console.log(`   ✅ Authentication Check: ${isAuthAfter ? 'PASS' : 'FAIL'}`);
    console.log(`   ✅ User Data: ${userAfter ? 'PASS' : 'FAIL'}`);
    console.log(`   ✅ ProtectedRoute Check: ${protectedRouteAuth ? 'PASS' : 'FAIL'}`);
    console.log(`   ✅ Dashboard Access: ${protectedRouteAuth && protectedRouteUser ? 'PASS' : 'FAIL'}`);

    return {
      success: true,
      results: {
        login: loginResult.success,
        authentication: isAuthAfter,
        userData: !!userAfter,
        protectedRoute: protectedRouteAuth,
        dashboardAccess: protectedRouteAuth && protectedRouteUser
      },
      message: 'Authentication flow test completed'
    };

  } catch (error) {
    console.error('💥 Authentication flow test failed:', error);
    console.error('Error details:', error.message);
    
    return {
      success: false,
      error: error.message,
      message: 'Authentication flow test failed'
    };
  }
};

/**
 * Quick login test
 */
export const quickLoginTest = async () => {
  try {
    console.log('⚡ Quick Login Test...');
    
    // Clear auth
    await logout();
    
    // Login
    const result = await login({ username: 'admin', password: 'admin123' });
    
    if (result.success) {
      console.log('✅ Quick login successful!');
      console.log('👤 User:', result.user.username, '- Type:', result.user.userType);
      console.log('🔐 Is authenticated:', isAuthenticated());
      console.log('🎯 Ready for dashboard access');
    } else {
      console.error('❌ Quick login failed:', result.error);
    }
    
    return result;
    
  } catch (error) {
    console.error('💥 Quick login test error:', error);
    return { success: false, error: error.message };
  }
};

/**
 * Debug authentication state
 */
export const debugAuthState = () => {
  try {
    console.log('🔍 DEBUGGING AUTHENTICATION STATE...');
    console.log('');
    
    // Check localStorage
    const authData = localStorage.getItem('innoventory_auth');
    const oldUserData = localStorage.getItem('currentUser');
    const oldToken = localStorage.getItem('authToken');
    
    console.log('💾 localStorage Data:');
    console.log('   - innoventory_auth:', authData ? 'EXISTS' : 'MISSING');
    console.log('   - currentUser (old):', oldUserData ? 'EXISTS' : 'MISSING');
    console.log('   - authToken (old):', oldToken ? 'EXISTS' : 'MISSING');
    
    if (authData) {
      const parsed = JSON.parse(authData);
      console.log('');
      console.log('📋 Enhanced Auth Data:');
      console.log('   - User:', parsed.user);
      console.log('   - Token:', parsed.token ? 'EXISTS' : 'MISSING');
      console.log('   - Expires:', new Date(parsed.expiresAt).toLocaleString());
      console.log('   - Is Expired:', parsed.expiresAt <= Date.now());
    }
    
    // Check authentication functions
    console.log('');
    console.log('🔧 Authentication Functions:');
    console.log('   - isAuthenticated():', isAuthenticated());
    console.log('   - getCurrentUser():', getCurrentUser());
    
    return {
      hasEnhancedAuth: !!authData,
      hasOldAuth: !!(oldUserData && oldToken),
      isAuthenticated: isAuthenticated(),
      currentUser: getCurrentUser()
    };
    
  } catch (error) {
    console.error('💥 Debug auth state error:', error);
    return { error: error.message };
  }
};

// Make functions available globally
if (typeof window !== 'undefined') {
  window.testAuthenticationFlow = testAuthenticationFlow;
  window.quickLoginTest = quickLoginTest;
  window.debugAuthState = debugAuthState;
  
  console.log('🧪 Authentication Flow Test Functions Available:');
  console.log('- window.testAuthenticationFlow() - Complete authentication flow test');
  console.log('- window.quickLoginTest() - Quick login test');
  console.log('- window.debugAuthState() - Debug current authentication state');
}
