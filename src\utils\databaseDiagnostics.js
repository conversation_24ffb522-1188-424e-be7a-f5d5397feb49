/**
 * Database Diagnostics Tool
 * Comprehensive testing and verification of NeonDB connection and data operations
 */

import { sql } from '../config/database.js';

/**
 * Test basic database connection
 */
export const testDatabaseConnection = async () => {
  try {
    console.log('🔄 Testing database connection...');
    const result = await sql`SELECT NOW() as current_time, version() as db_version`;
    console.log('✅ Database connection successful');
    console.log('📊 Database info:', result[0]);
    return { success: true, data: result[0] };
  } catch (error) {
    console.error('❌ Database connection failed:', error);
    return { success: false, error: error.message };
  }
};

/**
 * Check if tables exist
 */
export const checkTablesExist = async () => {
  try {
    console.log('🔄 Checking if tables exist...');
    
    const tables = await sql`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
      AND table_name IN ('vendors', 'area_of_expertise', 'type_of_work')
      ORDER BY table_name
    `;
    
    const tableNames = tables.map(t => t.table_name);
    console.log('📋 Existing tables:', tableNames);
    
    return { 
      success: true, 
      tables: tableNames,
      hasVendors: tableNames.includes('vendors'),
      hasAreaOfExpertise: tableNames.includes('area_of_expertise'),
      hasTypeOfWork: tableNames.includes('type_of_work')
    };
  } catch (error) {
    console.error('❌ Error checking tables:', error);
    return { success: false, error: error.message };
  }
};

/**
 * Check vendors table structure
 */
export const checkVendorsTableStructure = async () => {
  try {
    console.log('🔄 Checking vendors table structure...');
    
    const columns = await sql`
      SELECT column_name, data_type, is_nullable, column_default
      FROM information_schema.columns 
      WHERE table_name = 'vendors' 
      ORDER BY ordinal_position
    `;
    
    console.log('📋 Vendors table columns:', columns);
    
    const hasPointOfContact = columns.some(col => col.column_name === 'point_of_contact');
    
    return { 
      success: true, 
      columns: columns,
      hasPointOfContact: hasPointOfContact
    };
  } catch (error) {
    console.error('❌ Error checking vendors table:', error);
    return { success: false, error: error.message };
  }
};

/**
 * Test area of expertise operations
 */
export const testAreaOfExpertiseOperations = async () => {
  try {
    console.log('🔄 Testing area of expertise operations...');
    
    // Check if table exists
    const tableCheck = await sql`
      SELECT EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_name = 'area_of_expertise'
      ) as table_exists
    `;
    
    if (!tableCheck[0].table_exists) {
      console.log('⚠️ area_of_expertise table does not exist, creating...');
      
      // Create table
      await sql`
        CREATE TABLE area_of_expertise (
          id SERIAL PRIMARY KEY,
          name VARCHAR(255) NOT NULL UNIQUE,
          description TEXT,
          is_active BOOLEAN DEFAULT TRUE,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
      `;
      console.log('✅ area_of_expertise table created');
    }
    
    // Check current data count
    const countResult = await sql`SELECT COUNT(*) as count FROM area_of_expertise`;
    const currentCount = parseInt(countResult[0].count);
    console.log(`📊 Current area of expertise records: ${currentCount}`);
    
    // If no data, insert sample data
    if (currentCount === 0) {
      console.log('🔄 Inserting sample area of expertise data...');
      
      const sampleData = [
        { name: 'Patent Law', description: 'Patent filing, prosecution, and enforcement' },
        { name: 'Trademark Law', description: 'Trademark registration and protection' },
        { name: 'Copyright Law', description: 'Copyright registration and enforcement' },
        { name: 'IP Litigation', description: 'Intellectual property litigation' },
        { name: 'Technical Writing', description: 'Patent drafting and documentation' }
      ];
      
      for (const item of sampleData) {
        await sql`
          INSERT INTO area_of_expertise (name, description, is_active, created_at, updated_at)
          VALUES (${item.name}, ${item.description}, true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
          ON CONFLICT (name) DO NOTHING
        `;
      }
      
      console.log(`✅ Inserted ${sampleData.length} sample records`);
    }
    
    // Test reading data
    const allData = await sql`SELECT * FROM area_of_expertise ORDER BY id`;
    console.log('📋 Area of expertise data:', allData);
    
    return { 
      success: true, 
      tableExists: true,
      recordCount: allData.length,
      data: allData
    };
    
  } catch (error) {
    console.error('❌ Error in area of expertise operations:', error);
    return { success: false, error: error.message };
  }
};

/**
 * Test vendor operations
 */
export const testVendorOperations = async () => {
  try {
    console.log('🔄 Testing vendor operations...');
    
    // Check vendors table
    const vendorCount = await sql`SELECT COUNT(*) as count FROM vendors`;
    console.log(`📊 Current vendor records: ${vendorCount[0].count}`);
    
    // Check if point_of_contact column exists
    const columnCheck = await sql`
      SELECT column_name 
      FROM information_schema.columns 
      WHERE table_name = 'vendors' AND column_name = 'point_of_contact'
    `;
    
    const hasPointOfContact = columnCheck.length > 0;
    console.log(`📋 point_of_contact column exists: ${hasPointOfContact}`);
    
    if (!hasPointOfContact) {
      console.log('🔄 Adding point_of_contact column...');
      await sql`
        ALTER TABLE vendors
        ADD COLUMN point_of_contact JSONB DEFAULT '[]'
      `;
      console.log('✅ point_of_contact column added');
    }

    // Check if startup_benefits column exists
    const startupBenefitsCheck = await sql`
      SELECT column_name
      FROM information_schema.columns
      WHERE table_name = 'vendors' AND column_name = 'startup_benefits'
    `;

    const hasStartupBenefits = startupBenefitsCheck.length > 0;
    console.log(`📋 startup_benefits column exists: ${hasStartupBenefits}`);

    if (!hasStartupBenefits) {
      console.log('🔄 Adding startup_benefits column...');
      await sql`
        ALTER TABLE vendors
        ADD COLUMN startup_benefits VARCHAR(10) DEFAULT 'No'
      `;
      console.log('✅ startup_benefits column added');
    }

    // Test reading vendors
    const vendors = await sql`SELECT id, company_name, point_of_contact, startup_benefits FROM vendors LIMIT 5`;
    console.log('📋 Sample vendor data:', vendors);
    
    return { 
      success: true, 
      vendorCount: parseInt(vendorCount[0].count),
      hasPointOfContact: true,
      sampleData: vendors
    };
    
  } catch (error) {
    console.error('❌ Error in vendor operations:', error);
    return { success: false, error: error.message };
  }
};

/**
 * Comprehensive database diagnostic
 */
export const runFullDiagnostic = async () => {
  console.log('🚀 Starting comprehensive database diagnostic...');
  
  const results = {
    connection: await testDatabaseConnection(),
    tables: await checkTablesExist(),
    vendorsStructure: await checkVendorsTableStructure(),
    areaOfExpertise: await testAreaOfExpertiseOperations(),
    vendors: await testVendorOperations()
  };
  
  console.log('📊 Diagnostic Results Summary:');
  console.log('- Connection:', results.connection.success ? '✅' : '❌');
  console.log('- Tables exist:', results.tables.success ? '✅' : '❌');
  console.log('- Vendors structure:', results.vendorsStructure.success ? '✅' : '❌');
  console.log('- Area of expertise:', results.areaOfExpertise.success ? '✅' : '❌');
  console.log('- Vendor operations:', results.vendors.success ? '✅' : '❌');
  
  return results;
};

/**
 * Quick fix for common issues
 */
export const quickFix = async () => {
  try {
    console.log('🔧 Running quick fix for common database issues...');
    
    // Run area of expertise setup
    await testAreaOfExpertiseOperations();
    
    // Run vendor setup
    await testVendorOperations();
    
    console.log('✅ Quick fix completed');
    return { success: true };
    
  } catch (error) {
    console.error('❌ Quick fix failed:', error);
    return { success: false, error: error.message };
  }
};

// Make functions available globally for debugging
if (typeof window !== 'undefined' && process.env.NODE_ENV === 'development') {
  window.dbDiagnostic = runFullDiagnostic;
  window.dbQuickFix = quickFix;
  window.testDbConnection = testDatabaseConnection;
  window.checkTables = checkTablesExist;
  console.log('🔧 Database diagnostic functions available:');
  console.log('- window.dbDiagnostic() - Full diagnostic');
  console.log('- window.dbQuickFix() - Quick fix common issues');
  console.log('- window.testDbConnection() - Test connection');
  console.log('- window.checkTables() - Check table existence');
}
