import postgres from 'postgres';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Get database URL from environment
const databaseUrl = process.env.VITE_DATABASE_URL || process.env.DATABASE_URL;

if (!databaseUrl) {
  console.error('❌ Database URL not found. Please set VITE_DATABASE_URL or DATABASE_URL in your .env file.');
  process.exit(1);
}

// Create database connection
const sql = postgres(databaseUrl, {
  ssl: databaseUrl.includes('neon.tech') ? { rejectUnauthorized: false } : false
});

async function fixDatabaseSchema() {
  try {
    console.log('🔄 Fixing database schema issues...');

    // 1. Ensure orders table has correct columns
    console.log('📋 Checking orders table...');
    
    // Check if orders table exists
    const ordersTableExists = await sql`
      SELECT EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_name = 'orders'
      )
    `;

    if (!ordersTableExists[0].exists) {
      console.log('📝 Creating orders table...');
      await sql`
        CREATE TABLE orders (
          id SERIAL PRIMARY KEY,
          reference_number VARCHAR(100) UNIQUE NOT NULL,
          client_id INTEGER REFERENCES clients(id),
          vendor_id INTEGER REFERENCES vendors(id),
          description TEXT,
          amount DECIMAL(15,2),
          status VARCHAR(50) DEFAULT 'Pending',
          priority VARCHAR(50) DEFAULT 'Medium',
          files JSONB DEFAULT '{}',
          status_history JSONB DEFAULT '[]',
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
      `;
      console.log('✅ Orders table created');
    } else {
      console.log('✅ Orders table already exists');
      
      // Check if status column exists
      const statusColumnExists = await sql`
        SELECT EXISTS (
          SELECT FROM information_schema.columns 
          WHERE table_name = 'orders' AND column_name = 'status'
        )
      `;

      if (!statusColumnExists[0].exists) {
        console.log('📝 Adding status column to orders table...');
        await sql`ALTER TABLE orders ADD COLUMN status VARCHAR(50) DEFAULT 'Pending'`;
        console.log('✅ Status column added');
      }

      // Check if amount column exists
      const amountColumnExists = await sql`
        SELECT EXISTS (
          SELECT FROM information_schema.columns 
          WHERE table_name = 'orders' AND column_name = 'amount'
        )
      `;

      if (!amountColumnExists[0].exists) {
        console.log('📝 Adding amount column to orders table...');
        await sql`ALTER TABLE orders ADD COLUMN amount DECIMAL(15,2)`;
        console.log('✅ Amount column added');
      }
    }

    // 2. Ensure sub_admins table exists
    console.log('📋 Checking sub_admins table...');
    
    const subAdminsTableExists = await sql`
      SELECT EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_name = 'sub_admins'
      )
    `;

    if (!subAdminsTableExists[0].exists) {
      console.log('📝 Creating sub_admins table...');
      await sql`
        CREATE TABLE sub_admins (
          id SERIAL PRIMARY KEY,
          name VARCHAR(255) NOT NULL,
          email VARCHAR(255) UNIQUE NOT NULL,
          onboarding_date DATE,
          address TEXT,
          country VARCHAR(100),
          state VARCHAR(100),
          city VARCHAR(100),
          username VARCHAR(255),
          pan_number VARCHAR(50),
          term_of_work VARCHAR(100),
          files JSONB DEFAULT '{}',
          status VARCHAR(50) DEFAULT 'Active',
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
      `;
      console.log('✅ Sub_admins table created');
    } else {
      console.log('✅ Sub_admins table already exists');
    }

    // 3. Insert some sample data for testing
    console.log('📋 Checking for sample data...');
    
    const orderCount = await sql`SELECT COUNT(*) as count FROM orders`;
    if (orderCount[0].count === 0) {
      console.log('📝 Adding sample orders for testing...');
      
      // First, check if we have clients and vendors
      const clientCount = await sql`SELECT COUNT(*) as count FROM clients`;
      const vendorCount = await sql`SELECT COUNT(*) as count FROM vendors`;
      
      if (clientCount[0].count > 0 && vendorCount[0].count > 0) {
        // Get first client and vendor IDs
        const firstClient = await sql`SELECT id FROM clients LIMIT 1`;
        const firstVendor = await sql`SELECT id FROM vendors LIMIT 1`;
        
        await sql`
          INSERT INTO orders (reference_number, client_id, vendor_id, description, amount, status)
          VALUES 
            ('ORD-2024-001', ${firstClient[0].id}, ${firstVendor[0].id}, 'Sample Order 1', 1500.00, 'Pending'),
            ('ORD-2024-002', ${firstClient[0].id}, ${firstVendor[0].id}, 'Sample Order 2', 2500.00, 'In Progress'),
            ('ORD-2024-003', ${firstClient[0].id}, ${firstVendor[0].id}, 'Sample Order 3', 3500.00, 'Completed')
        `;
        console.log('✅ Sample orders added');
      } else {
        console.log('⚠️ No clients or vendors found, skipping sample orders');
      }
    }

    // 4. Verify the fixes
    console.log('🔍 Verifying fixes...');
    
    try {
      const statusDistribution = await sql`
        SELECT status, COUNT(*) as count 
        FROM orders 
        GROUP BY status 
        ORDER BY count DESC
      `;
      console.log('✅ Status query working:', statusDistribution);
    } catch (error) {
      console.error('❌ Status query still failing:', error.message);
    }

    try {
      const revenueData = await sql`
        SELECT SUM(CAST(amount AS DECIMAL)) as total_revenue 
        FROM orders 
        WHERE status = 'Completed'
      `;
      console.log('✅ Revenue query working:', revenueData);
    } catch (error) {
      console.error('❌ Revenue query still failing:', error.message);
    }

    try {
      const userDistribution = await sql`SELECT COUNT(*) as count FROM sub_admins`;
      console.log('✅ Sub-admins query working:', userDistribution);
    } catch (error) {
      console.error('❌ Sub-admins query still failing:', error.message);
    }

    console.log('🎉 Database schema fixes completed successfully!');

  } catch (error) {
    console.error('❌ Error fixing database schema:', error);
    process.exit(1);
  } finally {
    // Close database connection
    await sql.end();
  }
}

// Run the fix
fixDatabaseSchema();
