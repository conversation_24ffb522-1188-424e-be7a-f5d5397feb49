import { defineConfig, loadEnv } from 'vite'
import react from '@vitejs/plugin-react'
import uploadPlugin from './vite-upload-plugin.js'

// https://vite.dev/config/
export default defineConfig(({ mode }) => {
  // Load env file based on `mode` in the current working directory.
  const env = loadEnv(mode, process.cwd(), '')

  return {
    plugins: [react(), uploadPlugin()],
    define: {
      // Make env variables available to the client
      __DATABASE_URL__: JSON.stringify(env.VITE_DATABASE_URL),
    },
    // Expose env variables that start with VITE_
    envPrefix: 'VITE_',
    // Exclude server-side dependencies from browser bundle
    optimizeDeps: {
      exclude: ['@prisma/client', '@aws-sdk/client-s3', '@aws-sdk/s3-request-presigner', 'multer']
    },
    // Configure external dependencies for server-side only
    build: {
      rollupOptions: {
        external: ['@prisma/client', '@aws-sdk/client-s3', '@aws-sdk/s3-request-presigner', 'multer']
      }
    }
  }
})
