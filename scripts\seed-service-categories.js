import postgres from 'postgres';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Get database URL from environment
const databaseUrl = process.env.VITE_DATABASE_URL || process.env.DATABASE_URL;

if (!databaseUrl) {
  console.error('❌ Database URL not found. Please set VITE_DATABASE_URL or DATABASE_URL in your .env file.');
  process.exit(1);
}

// Create database connection
const sql = postgres(databaseUrl, {
  ssl: databaseUrl.includes('neon.tech') ? { rejectUnauthorized: false } : false
});

// Default service categories data
const defaultCategories = [
  {
    name: 'General',
    description: 'General services and miscellaneous offerings',
    color: '#6B7280'
  },
  {
    name: 'Office Supplies',
    description: 'Stationery, paper, office equipment and supplies',
    color: '#3B82F6'
  },
  {
    name: 'Technology',
    description: 'IT services, software, hardware and technical solutions',
    color: '#8B5CF6'
  },
  {
    name: 'Maintenance',
    description: 'Building maintenance, repairs and facility management',
    color: '#F59E0B'
  },
  {
    name: 'Professional Services',
    description: 'Consulting, legal, financial and business services',
    color: '#10B981'
  },
  {
    name: 'Logistics',
    description: 'Transportation, delivery and supply chain services',
    color: '#EF4444'
  },
  {
    name: 'Marketing',
    description: 'Advertising, digital marketing and promotional services',
    color: '#EC4899'
  },
  {
    name: 'Security',
    description: 'Security services, surveillance and access control',
    color: '#84CC16'
  },
  {
    name: 'Food & Catering',
    description: 'Food services, catering and beverage solutions',
    color: '#F97316'
  },
  {
    name: 'Training & Education',
    description: 'Training programs, education and skill development',
    color: '#06B6D4'
  }
];

async function seedServiceCategories() {
  try {
    console.log('🌱 Starting service categories seeding...');

    // Check if categories table exists and has data
    const existingCategories = await sql`
      SELECT COUNT(*) as count FROM service_categories
    `;

    if (existingCategories[0].count > 0) {
      console.log(`📊 Found ${existingCategories[0].count} existing service categories in database`);
      console.log('⚠️  Skipping seeding to avoid duplicates. Delete existing categories first if you want to reseed.');
      return;
    }

    console.log('📝 Creating default service categories...');

    // Insert default categories
    for (const category of defaultCategories) {
      try {
        const result = await sql`
          INSERT INTO service_categories (name, description, color, "isActive", status, created_at, updated_at)
          VALUES (
            ${category.name},
            ${category.description},
            ${category.color},
            true,
            'Active',
            CURRENT_TIMESTAMP,
            CURRENT_TIMESTAMP
          )
          RETURNING id, name
        `;
        
        console.log(`✅ Created: ${result[0].name}`);
      } catch (error) {
        if (error.message.includes('duplicate key')) {
          console.log(`⚠️  Skipped: ${category.name} (already exists)`);
        } else {
          console.error(`❌ Error creating ${category.name}:`, error.message);
        }
      }
    }

    // Get final count
    const finalCount = await sql`
      SELECT COUNT(*) as count FROM service_categories WHERE "isActive" = true
    `;

    console.log(`🎉 Successfully created ${defaultCategories.length} service categories!`);
    console.log(`📊 Total active service categories: ${finalCount[0].count}`);
    console.log('✅ Service categories seeding completed successfully!');

  } catch (error) {
    console.error('❌ Error seeding service categories:', error);
    process.exit(1);
  } finally {
    // Close database connection
    await sql.end();
  }
}

// Run the seeding
seedServiceCategories();
