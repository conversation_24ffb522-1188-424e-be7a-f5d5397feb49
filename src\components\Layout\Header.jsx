import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Bars3Icon, UserCircleIcon, ArrowRightOnRectangleIcon, ChevronDownIcon } from '@heroicons/react/24/outline';
import { getCurrentUser, logoutUser } from '../../services/authService';

const Header = ({ onMenuClick }) => {
  const navigate = useNavigate();
  const [currentUser, setCurrentUser] = useState(null);
  const [showUserMenu, setShowUserMenu] = useState(false);

  useEffect(() => {
    const user = getCurrentUser();
    setCurrentUser(user);
  }, []);

  const handleLogout = () => {
    logoutUser();
    navigate('/login');
  };
  return (
    <header className="bg-white/80 backdrop-blur-md shadow-sm border-b border-gray-200/50 sticky top-0 z-40">
      <div className="flex items-center justify-between px-4 sm:px-6 py-4">
        {/* Left side - Mobile menu button */}
        <div className="flex items-center">
          <button
            onClick={onMenuClick}
            className="lg:hidden p-2 rounded-xl text-gray-400 hover:text-gray-600 hover:bg-gray-100 transition-all duration-200 hover:scale-105"
          >
            <Bars3Icon className="h-6 w-6" />
          </button>

          {/* Page title */}
          <div className="ml-4 lg:ml-0">
            <h1 className="text-xl sm:text-2xl font-bold bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent">
              Admin Panel
            </h1>
            <p className="text-xs text-gray-500 font-medium hidden sm:block">Innoventory Management System</p>
          </div>
        </div>

        {/* Right side - User menu */}
        <div className="flex items-center space-x-2 sm:space-x-4">
          {/* User menu */}
          <div className="relative">
            <div className="flex items-center space-x-3 group">
              <div className="text-right hidden sm:block">
                <p className="text-sm font-semibold text-gray-900 group-hover:text-blue-700 transition-colors duration-200">
                  {currentUser?.name || 'Admin User'}
                </p>
                <p className="text-xs text-gray-500">{currentUser?.email || '<EMAIL>'}</p>
                <p className="text-xs text-blue-600 font-medium">{currentUser?.role || 'ADMIN'}</p>
              </div>
              <button
                onClick={() => setShowUserMenu(!showUserMenu)}
                className="relative p-1 text-gray-400 hover:text-gray-600 transition-all duration-200 hover:scale-105 flex items-center space-x-1"
              >
                <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-blue-600 rounded-full flex items-center justify-center shadow-lg">
                  <span className="text-white font-semibold text-sm">
                    {currentUser?.name ? currentUser.name.charAt(0).toUpperCase() : 'A'}
                  </span>
                </div>
                <ChevronDownIcon className="h-4 w-4 text-gray-400" />
                {/* Online indicator */}
                <div className="absolute -bottom-0 -right-6 w-3 h-3 bg-green-500 border-2 border-white rounded-full"></div>
              </button>
            </div>

            {/* User dropdown menu */}
            {showUserMenu && (
              <div className="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg z-50 border border-gray-200">
                <div className="py-1">
                  <div className="px-4 py-2 border-b border-gray-100">
                    <p className="text-sm font-medium text-gray-900">{currentUser?.name || 'Admin User'}</p>
                    <p className="text-xs text-gray-500">{currentUser?.email || '<EMAIL>'}</p>
                    <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800 mt-1">
                      {currentUser?.role || 'ADMIN'}
                    </span>
                  </div>
                  <button
                    onClick={() => {
                      setShowUserMenu(false);
                      navigate('/settings');
                    }}
                    className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                  >
                    <UserCircleIcon className="h-4 w-4 inline mr-2" />
                    Profile Settings
                  </button>
                  <button
                    onClick={() => {
                      setShowUserMenu(false);
                      handleLogout();
                    }}
                    className="block w-full text-left px-4 py-2 text-sm text-red-700 hover:bg-red-50"
                  >
                    <ArrowRightOnRectangleIcon className="h-4 w-4 inline mr-2" />
                    Sign Out
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </header>
  );
};

export default Header;
