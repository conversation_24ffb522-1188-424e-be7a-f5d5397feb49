// Upload Configuration
// Centralized configuration for file uploads

// Environment detection (handle both Vite and Node.js environments)
export const isDevelopment = typeof import.meta !== 'undefined' && import.meta.env ? import.meta.env.DEV : process.env.NODE_ENV !== 'production';
export const isProduction = typeof import.meta !== 'undefined' && import.meta.env ? import.meta.env.PROD : process.env.NODE_ENV === 'production';

// S3 Configuration (handle both Vite and Node.js environments)
const getEnvVar = (viteKey, nodeKey, defaultValue) => {
  if (typeof import.meta !== 'undefined' && import.meta.env) {
    return import.meta.env[viteKey] || defaultValue;
  }
  return process.env[nodeKey] || process.env[viteKey] || defaultValue;
};

export const S3_CONFIG = {
  bucket: getEnvVar('VITE_AWS_S3_BUCKET_NAME', 'AWS_S3_BUCKET_NAME', 'innoventory3solutions'),
  region: getEnvVar('VITE_AWS_REGION', 'AWS_REGION', 'us-east-1'),
  baseUrl: getEnvVar('VITE_AWS_S3_BASE_URL', 'AWS_S3_BASE_URL', 'https://innoventory3solutions.s3.us-east-1.amazonaws.com')
};

// Upload Configuration
export const UPLOAD_CONFIG = {
  maxFileSize: 10 * 1024 * 1024, // 10MB
  allowedTypes: {
    images: ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'],
    documents: [
      'application/pdf', 
      'application/msword', 
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'application/vnd.ms-excel',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    ],
    all: [
      'image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp',
      'application/pdf', 
      'application/msword', 
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'application/vnd.ms-excel',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    ]
  }
};

// API Endpoints (handle both browser and Node.js environments)
export const API_ENDPOINTS = {
  upload: isDevelopment
    ? (typeof window !== 'undefined' ? `${window.location.origin}/api/upload` : 'http://localhost:5173/api/upload')
    : '/api/upload',
  signedUrl: isDevelopment
    ? (typeof window !== 'undefined' ? `${window.location.origin}/api/signed-url` : 'http://localhost:5173/api/signed-url')
    : '/api/signed-url'
};

// Validation Functions
export const validateFile = (file, allowedTypes = 'all') => {
  const errors = [];
  
  // Check file size
  if (file.size > UPLOAD_CONFIG.maxFileSize) {
    errors.push(`File size (${formatFileSize(file.size)}) exceeds maximum allowed size (${formatFileSize(UPLOAD_CONFIG.maxFileSize)})`);
  }
  
  // Check file type
  const validTypes = UPLOAD_CONFIG.allowedTypes[allowedTypes] || UPLOAD_CONFIG.allowedTypes.all;
  if (!validTypes.includes(file.type)) {
    errors.push(`File type ${file.type} is not allowed. Allowed types: ${validTypes.join(', ')}`);
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
};

// Utility Functions
export const formatFileSize = (bytes) => {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

export const generateFileKey = (module, recordId, fileType, originalName) => {
  const timestamp = Date.now();
  const uuid = Math.random().toString(36).substring(2, 8);
  const extension = originalName.split('.').pop();
  const sanitizedName = originalName.replace(/[^a-zA-Z0-9.-]/g, '_');
  
  return `${module}/${recordId}/${fileType}-${timestamp}-${uuid}-${sanitizedName}`;
};

export const getPublicFileUrl = (key) => {
  return `${S3_CONFIG.baseUrl}/${key}`;
};

// Debug Information
export const getDebugInfo = () => {
  return {
    environment: isDevelopment ? 'development' : 'production',
    s3Config: S3_CONFIG,
    uploadConfig: UPLOAD_CONFIG,
    apiEndpoints: API_ENDPOINTS,
    timestamp: new Date().toISOString()
  };
};

export default {
  S3_CONFIG,
  UPLOAD_CONFIG,
  API_ENDPOINTS,
  validateFile,
  formatFileSize,
  generateFileKey,
  getPublicFileUrl,
  getDebugInfo,
  isDevelopment,
  isProduction
};
