/**
 * Test Location Service Functions
 * Quick test to verify location service is working
 */

import { getAllCountries, getStatesByCountry, getCitiesByState } from '../services/locationService.js';

/**
 * Test location service functionality
 */
export const testLocationService = async () => {
  try {
    console.log('🧪 Testing Location Service...');
    console.log('');

    // Test 1: Get all countries
    console.log('1️⃣ Testing getAllCountries...');
    const countries = await getAllCountries();
    console.log(`✅ Countries loaded: ${countries.length}`);
    console.log('📋 Sample countries:', countries.slice(0, 5).map(c => `${c.name} (ID: ${c.id})`));
    console.log('');

    // Test 2: Get states for a specific country (USA = 233, India = 101)
    console.log('2️⃣ Testing getStatesByCountry (USA)...');
    const usaStates = await getStatesByCountry(233); // USA
    console.log(`✅ USA States loaded: ${usaStates.length}`);
    console.log('📋 Sample USA states:', usaStates.slice(0, 5).map(s => `${s.name} (ID: ${s.id})`));
    console.log('');

    console.log('3️⃣ Testing getStatesByCountry (India)...');
    const indiaStates = await getStatesByCountry(101); // India
    console.log(`✅ India States loaded: ${indiaStates.length}`);
    console.log('📋 Sample India states:', indiaStates.slice(0, 5).map(s => `${s.name} (ID: ${s.id})`));
    console.log('');

    // Test 3: Get cities for a specific state
    if (usaStates.length > 0) {
      const californiaState = usaStates.find(s => s.name.toLowerCase().includes('california'));
      if (californiaState) {
        console.log('4️⃣ Testing getCitiesByState (California)...');
        const californiaCities = await getCitiesByState(californiaState.id);
        console.log(`✅ California Cities loaded: ${californiaCities.length}`);
        console.log('📋 Sample California cities:', californiaCities.slice(0, 5).map(c => c.name));
        console.log('');
      }
    }

    if (indiaStates.length > 0) {
      const maharashtraState = indiaStates.find(s => s.name.toLowerCase().includes('maharashtra'));
      if (maharashtraState) {
        console.log('5️⃣ Testing getCitiesByState (Maharashtra)...');
        const maharashtraCities = await getCitiesByState(maharashtraState.id);
        console.log(`✅ Maharashtra Cities loaded: ${maharashtraCities.length}`);
        console.log('📋 Sample Maharashtra cities:', maharashtraCities.slice(0, 5).map(c => c.name));
        console.log('');
      }
    }

    console.log('🎉 Location Service Test Completed Successfully!');
    console.log('');
    console.log('✅ All functions working correctly');
    console.log('✅ Data is loading properly');
    console.log('✅ Ready for use in Sub-Admin form');
    console.log('');

    return {
      success: true,
      countriesCount: countries.length,
      usaStatesCount: usaStates.length,
      indiaStatesCount: indiaStates.length,
      message: 'Location service working correctly'
    };

  } catch (error) {
    console.error('💥 Location Service Test Failed:', error);
    console.error('Error details:', error.message);
    console.error('');
    console.error('🔧 Possible issues:');
    console.error('   - Location data files missing');
    console.error('   - Network connectivity issues');
    console.error('   - API endpoints not accessible');
    console.error('');

    return {
      success: false,
      error: error.message,
      message: 'Location service test failed'
    };
  }
};

/**
 * Test specific country lookup
 */
export const testCountryLookup = async (countryName) => {
  try {
    console.log(`🔍 Testing country lookup for: ${countryName}`);
    
    const countries = await getAllCountries();
    const country = countries.find(c => c.name.toLowerCase().includes(countryName.toLowerCase()));
    
    if (country) {
      console.log(`✅ Found country: ${country.name} (ID: ${country.id})`);
      
      // Get states for this country
      const states = await getStatesByCountry(country.id);
      console.log(`📋 States in ${country.name}: ${states.length}`);
      
      if (states.length > 0) {
        console.log('📋 Sample states:', states.slice(0, 3).map(s => s.name));
      }
      
      return { success: true, country, statesCount: states.length };
    } else {
      console.log(`❌ Country not found: ${countryName}`);
      return { success: false, message: 'Country not found' };
    }
    
  } catch (error) {
    console.error('❌ Country lookup failed:', error);
    return { success: false, error: error.message };
  }
};

// Make functions available globally for testing
if (typeof window !== 'undefined') {
  window.testLocationService = testLocationService;
  window.testCountryLookup = testCountryLookup;
  
  console.log('🧪 Location Service Test Functions Available:');
  console.log('- window.testLocationService() - Test all location functions');
  console.log('- window.testCountryLookup("USA") - Test specific country lookup');
}
