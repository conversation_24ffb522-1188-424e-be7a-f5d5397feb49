/**
 * Fix Export Button Issues
 * Comprehensive diagnosis and fix for PDF export problems
 */

/**
 * Step 1: Check if we're on the right page
 */
export const checkCurrentPage = () => {
  const path = window.location.pathname;
  console.log(`📍 Current page: ${path}`);
  
  if (!path.includes('type-of-work')) {
    console.log('⚠️ Please navigate to the Type of Work page first');
    return false;
  }
  
  console.log('✅ On Type of Work page');
  return true;
};

/**
 * Step 2: Find and analyze the export button
 */
export const findExportButton = () => {
  console.log('🔍 Looking for export button...');
  
  // Look for buttons with "Export" text
  const allButtons = document.querySelectorAll('button');
  let exportButton = null;
  
  allButtons.forEach((button, index) => {
    console.log(`Button ${index + 1}: "${button.textContent.trim()}" - Classes: ${button.className}`);
    
    if (button.textContent.includes('Export')) {
      exportButton = button;
      console.log(`✅ Found export button: "${button.textContent}"`);
    }
  });
  
  if (!exportButton) {
    console.log('❌ Export button not found');
    console.log('💡 This might mean:');
    console.log('   1. enableExport prop is not set to true');
    console.log('   2. DataTable component is not rendering properly');
    console.log('   3. Button is hidden or not visible');
    return null;
  }
  
  return exportButton;
};

/**
 * Step 3: Test button click functionality
 */
export const testButtonClick = (button) => {
  if (!button) {
    console.log('❌ No button provided to test');
    return false;
  }
  
  console.log('🖱️ Testing button click...');
  
  // Check if button is disabled
  if (button.disabled) {
    console.log('❌ Button is disabled');
    return false;
  }
  
  // Check if button has click handler
  const hasOnClick = button.onclick !== null;
  const hasEventListener = button.getAttribute('onclick') !== null;
  
  console.log(`🔧 Has onClick: ${hasOnClick}`);
  console.log(`🔧 Has event listener: ${hasEventListener}`);
  
  // Try clicking the button
  try {
    console.log('🖱️ Clicking export button...');
    button.click();
    
    // Wait a moment and check for modal
    setTimeout(() => {
      const modals = document.querySelectorAll('[class*="fixed"], [class*="modal"], [class*="z-50"]');
      console.log(`🔧 Modals found after click: ${modals.length}`);
      
      // Look for export modal specifically
      const exportModalText = Array.from(document.querySelectorAll('*')).find(el => 
        el.textContent && el.textContent.includes('Export Data')
      );
      
      if (exportModalText) {
        console.log('✅ Export modal opened successfully!');
        return true;
      } else {
        console.log('❌ Export modal did not open');
        console.log('💡 Possible issues:');
        console.log('   1. Modal state is not being set');
        console.log('   2. ExportModal component has errors');
        console.log('   3. Modal is being rendered but not visible');
        return false;
      }
    }, 500);
    
  } catch (error) {
    console.error('❌ Error clicking button:', error);
    return false;
  }
};

/**
 * Step 4: Check React component state
 */
export const checkReactState = () => {
  console.log('🔍 Checking React component state...');
  
  // Look for React DevTools
  if (window.__REACT_DEVTOOLS_GLOBAL_HOOK__) {
    console.log('✅ React DevTools available');
  } else {
    console.log('⚠️ React DevTools not available');
  }
  
  // Check for React Fiber nodes
  const reactElements = document.querySelectorAll('[data-reactroot], [data-react-*]');
  console.log(`🔧 React elements found: ${reactElements.length}`);
  
  // Look for DataTable component indicators
  const tables = document.querySelectorAll('table');
  console.log(`📊 Tables found: ${tables.length}`);
  
  if (tables.length > 0) {
    console.log('✅ DataTable appears to be rendered');
    
    // Check table structure
    const headers = document.querySelectorAll('th');
    const rows = document.querySelectorAll('tbody tr');
    
    console.log(`📋 Table headers: ${headers.length}`);
    console.log(`📄 Table rows: ${rows.length}`);
    
    // List headers
    headers.forEach((header, index) => {
      console.log(`   Header ${index + 1}: "${header.textContent}"`);
    });
    
    return true;
  } else {
    console.log('❌ No tables found - DataTable may not be rendering');
    return false;
  }
};

/**
 * Step 5: Test PDF libraries directly
 */
export const testPDFLibraries = async () => {
  console.log('🧪 Testing PDF libraries...');
  
  try {
    // Test jsPDF import
    const jsPDF = await import('jspdf');
    console.log('✅ jsPDF imported successfully');
    
    // Test jsPDF AutoTable import
    await import('jspdf-autotable');
    console.log('✅ jsPDF AutoTable imported successfully');
    
    // Create a simple test PDF
    const { default: jsPDFClass } = jsPDF;
    const doc = new jsPDFClass();
    
    doc.text('Test PDF Export', 10, 10);
    
    // Test autoTable
    doc.autoTable({
      head: [['Name', 'Status']],
      body: [['Test Item', 'Active']],
      startY: 20
    });
    
    // Save test PDF
    doc.save('test_export_fix.pdf');
    console.log('✅ Test PDF created and downloaded!');
    
    return true;
    
  } catch (error) {
    console.error('❌ PDF library test failed:', error);
    console.log('💡 This indicates a library import issue');
    return false;
  }
};

/**
 * Step 6: Force create export modal
 */
export const forceCreateExportModal = () => {
  console.log('🔧 Force creating export modal...');
  
  try {
    // Create modal HTML directly
    const modalHTML = `
      <div class="fixed inset-0 z-50 overflow-y-auto" style="background-color: rgba(0, 0, 0, 0.5);">
        <div class="flex items-center justify-center min-h-screen px-4">
          <div class="bg-white rounded-lg p-6 max-w-md w-full">
            <h3 class="text-lg font-medium mb-4">Export Data - Test Modal</h3>
            <p class="text-sm text-gray-600 mb-4">This is a test modal to verify modal functionality works.</p>
            <div class="flex space-x-3">
              <button onclick="this.closest('.fixed').remove()" class="px-4 py-2 bg-gray-300 rounded">Close</button>
              <button onclick="window.testPDFLibraries()" class="px-4 py-2 bg-blue-600 text-white rounded">Test PDF</button>
            </div>
          </div>
        </div>
      </div>
    `;
    
    // Insert modal into DOM
    document.body.insertAdjacentHTML('beforeend', modalHTML);
    console.log('✅ Test modal created successfully!');
    
    return true;
    
  } catch (error) {
    console.error('❌ Failed to create test modal:', error);
    return false;
  }
};

/**
 * Main diagnostic function
 */
export const diagnoseExportButton = async () => {
  console.log('🚀 DIAGNOSING EXPORT BUTTON ISSUES...');
  console.log('');
  
  // Step 1: Check current page
  const onCorrectPage = checkCurrentPage();
  if (!onCorrectPage) return;
  
  console.log('');
  
  // Step 2: Find export button
  const exportButton = findExportButton();
  
  console.log('');
  
  // Step 3: Check React state
  const reactStateOK = checkReactState();
  
  console.log('');
  
  // Step 4: Test button click if button exists
  if (exportButton) {
    testButtonClick(exportButton);
  }
  
  console.log('');
  
  // Step 5: Test PDF libraries
  const pdfLibrariesOK = await testPDFLibraries();
  
  console.log('');
  
  // Step 6: Create test modal
  forceCreateExportModal();
  
  console.log('');
  console.log('📊 DIAGNOSIS SUMMARY:');
  console.log(`✅ On correct page: ${onCorrectPage}`);
  console.log(`${exportButton ? '✅' : '❌'} Export button found: ${!!exportButton}`);
  console.log(`${reactStateOK ? '✅' : '❌'} React components OK: ${reactStateOK}`);
  console.log(`${pdfLibrariesOK ? '✅' : '❌'} PDF libraries OK: ${pdfLibrariesOK}`);
  
  console.log('');
  console.log('💡 NEXT STEPS:');
  
  if (!exportButton) {
    console.log('1. Check if enableExport={true} is set in TypeOfWork.jsx');
    console.log('2. Verify DataTable component is receiving correct props');
  }
  
  if (exportButton && !pdfLibrariesOK) {
    console.log('1. Check if jsPDF and jspdf-autotable are installed');
    console.log('2. Verify imports in ExportModal.jsx');
  }
  
  if (exportButton && pdfLibrariesOK) {
    console.log('1. Check browser console for React errors');
    console.log('2. Verify ExportModal component is not throwing errors');
  }
  
  console.log('');
  console.log('🎉 DIAGNOSIS COMPLETE!');
  
  return {
    onCorrectPage,
    exportButtonFound: !!exportButton,
    reactStateOK,
    pdfLibrariesOK
  };
};

// Make functions available globally
if (typeof window !== 'undefined') {
  window.checkCurrentPage = checkCurrentPage;
  window.findExportButton = findExportButton;
  window.testButtonClick = testButtonClick;
  window.checkReactState = checkReactState;
  window.testPDFLibraries = testPDFLibraries;
  window.forceCreateExportModal = forceCreateExportModal;
  window.diagnoseExportButton = diagnoseExportButton;
  
  console.log('🔧 Export Button Fix Functions Available:');
  console.log('- window.diagnoseExportButton() - Run complete diagnosis');
  console.log('- window.testPDFLibraries() - Test PDF generation directly');
  console.log('- window.forceCreateExportModal() - Create test modal');
  console.log('- window.findExportButton() - Find export button');
}
