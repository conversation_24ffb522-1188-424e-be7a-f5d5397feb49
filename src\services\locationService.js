/**
 * Global Location Service
 * Handles countries, states, and cities data from comprehensive global datasets
 */

// Cache for location data
let countriesData = null;
let statesData = null;
let citiesData = null;
let countriesCache = null;
let statesCache = {};
let citiesCache = {};

/**
 * Load countries data
 */
const loadCountriesData = async () => {
  try {
    if (countriesData) {
      return countriesData;
    }

    console.log('🌍 Loading countries data...');

    // Try to load from local comprehensive dataset first
    try {
      const response = await fetch('/global-locations.json');
      if (response.ok) {
        const data = await response.json();
        countriesData = data.countries;
        console.log(`✅ Loaded ${countriesData.length} countries from local dataset`);
        return countriesData;
      }
    } catch (error) {
      console.warn('⚠️ Could not load local countries data, trying external source');
    }

    // Try to load from external comprehensive dataset
    try {
      const response = await fetch('https://raw.githubusercontent.com/dr5hn/countries-states-cities-database/master/json/countries.json');
      if (response.ok) {
        countriesData = await response.json();
        console.log(`✅ Loaded ${countriesData.length} countries from external dataset`);
        return countriesData;
      }
    } catch (error) {
      console.warn('⚠️ Could not load external countries data, using fallback');
    }

    // Fallback to basic countries data
    countriesData = [
      { id: 1, name: "Afghanistan", iso2: "AF", iso3: "AFG", phonecode: "93" },
      { id: 2, name: "Albania", iso2: "AL", iso3: "ALB", phonecode: "355" },
      { id: 3, name: "Algeria", iso2: "DZ", iso3: "DZA", phonecode: "213" },
      { id: 4, name: "American Samoa", iso2: "AS", iso3: "ASM", phonecode: "1684" },
      { id: 5, name: "Andorra", iso2: "AD", iso3: "AND", phonecode: "376" },
      { id: 6, name: "Angola", iso2: "AO", iso3: "AGO", phonecode: "244" },
      { id: 7, name: "Anguilla", iso2: "AI", iso3: "AIA", phonecode: "1264" },
      { id: 8, name: "Antarctica", iso2: "AQ", iso3: "ATA", phonecode: "0" },
      { id: 9, name: "Antigua And Barbuda", iso2: "AG", iso3: "ATG", phonecode: "1268" },
      { id: 10, name: "Argentina", iso2: "AR", iso3: "ARG", phonecode: "54" },
      { id: 11, name: "Armenia", iso2: "AM", iso3: "ARM", phonecode: "374" },
      { id: 12, name: "Aruba", iso2: "AW", iso3: "ABW", phonecode: "297" },
      { id: 13, name: "Australia", iso2: "AU", iso3: "AUS", phonecode: "61" },
      { id: 14, name: "Austria", iso2: "AT", iso3: "AUT", phonecode: "43" },
      { id: 15, name: "Azerbaijan", iso2: "AZ", iso3: "AZE", phonecode: "994" },
      { id: 16, name: "Bahamas The", iso2: "BS", iso3: "BHS", phonecode: "1242" },
      { id: 17, name: "Bahrain", iso2: "BH", iso3: "BHR", phonecode: "973" },
      { id: 18, name: "Bangladesh", iso2: "BD", iso3: "BGD", phonecode: "880" },
      { id: 19, name: "Barbados", iso2: "BB", iso3: "BRB", phonecode: "1246" },
      { id: 20, name: "Belarus", iso2: "BY", iso3: "BLR", phonecode: "375" },
      { id: 21, name: "Belgium", iso2: "BE", iso3: "BEL", phonecode: "32" },
      { id: 22, name: "Belize", iso2: "BZ", iso3: "BLZ", phonecode: "501" },
      { id: 23, name: "Benin", iso2: "BJ", iso3: "BEN", phonecode: "229" },
      { id: 24, name: "Bermuda", iso2: "BM", iso3: "BMU", phonecode: "1441" },
      { id: 25, name: "Bhutan", iso2: "BT", iso3: "BTN", phonecode: "975" },
      { id: 26, name: "Bolivia", iso2: "BO", iso3: "BOL", phonecode: "591" },
      { id: 27, name: "Bosnia and Herzegovina", iso2: "BA", iso3: "BIH", phonecode: "387" },
      { id: 28, name: "Botswana", iso2: "BW", iso3: "BWA", phonecode: "267" },
      { id: 29, name: "Bouvet Island", iso2: "BV", iso3: "BVT", phonecode: "0" },
      { id: 30, name: "Brazil", iso2: "BR", iso3: "BRA", phonecode: "55" },
      { id: 31, name: "British Indian Ocean Territory", iso2: "IO", iso3: "IOT", phonecode: "246" },
      { id: 32, name: "Brunei", iso2: "BN", iso3: "BRN", phonecode: "673" },
      { id: 33, name: "Bulgaria", iso2: "BG", iso3: "BGR", phonecode: "359" },
      { id: 34, name: "Burkina Faso", iso2: "BF", iso3: "BFA", phonecode: "226" },
      { id: 35, name: "Burundi", iso2: "BI", iso3: "BDI", phonecode: "257" },
      { id: 36, name: "Cambodia", iso2: "KH", iso3: "KHM", phonecode: "855" },
      { id: 37, name: "Cameroon", iso2: "CM", iso3: "CMR", phonecode: "237" },
      { id: 38, name: "Canada", iso2: "CA", iso3: "CAN", phonecode: "1" },
      { id: 39, name: "Cape Verde", iso2: "CV", iso3: "CPV", phonecode: "238" },
      { id: 40, name: "Cayman Islands", iso2: "KY", iso3: "CYM", phonecode: "1345" },
      { id: 101, name: "India", iso2: "IN", iso3: "IND", phonecode: "91" },
      { id: 231, name: "United States", iso2: "US", iso3: "USA", phonecode: "1" },
      { id: 232, name: "United Kingdom", iso2: "GB", iso3: "GBR", phonecode: "44" },
      { id: 233, name: "China", iso2: "CN", iso3: "CHN", phonecode: "86" },
      { id: 234, name: "Japan", iso2: "JP", iso3: "JPN", phonecode: "81" },
      { id: 235, name: "Germany", iso2: "DE", iso3: "DEU", phonecode: "49" },
      { id: 236, name: "France", iso2: "FR", iso3: "FRA", phonecode: "33" },
      { id: 237, name: "Italy", iso2: "IT", iso3: "ITA", phonecode: "39" },
      { id: 238, name: "Spain", iso2: "ES", iso3: "ESP", phonecode: "34" },
      { id: 239, name: "Russia", iso2: "RU", iso3: "RUS", phonecode: "7" },
      { id: 240, name: "South Korea", iso2: "KR", iso3: "KOR", phonecode: "82" }
    ];

    console.log(`✅ Loaded ${countriesData.length} countries from fallback data`);
    return countriesData;
  } catch (error) {
    console.error('❌ Error loading countries data:', error);
    throw error;
  }
};

/**
 * Load states data
 */
const loadStatesData = async () => {
  try {
    if (statesData) {
      return statesData;
    }

    console.log('🏛️ Loading states data...');

    // Try to load from comprehensive dataset, fallback to basic data
    try {
      const response = await fetch('https://raw.githubusercontent.com/dr5hn/countries-states-cities-database/master/json/states.json');
      if (response.ok) {
        statesData = await response.json();
        console.log(`✅ Loaded ${statesData.length} states from global dataset`);
        return statesData;
      }
    } catch (error) {
      console.warn('⚠️ Could not load global states data, using fallback');
    }

    // Fallback to basic states data (focusing on major countries)
    statesData = [
      // India
      { id: 1, name: "Andhra Pradesh", country_id: 101, country_code: "IN", state_code: "AP" },
      { id: 2, name: "Arunachal Pradesh", country_id: 101, country_code: "IN", state_code: "AR" },
      { id: 3, name: "Assam", country_id: 101, country_code: "IN", state_code: "AS" },
      { id: 4, name: "Bihar", country_id: 101, country_code: "IN", state_code: "BR" },
      { id: 5, name: "Chhattisgarh", country_id: 101, country_code: "IN", state_code: "CT" },
      { id: 6, name: "Goa", country_id: 101, country_code: "IN", state_code: "GA" },
      { id: 7, name: "Gujarat", country_id: 101, country_code: "IN", state_code: "GJ" },
      { id: 8, name: "Haryana", country_id: 101, country_code: "IN", state_code: "HR" },
      { id: 9, name: "Himachal Pradesh", country_id: 101, country_code: "IN", state_code: "HP" },
      { id: 10, name: "Jharkhand", country_id: 101, country_code: "IN", state_code: "JH" },
      { id: 11, name: "Karnataka", country_id: 101, country_code: "IN", state_code: "KA" },
      { id: 12, name: "Kerala", country_id: 101, country_code: "IN", state_code: "KL" },
      { id: 13, name: "Madhya Pradesh", country_id: 101, country_code: "IN", state_code: "MP" },
      { id: 14, name: "Maharashtra", country_id: 101, country_code: "IN", state_code: "MH" },
      { id: 15, name: "Manipur", country_id: 101, country_code: "IN", state_code: "MN" },
      { id: 16, name: "Meghalaya", country_id: 101, country_code: "IN", state_code: "ML" },
      { id: 17, name: "Mizoram", country_id: 101, country_code: "IN", state_code: "MZ" },
      { id: 18, name: "Nagaland", country_id: 101, country_code: "IN", state_code: "NL" },
      { id: 19, name: "Odisha", country_id: 101, country_code: "IN", state_code: "OR" },
      { id: 20, name: "Punjab", country_id: 101, country_code: "IN", state_code: "PB" },
      { id: 21, name: "Rajasthan", country_id: 101, country_code: "IN", state_code: "RJ" },
      { id: 22, name: "Sikkim", country_id: 101, country_code: "IN", state_code: "SK" },
      { id: 23, name: "Tamil Nadu", country_id: 101, country_code: "IN", state_code: "TN" },
      { id: 24, name: "Telangana", country_id: 101, country_code: "IN", state_code: "TG" },
      { id: 25, name: "Tripura", country_id: 101, country_code: "IN", state_code: "TR" },
      { id: 26, name: "Uttar Pradesh", country_id: 101, country_code: "IN", state_code: "UP" },
      { id: 27, name: "Uttarakhand", country_id: 101, country_code: "IN", state_code: "UT" },
      { id: 28, name: "West Bengal", country_id: 101, country_code: "IN", state_code: "WB" },
      { id: 29, name: "Delhi", country_id: 101, country_code: "IN", state_code: "DL" },

      // United States
      { id: 1001, name: "Alabama", country_id: 231, country_code: "US", state_code: "AL" },
      { id: 1002, name: "Alaska", country_id: 231, country_code: "US", state_code: "AK" },
      { id: 1003, name: "Arizona", country_id: 231, country_code: "US", state_code: "AZ" },
      { id: 1004, name: "Arkansas", country_id: 231, country_code: "US", state_code: "AR" },
      { id: 1005, name: "California", country_id: 231, country_code: "US", state_code: "CA" },
      { id: 1006, name: "Colorado", country_id: 231, country_code: "US", state_code: "CO" },
      { id: 1007, name: "Connecticut", country_id: 231, country_code: "US", state_code: "CT" },
      { id: 1008, name: "Delaware", country_id: 231, country_code: "US", state_code: "DE" },
      { id: 1009, name: "Florida", country_id: 231, country_code: "US", state_code: "FL" },
      { id: 1010, name: "Georgia", country_id: 231, country_code: "US", state_code: "GA" },
      { id: 1011, name: "Hawaii", country_id: 231, country_code: "US", state_code: "HI" },
      { id: 1012, name: "Idaho", country_id: 231, country_code: "US", state_code: "ID" },
      { id: 1013, name: "Illinois", country_id: 231, country_code: "US", state_code: "IL" },
      { id: 1014, name: "Indiana", country_id: 231, country_code: "US", state_code: "IN" },
      { id: 1015, name: "Iowa", country_id: 231, country_code: "US", state_code: "IA" },
      { id: 1016, name: "Kansas", country_id: 231, country_code: "US", state_code: "KS" },
      { id: 1017, name: "Kentucky", country_id: 231, country_code: "US", state_code: "KY" },
      { id: 1018, name: "Louisiana", country_id: 231, country_code: "US", state_code: "LA" },
      { id: 1019, name: "Maine", country_id: 231, country_code: "US", state_code: "ME" },
      { id: 1020, name: "Maryland", country_id: 231, country_code: "US", state_code: "MD" },
      { id: 1021, name: "Massachusetts", country_id: 231, country_code: "US", state_code: "MA" },
      { id: 1022, name: "Michigan", country_id: 231, country_code: "US", state_code: "MI" },
      { id: 1023, name: "Minnesota", country_id: 231, country_code: "US", state_code: "MN" },
      { id: 1024, name: "Mississippi", country_id: 231, country_code: "US", state_code: "MS" },
      { id: 1025, name: "Missouri", country_id: 231, country_code: "US", state_code: "MO" },
      { id: 1026, name: "Montana", country_id: 231, country_code: "US", state_code: "MT" },
      { id: 1027, name: "Nebraska", country_id: 231, country_code: "US", state_code: "NE" },
      { id: 1028, name: "Nevada", country_id: 231, country_code: "US", state_code: "NV" },
      { id: 1029, name: "New Hampshire", country_id: 231, country_code: "US", state_code: "NH" },
      { id: 1030, name: "New Jersey", country_id: 231, country_code: "US", state_code: "NJ" },
      { id: 1031, name: "New Mexico", country_id: 231, country_code: "US", state_code: "NM" },
      { id: 1032, name: "New York", country_id: 231, country_code: "US", state_code: "NY" },
      { id: 1033, name: "North Carolina", country_id: 231, country_code: "US", state_code: "NC" },
      { id: 1034, name: "North Dakota", country_id: 231, country_code: "US", state_code: "ND" },
      { id: 1035, name: "Ohio", country_id: 231, country_code: "US", state_code: "OH" },
      { id: 1036, name: "Oklahoma", country_id: 231, country_code: "US", state_code: "OK" },
      { id: 1037, name: "Oregon", country_id: 231, country_code: "US", state_code: "OR" },
      { id: 1038, name: "Pennsylvania", country_id: 231, country_code: "US", state_code: "PA" },
      { id: 1039, name: "Rhode Island", country_id: 231, country_code: "US", state_code: "RI" },
      { id: 1040, name: "South Carolina", country_id: 231, country_code: "US", state_code: "SC" },
      { id: 1041, name: "South Dakota", country_id: 231, country_code: "US", state_code: "SD" },
      { id: 1042, name: "Tennessee", country_id: 231, country_code: "US", state_code: "TN" },
      { id: 1043, name: "Texas", country_id: 231, country_code: "US", state_code: "TX" },
      { id: 1044, name: "Utah", country_id: 231, country_code: "US", state_code: "UT" },
      { id: 1045, name: "Vermont", country_id: 231, country_code: "US", state_code: "VT" },
      { id: 1046, name: "Virginia", country_id: 231, country_code: "US", state_code: "VA" },
      { id: 1047, name: "Washington", country_id: 231, country_code: "US", state_code: "WA" },
      { id: 1048, name: "West Virginia", country_id: 231, country_code: "US", state_code: "WV" },
      { id: 1049, name: "Wisconsin", country_id: 231, country_code: "US", state_code: "WI" },
      { id: 1050, name: "Wyoming", country_id: 231, country_code: "US", state_code: "WY" }
    ];

    console.log(`✅ Loaded ${statesData.length} states from fallback data`);
    return statesData;
  } catch (error) {
    console.error('❌ Error loading states data:', error);
    throw error;
  }
};

/**
 * Load cities data
 */
const loadCitiesData = async () => {
  try {
    if (citiesData) {
      return citiesData;
    }

    console.log('🏙️ Loading cities data...');

    // Try to load from comprehensive dataset, fallback to basic data
    try {
      const response = await fetch('https://raw.githubusercontent.com/dr5hn/countries-states-cities-database/master/json/cities.json');
      if (response.ok) {
        citiesData = await response.json();
        console.log(`✅ Loaded ${citiesData.length} cities from global dataset`);
        return citiesData;
      }
    } catch (error) {
      console.warn('⚠️ Could not load global cities data, using fallback');
    }

    // Fallback to basic cities data (major cities)
    citiesData = [
      // India - Major cities
      { id: 1, name: "Mumbai", state_id: 14, state_name: "Maharashtra", country_id: 101, country_code: "IN" },
      { id: 2, name: "Delhi", state_id: 29, state_name: "Delhi", country_id: 101, country_code: "IN" },
      { id: 3, name: "Bangalore", state_id: 11, state_name: "Karnataka", country_id: 101, country_code: "IN" },
      { id: 4, name: "Hyderabad", state_id: 24, state_name: "Telangana", country_id: 101, country_code: "IN" },
      { id: 5, name: "Chennai", state_id: 23, state_name: "Tamil Nadu", country_id: 101, country_code: "IN" },
      { id: 6, name: "Kolkata", state_id: 28, state_name: "West Bengal", country_id: 101, country_code: "IN" },
      { id: 7, name: "Pune", state_id: 14, state_name: "Maharashtra", country_id: 101, country_code: "IN" },
      { id: 8, name: "Ahmedabad", state_id: 7, state_name: "Gujarat", country_id: 101, country_code: "IN" },
      { id: 9, name: "Jaipur", state_id: 21, state_name: "Rajasthan", country_id: 101, country_code: "IN" },
      { id: 10, name: "Surat", state_id: 7, state_name: "Gujarat", country_id: 101, country_code: "IN" },
      { id: 11, name: "Lucknow", state_id: 26, state_name: "Uttar Pradesh", country_id: 101, country_code: "IN" },
      { id: 12, name: "Kanpur", state_id: 26, state_name: "Uttar Pradesh", country_id: 101, country_code: "IN" },
      { id: 13, name: "Nagpur", state_id: 14, state_name: "Maharashtra", country_id: 101, country_code: "IN" },
      { id: 14, name: "Indore", state_id: 13, state_name: "Madhya Pradesh", country_id: 101, country_code: "IN" },
      { id: 15, name: "Thane", state_id: 14, state_name: "Maharashtra", country_id: 101, country_code: "IN" },
      { id: 16, name: "Bhopal", state_id: 13, state_name: "Madhya Pradesh", country_id: 101, country_code: "IN" },
      { id: 17, name: "Visakhapatnam", state_id: 1, state_name: "Andhra Pradesh", country_id: 101, country_code: "IN" },
      { id: 18, name: "Pimpri-Chinchwad", state_id: 14, state_name: "Maharashtra", country_id: 101, country_code: "IN" },
      { id: 19, name: "Patna", state_id: 4, state_name: "Bihar", country_id: 101, country_code: "IN" },
      { id: 20, name: "Vadodara", state_id: 7, state_name: "Gujarat", country_id: 101, country_code: "IN" },

      // United States - Major cities
      { id: 1001, name: "New York", state_id: 1032, state_name: "New York", country_id: 231, country_code: "US" },
      { id: 1002, name: "Los Angeles", state_id: 1005, state_name: "California", country_id: 231, country_code: "US" },
      { id: 1003, name: "Chicago", state_id: 1013, state_name: "Illinois", country_id: 231, country_code: "US" },
      { id: 1004, name: "Houston", state_id: 1043, state_name: "Texas", country_id: 231, country_code: "US" },
      { id: 1005, name: "Phoenix", state_id: 1003, state_name: "Arizona", country_id: 231, country_code: "US" },
      { id: 1006, name: "Philadelphia", state_id: 1038, state_name: "Pennsylvania", country_id: 231, country_code: "US" },
      { id: 1007, name: "San Antonio", state_id: 1043, state_name: "Texas", country_id: 231, country_code: "US" },
      { id: 1008, name: "San Diego", state_id: 1005, state_name: "California", country_id: 231, country_code: "US" },
      { id: 1009, name: "Dallas", state_id: 1043, state_name: "Texas", country_id: 231, country_code: "US" },
      { id: 1010, name: "San Jose", state_id: 1005, state_name: "California", country_id: 231, country_code: "US" },
      { id: 1011, name: "Austin", state_id: 1043, state_name: "Texas", country_id: 231, country_code: "US" },
      { id: 1012, name: "Jacksonville", state_id: 1009, state_name: "Florida", country_id: 231, country_code: "US" },
      { id: 1013, name: "Fort Worth", state_id: 1043, state_name: "Texas", country_id: 231, country_code: "US" },
      { id: 1014, name: "Columbus", state_id: 1035, state_name: "Ohio", country_id: 231, country_code: "US" },
      { id: 1015, name: "San Francisco", state_id: 1005, state_name: "California", country_id: 231, country_code: "US" },
      { id: 1016, name: "Charlotte", state_id: 1033, state_name: "North Carolina", country_id: 231, country_code: "US" },
      { id: 1017, name: "Indianapolis", state_id: 1014, state_name: "Indiana", country_id: 231, country_code: "US" },
      { id: 1018, name: "Seattle", state_id: 1047, state_name: "Washington", country_id: 231, country_code: "US" },
      { id: 1019, name: "Denver", state_id: 1006, state_name: "Colorado", country_id: 231, country_code: "US" },
      { id: 1020, name: "Washington", state_id: 1020, state_name: "Maryland", country_id: 231, country_code: "US" }
    ];

    console.log(`✅ Loaded ${citiesData.length} cities from fallback data`);
    return citiesData;
  } catch (error) {
    console.error('❌ Error loading cities data:', error);
    throw error;
  }
};

// ===== COUNTRIES API =====

/**
 * Get all countries
 */
export const getAllCountries = async () => {
  try {
    if (countriesCache) {
      console.log('🌍 Returning cached countries data');
      return countriesCache;
    }

    const data = await loadCountriesData();

    // Transform and sort countries
    const countries = data.map(country => ({
      id: country.id,
      name: country.name,
      iso2: country.iso2,
      iso3: country.iso3,
      phonecode: country.phonecode,
      capital: country.capital,
      currency: country.currency,
      flag: country.emoji
    }));

    countries.sort((a, b) => a.name.localeCompare(b.name));

    countriesCache = countries;
    console.log(`✅ Processed ${countries.length} countries successfully`);
    return countries;

  } catch (error) {
    console.error('❌ Error fetching countries:', error);
    return [];
  }
};

/**
 * Get country by ID
 */
export const getCountryById = async (countryId) => {
  try {
    const countries = await getAllCountries();
    return countries.find(country => country.id == countryId);
  } catch (error) {
    console.error(`❌ Error getting country ${countryId}:`, error);
    return null;
  }
};

/**
 * Search countries by name
 */
export const searchCountries = async (searchTerm) => {
  try {
    const countries = await getAllCountries();
    return countries.filter(country =>
      country.name.toLowerCase().includes(searchTerm.toLowerCase())
    );
  } catch (error) {
    console.error('❌ Error searching countries:', error);
    return [];
  }
};

// ===== STATES API =====

/**
 * Get all states for a specific country
 */
export const getStatesByCountry = async (countryId) => {
  try {
    const cacheKey = `country_${countryId}`;
    if (statesCache[cacheKey]) {
      console.log(`🏛️ Returning cached states for country ${countryId}`);
      return statesCache[cacheKey];
    }

    const data = await loadStatesData();

    // Filter states by country
    const countryStates = data.filter(state => state.country_id == countryId);

    // Transform states
    const states = countryStates.map(state => ({
      id: state.id,
      name: state.name,
      code: state.state_code,
      country_id: state.country_id,
      country_code: state.country_code
    }));

    states.sort((a, b) => a.name.localeCompare(b.name));

    statesCache[cacheKey] = states;
    console.log(`✅ Processed ${states.length} states for country ${countryId}`);
    return states;

  } catch (error) {
    console.error(`❌ Error fetching states for country ${countryId}:`, error);
    return [];
  }
};

/**
 * Get all states (backward compatibility)
 */
export const getAllStates = async () => {
  try {
    // Default to India (country_id: 101) for backward compatibility
    return await getStatesByCountry(101);
  } catch (error) {
    console.error('❌ Error fetching states:', error);
    return [];
  }
};

// ===== CITIES API =====

/**
 * Get cities for a specific state by state ID
 */
export const getCitiesByState = async (stateId) => {
  try {
    const cacheKey = `state_${stateId}`;
    if (citiesCache[cacheKey]) {
      console.log(`🏙️ Returning cached cities for state ${stateId}`);
      return citiesCache[cacheKey];
    }

    console.log(`🔄 Loading cities for state ${stateId}...`);

    const data = await loadCitiesData();

    // Filter cities by state
    const stateCities = data.filter(city => city.state_id == stateId);

    // Transform cities
    const cities = stateCities.map(city => ({
      id: city.id,
      name: city.name,
      state_id: city.state_id,
      state_name: city.state_name,
      country_id: city.country_id,
      country_code: city.country_code
    }));

    cities.sort((a, b) => a.name.localeCompare(b.name));

    citiesCache[cacheKey] = cities;
    console.log(`✅ Loaded ${cities.length} cities for state ${stateId}`);
    return cities;

  } catch (error) {
    console.error(`❌ Error loading cities for state ${stateId}:`, error);
    return [];
  }
};

/**
 * Get cities for a specific state by state name (backward compatibility)
 */
export const getCitiesByStateName = async (stateName) => {
  try {
    console.log(`🔄 Loading cities for state ${stateName}...`);

    const data = await loadCitiesData();

    // Filter cities by state name
    const stateCities = data.filter(city =>
      city.state_name && city.state_name.toLowerCase() === stateName.toLowerCase()
    );

    // Transform cities
    const cities = stateCities.map(city => ({
      id: city.id,
      name: city.name,
      stateName: city.state_name,
      state_id: city.state_id,
      country_id: city.country_id
    }));

    cities.sort((a, b) => a.name.localeCompare(b.name));

    console.log(`✅ Loaded ${cities.length} cities for state ${stateName}`);
    return cities;

  } catch (error) {
    console.error(`❌ Error loading cities for state ${stateName}:`, error);
    return [];
  }
};

/**
 * Get cities for a specific country
 */
export const getCitiesByCountry = async (countryId) => {
  try {
    const cacheKey = `country_cities_${countryId}`;
    if (citiesCache[cacheKey]) {
      console.log(`🏙️ Returning cached cities for country ${countryId}`);
      return citiesCache[cacheKey];
    }

    const data = await loadCitiesData();

    // Filter cities by country
    const countryCities = data.filter(city => city.country_id == countryId);

    // Transform cities
    const cities = countryCities.map(city => ({
      id: city.id,
      name: city.name,
      state_id: city.state_id,
      state_name: city.state_name,
      country_id: city.country_id,
      country_code: city.country_code
    }));

    cities.sort((a, b) => a.name.localeCompare(b.name));

    citiesCache[cacheKey] = cities;
    console.log(`✅ Loaded ${cities.length} cities for country ${countryId}`);
    return cities;

  } catch (error) {
    console.error(`❌ Error loading cities for country ${countryId}:`, error);
    return [];
  }
};

/**
 * Get cities for a specific state by state ID (backward compatibility)
 */
export const getDistrictsByState = async (stateId) => {
  try {
    return await getCitiesByState(stateId);
  } catch (error) {
    console.error(`❌ Error getting cities for state ID ${stateId}:`, error);
    return [];
  }
};

// ===== SEARCH FUNCTIONS =====



/**
 * Search states by name within a country
 */
export const searchStates = async (countryId, searchTerm) => {
  try {
    const states = await getStatesByCountry(countryId);
    return states.filter(state =>
      state.name.toLowerCase().includes(searchTerm.toLowerCase())
    );
  } catch (error) {
    console.error('❌ Error searching states:', error);
    return [];
  }
};

/**
 * Search states by name (backward compatibility - defaults to India)
 */
export const searchStatesLegacy = async (searchTerm) => {
  try {
    return await searchStates(101, searchTerm); // Default to India
  } catch (error) {
    console.error('❌ Error searching states:', error);
    return [];
  }
};

/**
 * Search cities by name within a state
 */
export const searchCities = async (stateId, searchTerm) => {
  try {
    const cities = await getCitiesByState(stateId);
    return cities.filter(city =>
      city.name.toLowerCase().includes(searchTerm.toLowerCase())
    );
  } catch (error) {
    console.error('❌ Error searching cities:', error);
    return [];
  }
};

/**
 * Search cities by name within a state (backward compatibility)
 */
export const searchCitiesLegacy = async (stateName, searchTerm) => {
  try {
    const cities = await getCitiesByStateName(stateName);
    return cities.filter(city =>
      city.name.toLowerCase().includes(searchTerm.toLowerCase())
    );
  } catch (error) {
    console.error('❌ Error searching cities:', error);
    return [];
  }
};

/**
 * Search districts by name within a state (backward compatibility)
 */
export const searchDistricts = async (stateId, searchTerm) => {
  try {
    const districts = await getDistrictsByState(stateId);
    return districts.filter(district =>
      district.name.toLowerCase().includes(searchTerm.toLowerCase())
    );
  } catch (error) {
    console.error('❌ Error searching districts:', error);
    return [];
  }
};

/**
 * Global search across all locations
 */
export const globalLocationSearch = async (searchTerm) => {
  try {
    const [countries, states, cities] = await Promise.all([
      searchCountries(searchTerm),
      searchStatesLegacy(searchTerm),
      searchCitiesLegacy('', searchTerm)
    ]);

    return {
      countries: countries.slice(0, 10), // Limit results
      states: states.slice(0, 10),
      cities: cities.slice(0, 20)
    };
  } catch (error) {
    console.error('❌ Error in global location search:', error);
    return { countries: [], states: [], cities: [] };
  }
};

// ===== UTILITY FUNCTIONS =====

/**
 * Clear cache (useful for refreshing data)
 */
export const clearLocationCache = () => {
  countriesData = null;
  statesData = null;
  citiesData = null;
  countriesCache = null;
  statesCache = {};
  citiesCache = {};
  console.log('🗑️ Location cache cleared');
};

/**
 * Get location hierarchy (country -> state -> city)
 */
export const getLocationHierarchy = async (cityId) => {
  try {
    const cities = await loadCitiesData();
    const city = cities.find(c => c.id == cityId);

    if (!city) {
      return null;
    }

    const [countries, states] = await Promise.all([
      getAllCountries(),
      getStatesByCountry(city.country_id)
    ]);

    const country = countries.find(c => c.id == city.country_id);
    const state = states.find(s => s.id == city.state_id);

    return {
      country: country || null,
      state: state || null,
      city: {
        id: city.id,
        name: city.name
      }
    };
  } catch (error) {
    console.error(`❌ Error getting location hierarchy for city ${cityId}:`, error);
    return null;
  }
};
