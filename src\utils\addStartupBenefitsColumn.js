/**
 * Quick fix to add startup_benefits column to vendors table
 */

import { sql } from '../config/database.js';

/**
 * Add startup_benefits column to vendors table
 */
export const addStartupBenefitsColumn = async () => {
  try {
    console.log('🔄 Adding startup_benefits column to vendors table...');

    // Check if column already exists
    const columnCheck = await sql`
      SELECT column_name 
      FROM information_schema.columns 
      WHERE table_name = 'vendors' AND column_name = 'startup_benefits'
    `;

    if (columnCheck.length > 0) {
      console.log('✅ startup_benefits column already exists');
      return { success: true, message: 'Column already exists' };
    }

    // Add the column
    await sql`
      ALTER TABLE vendors 
      ADD COLUMN startup_benefits VARCHAR(10) DEFAULT 'No'
    `;

    console.log('✅ startup_benefits column added successfully');

    // Verify the column was added
    const verifyCheck = await sql`
      SELECT column_name, data_type, column_default
      FROM information_schema.columns 
      WHERE table_name = 'vendors' AND column_name = 'startup_benefits'
    `;

    if (verifyCheck.length > 0) {
      console.log('✅ Column verification successful:', verifyCheck[0]);
      return { 
        success: true, 
        message: 'Column added successfully',
        columnInfo: verifyCheck[0]
      };
    } else {
      throw new Error('Column was not created properly');
    }

  } catch (error) {
    console.error('❌ Error adding startup_benefits column:', error);
    return { 
      success: false, 
      error: error.message 
    };
  }
};

/**
 * Test the startup_benefits column functionality
 */
export const testStartupBenefitsColumn = async () => {
  try {
    console.log('🧪 Testing startup_benefits column...');

    // First ensure column exists
    await addStartupBenefitsColumn();

    // Test reading vendors with startup_benefits
    const vendors = await sql`
      SELECT id, company_name, startup_benefits 
      FROM vendors 
      LIMIT 3
    `;

    console.log('📊 Sample vendor data with startup_benefits:', vendors);

    // Test updating a vendor's startup_benefits (if vendors exist)
    if (vendors.length > 0) {
      const testVendorId = vendors[0].id;
      
      await sql`
        UPDATE vendors 
        SET startup_benefits = 'Yes' 
        WHERE id = ${testVendorId}
      `;

      const updatedVendor = await sql`
        SELECT id, company_name, startup_benefits 
        FROM vendors 
        WHERE id = ${testVendorId}
      `;

      console.log('✅ Test update successful:', updatedVendor[0]);

      // Reset back to original value
      await sql`
        UPDATE vendors 
        SET startup_benefits = 'No' 
        WHERE id = ${testVendorId}
      `;

      console.log('✅ Test completed successfully');
    }

    return { success: true, message: 'All tests passed' };

  } catch (error) {
    console.error('❌ Test failed:', error);
    return { success: false, error: error.message };
  }
};

// Make functions available globally for debugging
if (typeof window !== 'undefined' && process.env.NODE_ENV === 'development') {
  window.addStartupBenefitsColumn = addStartupBenefitsColumn;
  window.testStartupBenefitsColumn = testStartupBenefitsColumn;
  console.log('🔧 Startup Benefits functions available:');
  console.log('- window.addStartupBenefitsColumn() - Add the column');
  console.log('- window.testStartupBenefitsColumn() - Test functionality');
}
