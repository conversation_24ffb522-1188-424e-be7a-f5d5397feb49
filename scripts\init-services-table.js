import postgres from 'postgres';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Get database URL from environment
const databaseUrl = process.env.VITE_DATABASE_URL || process.env.DATABASE_URL;

if (!databaseUrl) {
  console.error('❌ Database URL not found. Please set VITE_DATABASE_URL or DATABASE_URL in your .env file.');
  process.exit(1);
}

// Create database connection
const sql = postgres(databaseUrl, {
  ssl: databaseUrl.includes('neon.tech') ? { rejectUnauthorized: false } : false
});

async function initServicesTable() {
  try {
    console.log('🔄 Initializing services table...');

    // Create services table
    await sql`
      CREATE TABLE IF NOT EXISTS services (
        id SERIAL PRIMARY KEY,
        name VARCHAR(255) NOT NULL UNIQUE,
        description TEXT,
        category VARCHAR(100),
        "isActive" BOOLEAN DEFAULT TRUE,
        status VARCHAR(50) DEFAULT 'Active',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `;

    console.log('✅ Services table created successfully!');

  } catch (error) {
    console.error('❌ Error creating services table:', error);
    process.exit(1);
  } finally {
    // Close database connection
    await sql.end();
  }
}

// Run the initialization
initServicesTable();
