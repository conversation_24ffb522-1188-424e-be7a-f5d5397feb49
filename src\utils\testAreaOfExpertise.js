/**
 * Quick test for Area of Expertise functionality
 * This will test the fixed service and create the table if needed
 */

import { sql } from '../config/database.js';
import { getAllAreaOfExpertise, createAreaOfExpertise } from '../services/areaOfExpertiseService.js';

/**
 * Test and setup area of expertise
 */
export const testAndSetupAreaOfExpertise = async () => {
  try {
    console.log('🧪 Testing Area of Expertise functionality...');

    // Step 1: Create table if it doesn't exist
    console.log('📋 Creating area_of_expertise table if needed...');
    await sql`
      CREATE TABLE IF NOT EXISTS area_of_expertise (
        id SERIAL PRIMARY KEY,
        name VARCHAR(255) NOT NULL UNIQUE,
        description TEXT,
        is_active BOOLEAN DEFAULT TRUE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `;
    console.log('✅ Table created/verified');

    // Step 2: Test reading data
    console.log('📖 Testing data retrieval...');
    let areas = await getAllAreaOfExpertise();
    console.log(`📊 Found ${areas.length} existing areas`);

    // Step 3: If no data, create sample data
    if (areas.length === 0) {
      console.log('🔄 No data found, creating sample areas...');
      
      const sampleAreas = [
        { name: 'Patent Law', description: 'Patent filing, prosecution, and enforcement expertise' },
        { name: 'Trademark Law', description: 'Trademark registration, protection, and enforcement' },
        { name: 'Copyright Law', description: 'Copyright registration and protection services' },
        { name: 'IP Litigation', description: 'Intellectual property litigation and dispute resolution' },
        { name: 'Technical Writing', description: 'Patent drafting and technical documentation' }
      ];

      for (const area of sampleAreas) {
        try {
          await createAreaOfExpertise(area.name, area.description, true);
          console.log(`✅ Created: ${area.name}`);
        } catch (error) {
          console.log(`⚠️ Skipped ${area.name}: ${error.message}`);
        }
      }

      // Re-fetch to verify
      areas = await getAllAreaOfExpertise();
      console.log(`📊 Now have ${areas.length} areas`);
    }

    // Step 4: Display results
    console.log('📋 Available areas:');
    areas.forEach(area => {
      console.log(`  - ${area.name} (ID: ${area.id})`);
    });

    console.log('🎉 Area of Expertise test completed successfully!');
    return {
      success: true,
      count: areas.length,
      areas: areas
    };

  } catch (error) {
    console.error('❌ Area of Expertise test failed:', error);
    return {
      success: false,
      error: error.message
    };
  }
};

// Make function available globally for testing
if (typeof window !== 'undefined' && process.env.NODE_ENV === 'development') {
  window.testAreaOfExpertise = testAndSetupAreaOfExpertise;
  console.log('🔧 Test function available: window.testAreaOfExpertise()');
}
