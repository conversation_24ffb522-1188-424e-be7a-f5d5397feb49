import { S3Client, PutObjectCommand, DeleteObjectCommand } from '@aws-sdk/client-s3';
import { v4 as uuidv4 } from 'uuid';
import multer from 'multer';

// S3 Client Configuration (Server-side only)
const s3Client = new S3Client({
  region: process.env.AWS_REGION || 'us-east-1',
  credentials: {
    accessKeyId: process.env.AWS_ACCESS_KEY_ID,
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY
  }
});

const BUCKET_NAME = process.env.AWS_S3_BUCKET_NAME || 'innoventory3solutions';
const BASE_URL = process.env.AWS_S3_BASE_URL || 'https://innoventory3solutions.s3.us-east-1.amazonaws.com';

// File type validation
const ALLOWED_FILE_TYPES = {
  images: ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'],
  documents: ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'],
  spreadsheets: ['application/vnd.ms-excel', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'],
  all: ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp', 'application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'application/vnd.ms-excel', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet']
};

const MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB

// Configure multer for memory storage
const upload = multer({
  storage: multer.memoryStorage(),
  limits: {
    fileSize: MAX_FILE_SIZE
  },
  fileFilter: (req, file, cb) => {
    const allowedTypes = req.query.allowedTypes || 'all';
    const validTypes = ALLOWED_FILE_TYPES[allowedTypes] || ALLOWED_FILE_TYPES.all;
    
    if (validTypes.includes(file.mimetype)) {
      cb(null, true);
    } else {
      cb(new Error(`File type ${file.mimetype} not allowed. Allowed types: ${validTypes.join(', ')}`));
    }
  }
});

/**
 * Generate a unique file key for S3
 */
const generateFileKey = (module, recordId, fileType, originalName) => {
  const timestamp = Date.now();
  const uuid = uuidv4().split('-')[0];
  const extension = originalName.split('.').pop();
  const sanitizedName = originalName.replace(/[^a-zA-Z0-9.-]/g, '_');
  
  return `${module}/${recordId}/${fileType}-${timestamp}-${uuid}-${sanitizedName}`;
};

/**
 * Upload file to S3
 */
const uploadToS3 = async (file, key) => {
  const command = new PutObjectCommand({
    Bucket: BUCKET_NAME,
    Key: key,
    Body: file.buffer,
    ContentType: file.mimetype,
    ContentDisposition: 'inline',
    CacheControl: 'max-age=31536000'
    // Note: ACL removed because bucket doesn't allow ACLs
    // Files will be accessible via signed URLs or bucket policy
  });

  await s3Client.send(command);

  return {
    key,
    url: `${BASE_URL}/${key}`,
    size: file.size,
    type: file.mimetype,
    name: file.originalname
  };
};

/**
 * Delete file from S3
 */
const deleteFromS3 = async (key) => {
  const command = new DeleteObjectCommand({
    Bucket: BUCKET_NAME,
    Key: key
  });

  await s3Client.send(command);
};

/**
 * Handle file upload API endpoint
 */
export default async function handler(req, res) {
  // Set CORS headers
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'POST, DELETE, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type');

  if (req.method === 'OPTIONS') {
    res.status(200).end();
    return;
  }

  if (req.method === 'POST') {
    try {
      // Use multer middleware
      const uploadMiddleware = upload.single('file');
      
      await new Promise((resolve, reject) => {
        uploadMiddleware(req, res, (err) => {
          if (err) reject(err);
          else resolve();
        });
      });

      const { module, recordId, fileType } = req.query;
      
      if (!module || !recordId || !fileType) {
        return res.status(400).json({
          success: false,
          error: 'Missing required parameters: module, recordId, fileType'
        });
      }

      if (!req.file) {
        return res.status(400).json({
          success: false,
          error: 'No file uploaded'
        });
      }

      console.log('📤 Uploading file:', {
        module,
        recordId,
        fileType,
        fileName: req.file.originalname,
        fileSize: req.file.size,
        fileType: req.file.mimetype
      });

      // Generate unique key
      const key = generateFileKey(module, recordId, fileType, req.file.originalname);
      
      // Upload to S3
      const result = await uploadToS3(req.file, key);
      
      console.log('✅ File uploaded successfully:', result);

      res.status(200).json({
        success: true,
        file: result
      });

    } catch (error) {
      console.error('❌ Upload error:', error);
      res.status(500).json({
        success: false,
        error: error.message || 'Upload failed'
      });
    }
  } 
  
  else if (req.method === 'DELETE') {
    try {
      const { key } = req.query;
      
      if (!key) {
        return res.status(400).json({
          success: false,
          error: 'Missing file key'
        });
      }

      console.log('🗑️ Deleting file:', key);
      
      await deleteFromS3(key);
      
      console.log('✅ File deleted successfully');

      res.status(200).json({
        success: true,
        message: 'File deleted successfully'
      });

    } catch (error) {
      console.error('❌ Delete error:', error);
      res.status(500).json({
        success: false,
        error: error.message || 'Delete failed'
      });
    }
  } 
  
  else {
    res.status(405).json({
      success: false,
      error: 'Method not allowed'
    });
  }
}
