// ESM module
import { createRequire } from 'module';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';
import * as dotenv from 'dotenv';
import postgres from 'postgres';

// Setup paths for ESM
const require = createRequire(import.meta.url);
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Load environment variables
dotenv.config({ path: join(__dirname, '..', '.env') });

// Get database URL
const databaseUrl = process.env.DATABASE_URL || process.env.VITE_DATABASE_URL;
if (!databaseUrl) {
  throw new Error('Database URL not found. Please set DATABASE_URL or VITE_DATABASE_URL in your .env file.');
}

// Create SQL client
const sql = postgres(databaseUrl, {
  ssl: { rejectUnauthorized: false },
  max: 1
});

/**
 * Seed default company types
 */

const defaultCompanyTypes = [
  {
    name: 'Private Limited Company',
    description: 'A private company limited by shares, commonly used for small to medium businesses'
  },
  {
    name: 'Public Limited Company',
    description: 'A company whose shares can be traded publicly on stock exchanges'
  },
  {
    name: 'Partnership Firm',
    description: 'A business structure where two or more individuals share ownership and responsibilities'
  },
  {
    name: 'Limited Liability Partnership (LLP)',
    description: 'A partnership where partners have limited liability protection'
  },
  {
    name: 'Sole Proprietorship',
    description: 'A business owned and operated by a single individual'
  },
  {
    name: 'One Person Company (OPC)',
    description: 'A company with only one shareholder, providing limited liability benefits'
  },
  {
    name: 'Section 8 Company',
    description: 'A non-profit company formed for promoting commerce, art, science, sports, education, research, social welfare, religion, charity, or protection of environment'
  },
  {
    name: 'Producer Company',
    description: 'A company formed by primary producers (farmers, artisans, etc.) for mutual benefit'
  },
  {
    name: 'Nidhi Company',
    description: 'A company formed for cultivating the habit of thrift and savings among members'
  },
  {
    name: 'Government Company',
    description: 'A company in which at least 51% of the paid-up share capital is held by the government'
  },
  {
    name: 'Foreign Company',
    description: 'A company incorporated outside India but conducting business in India'
  },
  {
    name: 'Trust',
    description: 'A legal arrangement where assets are held by trustees for beneficiaries'
  },
  {
    name: 'Society',
    description: 'A non-profit organization registered under the Societies Registration Act'
  },
  {
    name: 'Cooperative Society',
    description: 'An organization owned and operated by members for their mutual benefit'
  },
  {
    name: 'Individual',
    description: 'Individual person or freelancer'
  }
];

async function seedCompanyTypes() {
  try {
    console.log('🌱 Starting company types seeding...');

    // Check if company types already exist
    const existingTypes = await sql`SELECT COUNT(*) as count FROM company_types`;
    const count = parseInt(existingTypes[0].count);

    if (count > 0) {
      console.log(`ℹ️ Found ${count} existing company types. Skipping seeding.`);
      console.log('💡 To re-seed, delete existing company types first.');
      return;
    }

    console.log('📝 Creating default company types...');

    // Insert default company types
    for (const companyType of defaultCompanyTypes) {
      const companyTypeId = `comptype-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
      
      await sql`
        INSERT INTO company_types (
          id,
          name,
          description,
          "isActive",
          "createdAt",
          "updatedAt"
        ) VALUES (
          ${companyTypeId},
          ${companyType.name},
          ${companyType.description},
          true,
          NOW(),
          NOW()
        )
      `;

      console.log(`✅ Created: ${companyType.name}`);
      
      // Small delay to ensure unique timestamps
      await new Promise(resolve => setTimeout(resolve, 10));
    }

    console.log(`🎉 Successfully created ${defaultCompanyTypes.length} company types!`);

    // Display summary
    const finalCount = await sql`SELECT COUNT(*) as count FROM company_types WHERE "isActive" = true`;
    console.log(`📊 Total active company types: ${finalCount[0].count}`);

  } catch (error) {
    console.error('❌ Error seeding company types:', error);
    throw error;
  }
}

// Run the seeding
seedCompanyTypes()
  .then(() => {
    console.log('✅ Company types seeding completed successfully!');
    process.exit(0);
  })
  .catch((error) => {
    console.error('❌ Company types seeding failed:', error);
    process.exit(1);
  });
