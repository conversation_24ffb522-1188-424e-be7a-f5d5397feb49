import { sql } from '../config/database.js';

/**
 * User Permissions Service
 * Handles user permission management in the database
 */

// Create user permissions
export const createUserPermissions = async (userId, permissions) => {
  try {
    console.log('🔐 Creating permissions for user:', userId, permissions);

    if (!permissions || permissions.length === 0) {
      console.log('⚠️ No permissions provided, skipping permission creation');
      return [];
    }

    // Delete existing permissions for this user first
    await sql`DELETE FROM user_permissions WHERE "userId" = ${userId}`;

    // Create new permissions
    const permissionPromises = permissions.map(permission => {
      const permissionId = `perm-${userId}-${permission}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
      return sql`
        INSERT INTO user_permissions (
          id,
          "userId",
          permission,
          "createdAt"
        ) VALUES (
          ${permissionId},
          ${userId},
          ${permission},
          NOW()
        )
        RETURNING *
      `;
    });

    const results = await Promise.all(permissionPromises);
    const createdPermissions = results.map(result => result[0]);

    console.log('✅ User permissions created successfully:', createdPermissions.length);
    return createdPermissions;

  } catch (error) {
    console.error('❌ Error creating user permissions:', error);
    throw error;
  }
};

// Get user permissions
export const getUserPermissions = async (userId) => {
  try {
    console.log('🔍 Getting permissions for user:', userId);

    const permissions = await sql`
      SELECT 
        id,
        "userId",
        permission,
        "createdAt"
      FROM user_permissions 
      WHERE "userId" = ${userId}
      ORDER BY "createdAt" ASC
    `;

    const permissionValues = permissions.map(p => p.permission);
    console.log('✅ User permissions retrieved:', permissionValues);
    
    return permissionValues;

  } catch (error) {
    console.error('❌ Error getting user permissions:', error);
    throw error;
  }
};

// Update user permissions
export const updateUserPermissions = async (userId, permissions) => {
  try {
    console.log('🔄 Updating permissions for user:', userId, permissions);

    // Delete existing permissions
    await sql`DELETE FROM user_permissions WHERE "userId" = ${userId}`;

    // Create new permissions if any provided
    if (permissions && permissions.length > 0) {
      return await createUserPermissions(userId, permissions);
    }

    console.log('✅ User permissions updated (cleared)');
    return [];

  } catch (error) {
    console.error('❌ Error updating user permissions:', error);
    throw error;
  }
};

// Delete user permissions
export const deleteUserPermissions = async (userId) => {
  try {
    console.log('🗑️ Deleting all permissions for user:', userId);

    await sql`DELETE FROM user_permissions WHERE "userId" = ${userId}`;

    console.log('✅ User permissions deleted successfully');
    return true;

  } catch (error) {
    console.error('❌ Error deleting user permissions:', error);
    throw error;
  }
};

// Check if user has specific permission
export const hasPermission = async (userId, permission) => {
  try {
    const permissions = await sql`
      SELECT id 
      FROM user_permissions 
      WHERE "userId" = ${userId} AND permission = ${permission}
      LIMIT 1
    `;

    return permissions.length > 0;

  } catch (error) {
    console.error('❌ Error checking user permission:', error);
    return false;
  }
};

// Get all users with specific permission
export const getUsersWithPermission = async (permission) => {
  try {
    const users = await sql`
      SELECT DISTINCT u.id, u.name, u.email, u.role
      FROM users u
      INNER JOIN user_permissions up ON u.id = up."userId"
      WHERE up.permission = ${permission} AND u."isActive" = true
      ORDER BY u.name ASC
    `;

    return users;

  } catch (error) {
    console.error('❌ Error getting users with permission:', error);
    throw error;
  }
};

// Get permission summary for all users
export const getPermissionSummary = async () => {
  try {
    const summary = await sql`
      SELECT 
        u.id,
        u.name,
        u.email,
        u.role,
        u."isActive",
        COALESCE(
          JSON_AGG(up.permission ORDER BY up.permission) FILTER (WHERE up.permission IS NOT NULL),
          '[]'::json
        ) as permissions
      FROM users u
      LEFT JOIN user_permissions up ON u.id = up."userId"
      WHERE u.role = 'SUB_ADMIN'
      GROUP BY u.id, u.name, u.email, u.role, u."isActive"
      ORDER BY u.name ASC
    `;

    return summary;

  } catch (error) {
    console.error('❌ Error getting permission summary:', error);
    throw error;
  }
};
