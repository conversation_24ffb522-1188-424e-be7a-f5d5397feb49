/**
 * Test PDF Export Functionality
 * Direct test for jsPDF and autoTable functionality
 */

/**
 * Test basic jsPDF functionality
 */
export const testBasicPDF = async () => {
  try {
    console.log('🧪 Testing basic jsPDF functionality...');
    
    // Dynamic import
    const jsPDFModule = await import('jspdf');
    const jsPDFClass = jsPDFModule.default;
    
    const doc = new jsPDFClass();
    doc.text('Hello World - Basic PDF Test', 10, 10);
    doc.save('basic_test.pdf');
    
    console.log('✅ Basic PDF test successful!');
    return true;
    
  } catch (error) {
    console.error('❌ Basic PDF test failed:', error);
    return false;
  }
};

/**
 * Test jsPDF with autoTable
 */
export const testAutoTablePDF = async () => {
  try {
    console.log('🧪 Testing jsPDF with autoTable...');
    
    // Dynamic import with autoTable
    const jsPDFModule = await import('jspdf');
    await import('jspdf-autotable');
    
    const jsPDFClass = jsPDFModule.default;
    const doc = new jsPDFClass();
    
    // Add title
    doc.setFontSize(16);
    doc.text('AutoTable Test', 14, 22);
    
    // Test data
    const headers = ['Name', 'Description', 'Status', 'Date'];
    const rows = [
      ['Test Item 1', 'This is a test description', 'Active', '16/7/2025'],
      ['Test Item 2', 'Another test description', 'Inactive', '16/7/2025'],
      ['Test Item 3', 'Third test description', 'Active', '16/7/2025']
    ];
    
    console.log('🔧 Checking if autoTable is available...');
    console.log('autoTable method:', typeof doc.autoTable);
    
    if (typeof doc.autoTable === 'function') {
      console.log('✅ autoTable is available, creating table...');
      
      doc.autoTable({
        head: [headers],
        body: rows,
        startY: 30,
        styles: {
          fontSize: 10,
          cellPadding: 3,
          lineColor: [0, 0, 0],
          lineWidth: 0.1,
        },
        headStyles: {
          fillColor: [59, 130, 246],
          textColor: [255, 255, 255],
          fontStyle: 'bold',
        },
        alternateRowStyles: {
          fillColor: [248, 250, 252],
        },
      });
      
      doc.save('autotable_test.pdf');
      console.log('✅ AutoTable PDF test successful!');
      return true;
      
    } else {
      console.log('❌ autoTable is not available');
      console.log('Available methods:', Object.getOwnPropertyNames(doc));
      
      // Fallback test
      console.log('🔄 Testing fallback method...');
      doc.setFontSize(12);
      doc.text('AutoTable not available - using fallback', 14, 40);
      
      let y = 60;
      headers.forEach((header, index) => {
        doc.text(header, 14 + (index * 40), y);
      });
      
      y += 10;
      rows.forEach((row, rowIndex) => {
        row.forEach((cell, cellIndex) => {
          doc.text(cell, 14 + (cellIndex * 40), y);
        });
        y += 8;
      });
      
      doc.save('fallback_test.pdf');
      console.log('✅ Fallback PDF test successful!');
      return false; // autoTable not working, but PDF generation works
    }
    
  } catch (error) {
    console.error('❌ AutoTable PDF test failed:', error);
    return false;
  }
};

/**
 * Test Type of Work data export
 */
export const testTypeOfWorkExport = async () => {
  try {
    console.log('🧪 Testing Type of Work data export...');
    
    // Get current page data
    const tables = document.querySelectorAll('table');
    if (tables.length === 0) {
      console.log('❌ No tables found on page');
      return false;
    }
    
    const table = tables[0];
    const headers = Array.from(table.querySelectorAll('th')).map(th => th.textContent.trim());
    const rows = Array.from(table.querySelectorAll('tbody tr')).map(tr => 
      Array.from(tr.querySelectorAll('td')).map(td => td.textContent.trim())
    );
    
    console.log('📋 Found headers:', headers);
    console.log('📄 Found rows:', rows.length);
    
    if (headers.length === 0 || rows.length === 0) {
      console.log('❌ No data found in table');
      return false;
    }
    
    // Create PDF with actual data
    const jsPDFModule = await import('jspdf');
    await import('jspdf-autotable');
    
    const jsPDFClass = jsPDFModule.default;
    const doc = new jsPDFClass();
    
    // Add title
    doc.setFontSize(16);
    doc.text('Type of Work Export Test', 14, 22);
    
    // Add timestamp
    doc.setFontSize(10);
    doc.text(`Generated on: ${new Date().toLocaleString()}`, 14, 32);
    
    if (typeof doc.autoTable === 'function') {
      doc.autoTable({
        head: [headers],
        body: rows,
        startY: 40,
        styles: {
          fontSize: 9,
          cellPadding: 3,
          lineColor: [0, 0, 0],
          lineWidth: 0.1,
        },
        headStyles: {
          fillColor: [59, 130, 246],
          textColor: [255, 255, 255],
          fontStyle: 'bold',
        },
        alternateRowStyles: {
          fillColor: [248, 250, 252],
        },
      });
    } else {
      // Fallback
      doc.setFontSize(12);
      let y = 50;
      
      headers.forEach((header, index) => {
        doc.text(header, 14 + (index * 30), y);
      });
      
      y += 10;
      rows.forEach((row, rowIndex) => {
        if (y > 280) {
          doc.addPage();
          y = 20;
        }
        
        row.forEach((cell, cellIndex) => {
          doc.text(cell.substring(0, 20), 14 + (cellIndex * 30), y);
        });
        y += 8;
      });
    }
    
    doc.save('type_of_work_test.pdf');
    console.log('✅ Type of Work export test successful!');
    return true;
    
  } catch (error) {
    console.error('❌ Type of Work export test failed:', error);
    return false;
  }
};

/**
 * Run all PDF tests
 */
export const runAllPDFTests = async () => {
  console.log('🚀 RUNNING ALL PDF TESTS...');
  console.log('');
  
  const results = {
    basicPDF: false,
    autoTablePDF: false,
    typeOfWorkExport: false
  };
  
  // Test 1: Basic PDF
  console.log('1️⃣ Testing Basic PDF Generation...');
  results.basicPDF = await testBasicPDF();
  console.log('');
  
  // Test 2: AutoTable PDF
  console.log('2️⃣ Testing AutoTable PDF Generation...');
  results.autoTablePDF = await testAutoTablePDF();
  console.log('');
  
  // Test 3: Type of Work Export
  console.log('3️⃣ Testing Type of Work Data Export...');
  results.typeOfWorkExport = await testTypeOfWorkExport();
  console.log('');
  
  // Summary
  console.log('📊 TEST RESULTS SUMMARY:');
  console.log(`✅ Basic PDF: ${results.basicPDF ? 'PASSED' : 'FAILED'}`);
  console.log(`${results.autoTablePDF ? '✅' : '❌'} AutoTable PDF: ${results.autoTablePDF ? 'PASSED' : 'FAILED'}`);
  console.log(`${results.typeOfWorkExport ? '✅' : '❌'} Type of Work Export: ${results.typeOfWorkExport ? 'PASSED' : 'FAILED'}`);
  
  console.log('');
  console.log('💡 RECOMMENDATIONS:');
  
  if (!results.basicPDF) {
    console.log('❌ jsPDF is not working - check installation');
  } else if (!results.autoTablePDF) {
    console.log('⚠️ autoTable plugin not working - using fallback method');
    console.log('   PDF will be generated but without table formatting');
  } else {
    console.log('✅ All PDF functionality working correctly!');
  }
  
  console.log('');
  console.log('📁 Check your downloads folder for test PDF files:');
  console.log('   - basic_test.pdf');
  console.log('   - autotable_test.pdf (or fallback_test.pdf)');
  console.log('   - type_of_work_test.pdf');
  
  return results;
};

// Make functions available globally
if (typeof window !== 'undefined') {
  window.testBasicPDF = testBasicPDF;
  window.testAutoTablePDF = testAutoTablePDF;
  window.testTypeOfWorkExport = testTypeOfWorkExport;
  window.runAllPDFTests = runAllPDFTests;
  
  console.log('🧪 PDF Test Functions Available:');
  console.log('- window.testBasicPDF() - Test basic PDF generation');
  console.log('- window.testAutoTablePDF() - Test autoTable functionality');
  console.log('- window.testTypeOfWorkExport() - Test with real data');
  console.log('- window.runAllPDFTests() - Run all tests');
}
