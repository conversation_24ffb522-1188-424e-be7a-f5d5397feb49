import { S3Client, PutObjectCommand, DeleteObjectCommand } from '@aws-sdk/client-s3';
import { v4 as uuidv4 } from 'uuid';
import multer from 'multer';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// S3 Client Configuration
const s3Client = new S3Client({
  region: process.env.AWS_REGION || 'us-east-1',
  credentials: {
    accessKeyId: process.env.AWS_ACCESS_KEY_ID,
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY
  }
});

const BUCKET_NAME = process.env.AWS_S3_BUCKET_NAME || 'innoventory3solutions';
const BASE_URL = process.env.AWS_S3_BASE_URL || 'https://innoventory3solutions.s3.us-east-1.amazonaws.com';

// File type validation
const ALLOWED_FILE_TYPES = {
  images: ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'],
  documents: ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'],
  spreadsheets: ['application/vnd.ms-excel', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'],
  all: ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp', 'application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'application/vnd.ms-excel', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet']
};

const MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB

// Configure multer for memory storage
const upload = multer({
  storage: multer.memoryStorage(),
  limits: {
    fileSize: MAX_FILE_SIZE
  },
  fileFilter: (req, file, cb) => {
    try {
      // Parse query parameters from URL if req.query is not available
      let allowedTypes = 'all';

      if (req.query && req.query.allowedTypes) {
        allowedTypes = req.query.allowedTypes;
      } else if (req.url) {
        // Parse URL manually if query is not available
        const url = new URL(req.url, 'http://localhost');
        allowedTypes = url.searchParams.get('allowedTypes') || 'all';
      }

      const validTypes = ALLOWED_FILE_TYPES[allowedTypes] || ALLOWED_FILE_TYPES.all;

      console.log('🔍 File filter check:', {
        fileName: file.originalname,
        mimeType: file.mimetype,
        allowedTypes,
        validTypes: validTypes.slice(0, 3) + '...' // Show first 3 types
      });

      if (validTypes.includes(file.mimetype)) {
        cb(null, true);
      } else {
        cb(new Error(`File type ${file.mimetype} not allowed. Allowed types: ${validTypes.join(', ')}`));
      }
    } catch (error) {
      console.error('❌ File filter error:', error);
      // Allow all files if there's an error in filtering
      cb(null, true);
    }
  }
});

/**
 * Generate a unique file key for S3
 */
const generateFileKey = (module, recordId, fileType, originalName) => {
  const timestamp = Date.now();
  const uuid = uuidv4().split('-')[0];
  const extension = originalName.split('.').pop();
  const sanitizedName = originalName.replace(/[^a-zA-Z0-9.-]/g, '_');
  
  return `${module}/${recordId}/${fileType}-${timestamp}-${uuid}-${sanitizedName}`;
};

/**
 * Upload file to S3
 */
const uploadToS3 = async (file, key) => {
  const command = new PutObjectCommand({
    Bucket: BUCKET_NAME,
    Key: key,
    Body: file.buffer,
    ContentType: file.mimetype,
    ContentDisposition: 'inline',
    CacheControl: 'max-age=31536000'
    // Note: ACL removed because bucket doesn't allow ACLs
    // Files will be accessible via signed URLs or bucket policy
  });

  await s3Client.send(command);

  return {
    key,
    url: `${BASE_URL}/${key}`,
    size: file.size,
    type: file.mimetype,
    name: file.originalname
  };
};

/**
 * Delete file from S3
 */
const deleteFromS3 = async (key) => {
  const command = new DeleteObjectCommand({
    Bucket: BUCKET_NAME,
    Key: key
  });

  await s3Client.send(command);
};

/**
 * Vite plugin for handling file uploads during development
 */
export default function uploadPlugin() {
  return {
    name: 'upload-plugin',
    configureServer(server) {
      // Handle file upload
      server.middlewares.use('/api/upload', (req, res, next) => {
        // Set CORS headers
        res.setHeader('Access-Control-Allow-Origin', '*');
        res.setHeader('Access-Control-Allow-Methods', 'POST, DELETE, OPTIONS');
        res.setHeader('Access-Control-Allow-Headers', 'Content-Type');

        if (req.method === 'OPTIONS') {
          res.statusCode = 200;
          res.end();
          return;
        }

        if (req.method === 'POST') {
          // Use multer middleware
          const uploadMiddleware = upload.single('file');
          
          uploadMiddleware(req, res, async (err) => {
            if (err) {
              console.error('❌ Upload middleware error:', err);
              res.statusCode = 400;
              res.setHeader('Content-Type', 'application/json');
              res.end(JSON.stringify({
                success: false,
                error: err.message
              }));
              return;
            }

            try {
              const url = new URL(req.url, `http://${req.headers.host}`);
              const { module, recordId, fileType } = Object.fromEntries(url.searchParams);
              
              if (!module || !recordId || !fileType) {
                res.statusCode = 400;
                res.setHeader('Content-Type', 'application/json');
                res.end(JSON.stringify({
                  success: false,
                  error: 'Missing required parameters: module, recordId, fileType'
                }));
                return;
              }

              if (!req.file) {
                res.statusCode = 400;
                res.setHeader('Content-Type', 'application/json');
                res.end(JSON.stringify({
                  success: false,
                  error: 'No file uploaded'
                }));
                return;
              }

              console.log('📤 Uploading file:', {
                module,
                recordId,
                fileType,
                fileName: req.file.originalname,
                fileSize: req.file.size,
                mimeType: req.file.mimetype
              });

              // Generate unique key
              const key = generateFileKey(module, recordId, fileType, req.file.originalname);
              
              // Upload to S3
              const result = await uploadToS3(req.file, key);
              
              console.log('✅ File uploaded successfully:', result);

              res.statusCode = 200;
              res.setHeader('Content-Type', 'application/json');
              res.end(JSON.stringify({
                success: true,
                file: result
              }));

            } catch (error) {
              console.error('❌ Upload error:', error);
              res.statusCode = 500;
              res.setHeader('Content-Type', 'application/json');
              res.end(JSON.stringify({
                success: false,
                error: error.message || 'Upload failed'
              }));
            }
          });
        } 
        
        else if (req.method === 'DELETE') {
          try {
            const url = new URL(req.url, `http://${req.headers.host}`);
            const { key } = Object.fromEntries(url.searchParams);
            
            if (!key) {
              res.statusCode = 400;
              res.setHeader('Content-Type', 'application/json');
              res.end(JSON.stringify({
                success: false,
                error: 'Missing file key'
              }));
              return;
            }

            console.log('🗑️ Deleting file:', key);
            
            deleteFromS3(key).then(() => {
              console.log('✅ File deleted successfully');

              res.statusCode = 200;
              res.setHeader('Content-Type', 'application/json');
              res.end(JSON.stringify({
                success: true,
                message: 'File deleted successfully'
              }));
            }).catch((error) => {
              console.error('❌ Delete error:', error);
              res.statusCode = 500;
              res.setHeader('Content-Type', 'application/json');
              res.end(JSON.stringify({
                success: false,
                error: error.message || 'Delete failed'
              }));
            });

          } catch (error) {
            console.error('❌ Delete error:', error);
            res.statusCode = 500;
            res.setHeader('Content-Type', 'application/json');
            res.end(JSON.stringify({
              success: false,
              error: error.message || 'Delete failed'
            }));
          }
        } 
        
        else {
          res.statusCode = 405;
          res.setHeader('Content-Type', 'application/json');
          res.end(JSON.stringify({
            success: false,
            error: 'Method not allowed'
          }));
        }
      });

      // Health check endpoint
      server.middlewares.use('/api/health', (req, res) => {
        res.setHeader('Content-Type', 'application/json');
        res.end(JSON.stringify({ 
          success: true, 
          message: 'Upload server is running',
          timestamp: new Date().toISOString()
        }));
      });
    }
  };
}
