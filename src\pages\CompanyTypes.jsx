import { useState, useEffect } from 'react';
import { PlusIcon, EyeIcon, PencilIcon, TrashIcon, BuildingOfficeIcon } from '@heroicons/react/24/outline';
import { 
  getAllCompanyTypes, 
  createCompanyType, 
  updateCompanyType, 
  deleteCompanyType,
  getCompanyTypeStats 
} from '../services/companyTypeService';

const CompanyTypes = () => {
  const [companyTypes, setCompanyTypes] = useState([]);
  const [showAddForm, setShowAddForm] = useState(false);
  const [showEditForm, setShowEditForm] = useState(false);
  const [editingType, setEditingType] = useState(null);
  const [showInactive, setShowInactive] = useState(false);
  const [loading, setLoading] = useState(true);
  const [stats, setStats] = useState(null);
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    isActive: true
  });

  // Load company types
  const loadCompanyTypes = async () => {
    try {
      setLoading(true);
      const types = await getAllCompanyTypes(showInactive);
      setCompanyTypes(types);
      
      // Load stats
      const statsData = await getCompanyTypeStats();
      setStats(statsData);
    } catch (error) {
      console.error('Error loading company types:', error);
      alert('Error loading company types: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadCompanyTypes();
  }, [showInactive]);

  // Handle form input changes
  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
  };

  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();

    // Validation
    if (!formData.name || formData.name.trim() === '') {
      alert('❌ Company type name is required');
      return;
    }

    if (formData.name.trim().length < 2) {
      alert('❌ Company type name must be at least 2 characters');
      return;
    }

    try {
      setLoading(true);

      if (editingType) {
        // Update existing company type
        await updateCompanyType(editingType.id, {
          name: formData.name.trim(),
          description: formData.description.trim() || null,
          isActive: formData.isActive
        });
        alert('✅ Company type updated successfully!');
      } else {
        // Create new company type
        await createCompanyType({
          name: formData.name.trim(),
          description: formData.description.trim() || null,
          isActive: formData.isActive
        });
        alert('✅ Company type created successfully!');
      }

      // Reset form and reload data
      setFormData({ name: '', description: '', isActive: true });
      setShowAddForm(false);
      setShowEditForm(false);
      setEditingType(null);
      await loadCompanyTypes();

    } catch (error) {
      console.error('Error saving company type:', error);
      alert('❌ Error saving company type: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  // Handle edit
  const handleEdit = (companyType) => {
    setEditingType(companyType);
    setFormData({
      name: companyType.name,
      description: companyType.description || '',
      isActive: companyType.isActive
    });
    setShowEditForm(true);
  };

  // Handle delete
  const handleDelete = async (companyType) => {
    const confirmMessage = `Are you sure you want to delete "${companyType.name}"?

⚠️ Note: If this company type is being used by vendors or customers, it will be deactivated instead of deleted to preserve data integrity.`;

    if (!confirm(confirmMessage)) {
      return;
    }

    try {
      setLoading(true);
      const result = await deleteCompanyType(companyType.id);
      alert('✅ ' + result.message);
      await loadCompanyTypes();
    } catch (error) {
      console.error('Error deleting company type:', error);
      alert('❌ Error deleting company type: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  // Cancel form
  const handleCancel = () => {
    setFormData({ name: '', description: '', isActive: true });
    setShowAddForm(false);
    setShowEditForm(false);
    setEditingType(null);
  };

  if (loading && companyTypes.length === 0) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading company types...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 flex items-center">
            <BuildingOfficeIcon className="h-8 w-8 mr-3 text-blue-600" />
            Company Types
          </h1>
          <p className="mt-1 text-sm text-gray-600">
            Manage company type categories for vendors and customers
          </p>
        </div>
        <div className="flex space-x-3">
          <button
            onClick={() => setShowInactive(!showInactive)}
            className={`btn-secondary flex items-center ${showInactive ? 'bg-orange-100 text-orange-700 border-orange-300' : ''}`}
          >
            <EyeIcon className="h-5 w-5 mr-2" />
            {showInactive ? 'Hide Inactive' : 'Show Inactive'}
          </button>
          <button
            onClick={() => setShowAddForm(true)}
            className="btn-primary flex items-center"
          >
            <PlusIcon className="h-5 w-5 mr-2" />
            Add Company Type
          </button>
        </div>
      </div>

      {/* Statistics */}
      {stats && (
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="card">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <BuildingOfficeIcon className="h-8 w-8 text-blue-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Total Types</p>
                <p className="text-2xl font-bold text-gray-900">{stats.overview.total_types}</p>
              </div>
            </div>
          </div>
          <div className="card">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="h-8 w-8 bg-green-100 rounded-full flex items-center justify-center">
                  <span className="text-green-600 font-bold">✓</span>
                </div>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Active Types</p>
                <p className="text-2xl font-bold text-green-600">{stats.overview.active_types}</p>
              </div>
            </div>
          </div>
          <div className="card">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="h-8 w-8 bg-gray-100 rounded-full flex items-center justify-center">
                  <span className="text-gray-600 font-bold">−</span>
                </div>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Inactive Types</p>
                <p className="text-2xl font-bold text-gray-600">{stats.overview.inactive_types}</p>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Company Types Table */}
      {companyTypes.length > 0 ? (
        <div className="card">
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Company Type
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Description
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Created
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {companyTypes.map((type) => (
                  <tr key={type.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <BuildingOfficeIcon className="h-5 w-5 text-gray-400 mr-3" />
                        <div>
                          <div className="text-sm font-medium text-gray-900">{type.name}</div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4">
                      <div className="text-sm text-gray-900">
                        {type.description || 'No description'}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                        type.isActive 
                          ? 'bg-green-100 text-green-800' 
                          : 'bg-red-100 text-red-800'
                      }`}>
                        {type.isActive ? 'Active' : 'Inactive'}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {new Date(type.createdAt).toLocaleDateString()}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <div className="flex space-x-2">
                        <button
                          onClick={() => handleEdit(type)}
                          className="text-blue-600 hover:text-blue-900"
                          title="Edit company type"
                        >
                          <PencilIcon className="h-4 w-4" />
                        </button>
                        <button
                          onClick={() => handleDelete(type)}
                          className="text-red-600 hover:text-red-900"
                          title="Delete company type"
                        >
                          <TrashIcon className="h-4 w-4" />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      ) : (
        <div className="text-center py-12">
          <BuildingOfficeIcon className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900">No company types</h3>
          <p className="mt-1 text-sm text-gray-500">
            Get started by creating a new company type.
          </p>
          <div className="mt-6">
            <button
              onClick={() => setShowAddForm(true)}
              className="btn-primary flex items-center mx-auto"
            >
              <PlusIcon className="h-5 w-5 mr-2" />
              Add Company Type
            </button>
          </div>
        </div>
      )}

      {/* Add/Edit Form Modal */}
      {(showAddForm || showEditForm) && (
        <div className="fixed inset-0 z-50 overflow-y-auto">
          <div className="flex items-center justify-center min-h-screen px-4">
            <div className="fixed inset-0 bg-black opacity-50" onClick={handleCancel}></div>
            <div className="relative bg-white rounded-lg max-w-md w-full">
              <form onSubmit={handleSubmit} className="p-6">
                <div className="flex items-center justify-between mb-6">
                  <h3 className="text-lg font-medium text-gray-900">
                    {editingType ? 'Edit Company Type' : 'Add New Company Type'}
                  </h3>
                  <button
                    type="button"
                    onClick={handleCancel}
                    className="text-gray-400 hover:text-gray-600"
                  >
                    ×
                  </button>
                </div>

                <div className="space-y-4">
                  {/* Name */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Company Type Name *
                    </label>
                    <input
                      type="text"
                      name="name"
                      value={formData.name}
                      onChange={handleInputChange}
                      required
                      className="input-field"
                      placeholder="e.g., Private Limited, Partnership, Sole Proprietorship"
                    />
                  </div>

                  {/* Description */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Description
                    </label>
                    <textarea
                      name="description"
                      value={formData.description}
                      onChange={handleInputChange}
                      rows={3}
                      className="input-field"
                      placeholder="Optional description of this company type"
                    />
                  </div>

                  {/* Status */}
                  <div className="flex items-center">
                    <input
                      type="checkbox"
                      name="isActive"
                      checked={formData.isActive}
                      onChange={handleInputChange}
                      className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                    />
                    <label className="ml-2 text-sm text-gray-700">
                      Active (available for selection)
                    </label>
                  </div>
                </div>

                <div className="flex justify-end space-x-3 mt-6">
                  <button
                    type="button"
                    onClick={handleCancel}
                    className="btn-secondary"
                  >
                    Cancel
                  </button>
                  <button
                    type="submit"
                    disabled={loading}
                    className="btn-primary"
                  >
                    {loading ? 'Saving...' : (editingType ? 'Update' : 'Create')}
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default CompanyTypes;
