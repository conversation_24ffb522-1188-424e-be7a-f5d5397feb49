// Test Syntax Fix
// This script tests if the duplicate declaration issue is resolved

console.log('🧪 Testing Syntax Fix...');

try {
  // Test import of upload config
  console.log('📦 Testing upload config import...');
  const uploadConfig = await import('./src/config/uploadConfig.js');
  console.log('✅ Upload config imported successfully');
  console.log('   - Functions available:', Object.keys(uploadConfig));
  
  // Test import of s3 service
  console.log('📦 Testing s3 service import...');
  const s3Service = await import('./src/services/s3Service.js');
  console.log('✅ S3 service imported successfully');
  console.log('   - Functions available:', Object.keys(s3Service));
  
  // Test that getPublicFileUrl is available and working
  console.log('🔧 Testing getPublicFileUrl function...');
  const testKey = 'test/123/document-test.pdf';
  const testUrl = s3Service.getPublicFileUrl(testKey);
  console.log('✅ getPublicFileUrl works correctly');
  console.log('   - Test URL:', testUrl);
  
  // Test that generateFileKey is available and working
  console.log('🔧 Testing generateFileKey function...');
  const testFileKey = s3Service.generateFileKey('test', '123', 'document', 'test.pdf');
  console.log('✅ generateFileKey works correctly');
  console.log('   - Generated key:', testFileKey);
  
  // Test that validateFile is available
  console.log('🔧 Testing validateFile function...');
  console.log('✅ validateFile function is available');
  
  console.log('\n🎉 All syntax tests passed!');
  console.log('✅ No duplicate declarations');
  console.log('✅ All functions are properly exported');
  console.log('✅ Imports work correctly');
  
} catch (error) {
  console.error('❌ Syntax test failed:', error.message);
  console.error('Full error:', error);
  process.exit(1);
}

console.log('\n🚀 Ready to test upload functionality!');
console.log('Next steps:');
console.log('1. Start dev server: npm run dev');
console.log('2. Open: http://localhost:5173/test-upload');
console.log('3. Test file upload');
