/**
 * Emergency Database Fix Component
 * This component provides a UI to fix the startup_benefits column issue
 */

import React, { useState } from 'react';
import { sql } from '../../config/database.js';

const DatabaseFix = () => {
  const [isFixing, setIsFixing] = useState(false);
  const [fixResult, setFixResult] = useState(null);
  const [logs, setLogs] = useState([]);

  const addLog = (message, type = 'info') => {
    const timestamp = new Date().toLocaleTimeString();
    setLogs(prev => [...prev, { message, type, timestamp }]);
  };

  const fixDatabase = async () => {
    setIsFixing(true);
    setFixResult(null);
    setLogs([]);

    try {
      addLog('🚨 Starting emergency database fix...', 'info');

      // Step 1: Test connection
      addLog('1️⃣ Testing database connection...', 'info');
      const connectionTest = await sql`SELECT NOW() as current_time`;
      addLog(`✅ Database connection successful: ${connectionTest[0].current_time}`, 'success');

      // Step 2: Check current table structure
      addLog('2️⃣ Checking vendors table structure...', 'info');
      const currentColumns = await sql`
        SELECT column_name, data_type, column_default
        FROM information_schema.columns 
        WHERE table_name = 'vendors'
        ORDER BY ordinal_position
      `;
      
      addLog(`📋 Found ${currentColumns.length} columns in vendors table`, 'info');

      // Step 3: Check if startup_benefits column exists
      addLog('3️⃣ Checking for startup_benefits column...', 'info');
      const startupBenefitsExists = currentColumns.some(col => col.column_name === 'startup_benefits');
      
      if (startupBenefitsExists) {
        addLog('✅ startup_benefits column already exists!', 'success');
      } else {
        addLog('❌ startup_benefits column is missing!', 'error');
        
        // Add the missing column
        addLog('4️⃣ Adding startup_benefits column...', 'info');
        await sql`
          ALTER TABLE vendors 
          ADD COLUMN startup_benefits VARCHAR(10) DEFAULT 'No'
        `;
        addLog('✅ startup_benefits column added successfully!', 'success');
      }

      // Step 4: Check if point_of_contact column exists
      addLog('5️⃣ Checking for point_of_contact column...', 'info');
      const pocExists = currentColumns.some(col => col.column_name === 'point_of_contact');
      
      if (pocExists) {
        addLog('✅ point_of_contact column already exists!', 'success');
      } else {
        addLog('❌ point_of_contact column is missing!', 'error');
        
        // Add point_of_contact column
        addLog('6️⃣ Adding point_of_contact column...', 'info');
        await sql`
          ALTER TABLE vendors 
          ADD COLUMN point_of_contact JSONB DEFAULT '[]'
        `;
        addLog('✅ point_of_contact column added successfully!', 'success');
      }

      // Step 5: Verify the fix
      addLog('7️⃣ Verifying the fix...', 'info');
      const updatedColumns = await sql`
        SELECT column_name, data_type, column_default
        FROM information_schema.columns 
        WHERE table_name = 'vendors'
        AND column_name IN ('startup_benefits', 'point_of_contact')
        ORDER BY column_name
      `;

      updatedColumns.forEach(col => {
        addLog(`✅ ${col.column_name} (${col.data_type}) DEFAULT ${col.column_default}`, 'success');
      });

      // Step 6: Test vendor table
      addLog('8️⃣ Testing vendor table...', 'info');
      const vendorCount = await sql`SELECT COUNT(*) as count FROM vendors`;
      addLog(`📊 Current vendor count: ${vendorCount[0].count}`, 'info');

      // Test query with new columns
      await sql`
        SELECT id, company_name, startup_benefits, point_of_contact 
        FROM vendors 
        LIMIT 1
      `;
      addLog('✅ Can successfully query new columns!', 'success');

      addLog('🎉 DATABASE FIX COMPLETED SUCCESSFULLY!', 'success');
      setFixResult({ success: true, message: 'Database fixed successfully!' });

    } catch (error) {
      addLog(`💥 Database fix failed: ${error.message}`, 'error');
      setFixResult({ success: false, error: error.message });
    } finally {
      setIsFixing(false);
    }
  };

  return (
    <div className="max-w-4xl mx-auto p-6">
      <div className="bg-red-50 border border-red-200 rounded-lg p-6 mb-6">
        <h2 className="text-xl font-bold text-red-800 mb-4">
          🚨 Emergency Database Fix
        </h2>
        <p className="text-red-700 mb-4">
          This tool will fix the "startup_benefits column does not exist" error by adding the missing column to your NeonDB vendors table.
        </p>
        
        <button
          onClick={fixDatabase}
          disabled={isFixing}
          className={`px-6 py-3 rounded-lg font-medium ${
            isFixing 
              ? 'bg-gray-400 cursor-not-allowed' 
              : 'bg-red-600 hover:bg-red-700 text-white'
          }`}
        >
          {isFixing ? 'Fixing Database...' : 'Fix Database Now'}
        </button>
      </div>

      {/* Fix Result */}
      {fixResult && (
        <div className={`border rounded-lg p-4 mb-6 ${
          fixResult.success 
            ? 'bg-green-50 border-green-200' 
            : 'bg-red-50 border-red-200'
        }`}>
          <h3 className={`font-bold ${
            fixResult.success ? 'text-green-800' : 'text-red-800'
          }`}>
            {fixResult.success ? '✅ Success!' : '❌ Failed!'}
          </h3>
          <p className={fixResult.success ? 'text-green-700' : 'text-red-700'}>
            {fixResult.success ? fixResult.message : fixResult.error}
          </p>
        </div>
      )}

      {/* Logs */}
      {logs.length > 0 && (
        <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
          <h3 className="font-bold text-gray-800 mb-3">Fix Logs:</h3>
          <div className="space-y-2 max-h-96 overflow-y-auto">
            {logs.map((log, index) => (
              <div key={index} className="flex items-start space-x-2">
                <span className="text-xs text-gray-500 mt-1">{log.timestamp}</span>
                <span className={`text-sm ${
                  log.type === 'success' ? 'text-green-600' :
                  log.type === 'error' ? 'text-red-600' :
                  'text-gray-700'
                }`}>
                  {log.message}
                </span>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Instructions */}
      <div className="mt-6 bg-blue-50 border border-blue-200 rounded-lg p-4">
        <h3 className="font-bold text-blue-800 mb-2">After Fix Instructions:</h3>
        <ol className="list-decimal list-inside text-blue-700 space-y-1">
          <li>Wait for the "Database fix completed successfully" message</li>
          <li>Go to Vendors → Add New Vendor</li>
          <li>Fill in the form and select startup benefits</li>
          <li>Save the vendor - should work without errors</li>
        </ol>
      </div>
    </div>
  );
};

export default DatabaseFix;
