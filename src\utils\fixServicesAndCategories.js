/**
 * DEFINITIVE FIX FOR SERVICES AND CATEGORIES
 * This will recreate tables with the EXACT structure the code expects
 */

import { sql } from '../config/database.js';

/**
 * Drop and recreate tables with correct structure
 */
export const recreateServicesAndCategories = async () => {
  try {
    console.log('🚨 FIXING SERVICES AND CATEGORIES - DEFINITIVE SOLUTION');
    console.log('This will recreate tables with the exact structure the code expects');
    console.log('');

    // Step 1: Test connection
    console.log('1️⃣ Testing database connection...');
    const connectionTest = await sql`SELECT NOW() as current_time`;
    console.log('✅ Database connected:', connectionTest[0].current_time);

    // Step 2: Drop existing tables (if they exist)
    console.log('');
    console.log('2️⃣ Dropping existing tables...');
    
    try {
      await sql`DROP TABLE IF EXISTS services CASCADE`;
      console.log('✅ Dropped services table');
    } catch (error) {
      console.log('ℹ️ Services table did not exist');
    }

    try {
      await sql`DROP TABLE IF EXISTS service_categories CASCADE`;
      console.log('✅ Dropped service_categories table');
    } catch (error) {
      console.log('ℹ️ Service_categories table did not exist');
    }

    // Step 3: Create service_categories table with EXACT structure
    console.log('');
    console.log('3️⃣ Creating service_categories table...');
    await sql`
      CREATE TABLE service_categories (
        id SERIAL PRIMARY KEY,
        name VARCHAR(255) NOT NULL UNIQUE,
        description TEXT,
        color VARCHAR(7) DEFAULT '#6B7280',
        "isActive" BOOLEAN DEFAULT true,
        status VARCHAR(50) DEFAULT 'Active',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `;
    console.log('✅ service_categories table created with correct structure');

    // Step 4: Create services table with EXACT structure
    console.log('');
    console.log('4️⃣ Creating services table...');
    await sql`
      CREATE TABLE services (
        id SERIAL PRIMARY KEY,
        name VARCHAR(255) NOT NULL UNIQUE,
        description TEXT,
        category VARCHAR(255),
        category_id INTEGER REFERENCES service_categories(id),
        "isActive" BOOLEAN DEFAULT true,
        status VARCHAR(50) DEFAULT 'Active',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `;
    console.log('✅ services table created with correct structure');

    // Step 5: Insert service categories with proper data
    console.log('');
    console.log('5️⃣ Inserting service categories...');
    const categoryInserts = await sql`
      INSERT INTO service_categories (name, description, color, "isActive", status) VALUES
      ('Legal Services', 'Legal and compliance related services', '#EF4444', true, 'Active'),
      ('Technology', 'IT and software development services', '#3B82F6', true, 'Active'),
      ('Consulting', 'Business and management consulting', '#10B981', true, 'Active'),
      ('Marketing', 'Marketing and advertising services', '#F59E0B', true, 'Active'),
      ('Finance', 'Financial and accounting services', '#8B5CF6', true, 'Active'),
      ('Design', 'Creative and design services', '#EC4899', true, 'Active')
      RETURNING id, name
    `;
    console.log('✅ Service categories inserted:', categoryInserts.length);

    // Step 6: Insert services with proper data
    console.log('');
    console.log('6️⃣ Inserting services...');
    const serviceInserts = await sql`
      INSERT INTO services (name, description, category, category_id, "isActive", status) VALUES
      ('Patent Filing', 'Patent application and filing services', 'Legal Services', 1, true, 'Active'),
      ('Trademark Registration', 'Trademark registration and protection', 'Legal Services', 1, true, 'Active'),
      ('Copyright Registration', 'Copyright protection and registration', 'Legal Services', 1, true, 'Active'),
      ('Legal Compliance', 'Regulatory compliance and legal advice', 'Legal Services', 1, true, 'Active'),
      
      ('Web Development', 'Website and web application development', 'Technology', 2, true, 'Active'),
      ('Mobile App Development', 'iOS and Android app development', 'Technology', 2, true, 'Active'),
      ('Software Development', 'Custom software solutions', 'Technology', 2, true, 'Active'),
      ('Database Management', 'Database design and management', 'Technology', 2, true, 'Active'),
      
      ('Business Strategy', 'Strategic planning and consulting', 'Consulting', 3, true, 'Active'),
      ('Market Research', 'Market analysis and research', 'Consulting', 3, true, 'Active'),
      ('Process Optimization', 'Business process improvement', 'Consulting', 3, true, 'Active'),
      ('Management Consulting', 'Leadership and management advice', 'Consulting', 3, true, 'Active'),
      
      ('Digital Marketing', 'Online marketing and SEO services', 'Marketing', 4, true, 'Active'),
      ('Social Media Marketing', 'Social media strategy and management', 'Marketing', 4, true, 'Active'),
      ('Content Marketing', 'Content creation and marketing', 'Marketing', 4, true, 'Active'),
      ('Brand Development', 'Brand strategy and development', 'Marketing', 4, true, 'Active'),
      
      ('Financial Planning', 'Financial strategy and planning', 'Finance', 5, true, 'Active'),
      ('Accounting Services', 'Bookkeeping and accounting', 'Finance', 5, true, 'Active'),
      ('Tax Consultation', 'Tax planning and consultation', 'Finance', 5, true, 'Active'),
      ('Investment Advisory', 'Investment planning and advice', 'Finance', 5, true, 'Active'),
      
      ('Graphic Design', 'Visual design and branding', 'Design', 6, true, 'Active'),
      ('UI/UX Design', 'User interface and experience design', 'Design', 6, true, 'Active'),
      ('Logo Design', 'Logo and brand identity design', 'Design', 6, true, 'Active'),
      ('Web Design', 'Website design and layout', 'Design', 6, true, 'Active')
      RETURNING id, name, category
    `;
    console.log('✅ Services inserted:', serviceInserts.length);

    // Step 7: Verify the structure
    console.log('');
    console.log('7️⃣ Verifying table structure...');
    
    const categoryColumns = await sql`
      SELECT column_name, data_type 
      FROM information_schema.columns 
      WHERE table_name = 'service_categories'
      ORDER BY ordinal_position
    `;
    console.log('📋 service_categories columns:', categoryColumns.map(c => c.column_name));

    const serviceColumns = await sql`
      SELECT column_name, data_type 
      FROM information_schema.columns 
      WHERE table_name = 'services'
      ORDER BY ordinal_position
    `;
    console.log('📋 services columns:', serviceColumns.map(c => c.column_name));

    // Step 8: Test data retrieval
    console.log('');
    console.log('8️⃣ Testing data retrieval...');
    
    const categoryCount = await sql`SELECT COUNT(*) as count FROM service_categories WHERE "isActive" = true`;
    console.log(`✅ Active service categories: ${categoryCount[0].count}`);
    
    const serviceCount = await sql`SELECT COUNT(*) as count FROM services WHERE "isActive" = true`;
    console.log(`✅ Active services: ${serviceCount[0].count}`);

    // Step 9: Test the exact queries the code uses
    console.log('');
    console.log('9️⃣ Testing exact queries from the code...');
    
    const testCategories = await sql`
      SELECT 
        id,
        name,
        description,
        color,
        "isActive",
        status,
        created_at,
        updated_at
      FROM service_categories 
      WHERE "isActive" = true AND (status IS NULL OR status != 'Inactive')
      ORDER BY name ASC
    `;
    console.log(`✅ Category query test: ${testCategories.length} results`);

    const testServices = await sql`
      SELECT 
        id,
        name,
        description,
        category,
        "isActive",
        status,
        created_at,
        updated_at
      FROM services 
      WHERE "isActive" = true AND (status IS NULL OR status != 'Inactive')
      ORDER BY name ASC
    `;
    console.log(`✅ Service query test: ${testServices.length} results`);

    console.log('');
    console.log('🎉 SERVICES AND CATEGORIES FIX COMPLETED SUCCESSFULLY!');
    console.log('');
    console.log('✅ What was fixed:');
    console.log('   - Recreated service_categories table with correct structure');
    console.log('   - Recreated services table with correct structure');
    console.log('   - Added proper column names ("isActive", status, color)');
    console.log('   - Inserted comprehensive sample data');
    console.log('   - Verified all queries work exactly as code expects');
    console.log('');
    console.log('🔄 REFRESH THE PAGE NOW - SERVICES AND CATEGORIES WILL WORK!');

    return {
      success: true,
      categories: testCategories.length,
      services: testServices.length,
      message: 'Services and categories fixed successfully'
    };

  } catch (error) {
    console.error('💥 SERVICES AND CATEGORIES FIX FAILED:', error);
    console.error('Error details:', error.message);
    return {
      success: false,
      error: error.message,
      message: 'Fix failed'
    };
  }
};

// Make available globally
if (typeof window !== 'undefined') {
  window.recreateServicesAndCategories = recreateServicesAndCategories;
  console.log('🔧 SERVICES FIX AVAILABLE: window.recreateServicesAndCategories()');
}
