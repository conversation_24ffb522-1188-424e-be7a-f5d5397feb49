/**
 * Standalone script to fix the startup_benefits column issue
 * Run this with: node fix-startup-benefits.js
 */

import { sql } from './src/config/database.js';

async function fixStartupBenefitsColumn() {
  try {
    console.log('🔧 Starting startup_benefits column fix...');

    // Check if column exists
    console.log('🔍 Checking if startup_benefits column exists...');
    const columnCheck = await sql`
      SELECT column_name 
      FROM information_schema.columns 
      WHERE table_name = 'vendors' AND column_name = 'startup_benefits'
    `;

    if (columnCheck.length > 0) {
      console.log('✅ startup_benefits column already exists');
      console.log('📊 Column info:', columnCheck[0]);
      return true;
    }

    // Add the column
    console.log('🔄 Adding startup_benefits column...');
    await sql`
      ALTER TABLE vendors 
      ADD COLUMN startup_benefits VARCHAR(10) DEFAULT 'No'
    `;

    console.log('✅ startup_benefits column added successfully');

    // Verify the column was added
    console.log('🔍 Verifying column was added...');
    const verifyCheck = await sql`
      SELECT column_name, data_type, column_default
      FROM information_schema.columns 
      WHERE table_name = 'vendors' AND column_name = 'startup_benefits'
    `;

    if (verifyCheck.length > 0) {
      console.log('✅ Column verification successful:', verifyCheck[0]);
    } else {
      throw new Error('Column was not created properly');
    }

    // Test the column by checking vendor count
    console.log('🧪 Testing column functionality...');
    const vendorCount = await sql`SELECT COUNT(*) as count FROM vendors`;
    console.log(`📊 Total vendors: ${vendorCount[0].count}`);

    // Show sample data
    const sampleVendors = await sql`
      SELECT id, company_name, startup_benefits 
      FROM vendors 
      LIMIT 3
    `;
    console.log('📋 Sample vendor data with startup_benefits:', sampleVendors);

    console.log('🎉 startup_benefits column fix completed successfully!');
    console.log('');
    console.log('✅ You can now:');
    console.log('   - Create new vendors with startup benefits');
    console.log('   - Edit existing vendors to set startup benefits');
    console.log('   - View startup benefits in vendor details');
    console.log('');

    return true;

  } catch (error) {
    console.error('❌ Error fixing startup_benefits column:', error);
    console.error('');
    console.error('🔧 Troubleshooting:');
    console.error('   - Check database connection');
    console.error('   - Verify database permissions');
    console.error('   - Ensure NeonDB is accessible');
    console.error('');
    return false;
  }
}

// Run the fix
fixStartupBenefitsColumn()
  .then((success) => {
    if (success) {
      console.log('🎯 Fix completed successfully!');
      process.exit(0);
    } else {
      console.log('❌ Fix failed!');
      process.exit(1);
    }
  })
  .catch((error) => {
    console.error('💥 Unexpected error:', error);
    process.exit(1);
  });
