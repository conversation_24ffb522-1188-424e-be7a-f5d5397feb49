@echo off
echo 🚀 Starting Vercel Deployment...
echo.

echo 📦 Installing dependencies...
npm install
if %errorlevel% neq 0 (
    echo ❌ Failed to install dependencies
    pause
    exit /b 1
)

echo.
echo 🔨 Building application...
npm run build
if %errorlevel% neq 0 (
    echo ❌ Build failed
    pause
    exit /b 1
)

echo.
echo 🔧 Installing Vercel CLI...
npm install -g vercel

echo.
echo 🔐 Login to Vercel...
vercel login

echo.
echo 🚀 Deploying to production...
vercel --prod

echo.
echo ✅ Deployment completed!
echo.
echo 📋 IMPORTANT: Don't forget to:
echo 1. Set environment variables in Vercel dashboard
echo 2. Run: node scripts/fix-database-schema.js
echo 3. Test your deployment
echo.
echo 📖 See DEPLOYMENT_GUIDE.md for details
pause
