import { neon } from '@neondatabase/serverless';

// Database connection - get from environment variables
// Support both Vite (import.meta.env) and Node.js (process.env) environments
let DATABASE_URL;

if (typeof import.meta !== 'undefined' && import.meta.env) {
  // Vite/browser environment
  DATABASE_URL = import.meta.env.VITE_DATABASE_URL;
} else {
  // Node.js environment - try both VITE_DATABASE_URL and DATABASE_URL
  DATABASE_URL = process.env.VITE_DATABASE_URL || process.env.DATABASE_URL;
}

if (!DATABASE_URL) {
  throw new Error('Database URL not found. Please set VITE_DATABASE_URL or DATABASE_URL in your .env file.');
}

console.log('🔗 Connecting to database:', DATABASE_URL.replace(/:[^:@]*@/, ':****@')); // Hide password in logs

export const sql = neon(DATABASE_URL);

// Test database connection
export const testConnection = async () => {
  try {
    console.log('🔄 Testing database connection...');
    console.log('🔗 Using database URL:', DATABASE_URL.replace(/:[^:@]*@/, ':****@'));

    const result = await sql`SELECT 1 as test, version() as db_version`;
    console.log('✅ Database connection successful');
    console.log('📊 Database info:', result[0]);
    return true;
  } catch (error) {
    console.error('❌ Database connection failed:', error);
    console.error('🔍 Error details:', {
      message: error.message,
      code: error.code,
      detail: error.detail
    });

    // Check if it's a common connection issue
    if (error.message.includes('getaddrinfo ENOTFOUND')) {
      console.error('🌐 Network issue: Cannot resolve database hostname');
    } else if (error.message.includes('authentication failed')) {
      console.error('🔐 Authentication issue: Check your database credentials');
    } else if (error.message.includes('database') && error.message.includes('does not exist')) {
      console.error('🗄️ Database issue: Database does not exist');
    }

    return false;
  }
};

// Initialize database (alias for initializeSchema)
export const initializeDatabase = async () => {
  return await initializeSchema();
};

// Initialize database schema
export const initializeSchema = async () => {
  try {
    console.log('🔄 Initializing database schema...');

    // Create vendors table
    await sql`
      CREATE TABLE IF NOT EXISTS vendors (
        id SERIAL PRIMARY KEY,
        company_name VARCHAR(255) NOT NULL,
        company_type VARCHAR(100),
        onboarding_date DATE,
        emails JSONB DEFAULT '[]',
        phones JSONB DEFAULT '[]',
        address TEXT,
        country VARCHAR(100),
        state VARCHAR(100),
        city VARCHAR(100),
        username VARCHAR(255),
        gst_number VARCHAR(50),
        description TEXT,
        services JSONB DEFAULT '[]',
        website VARCHAR(255),
        type_of_work VARCHAR(255),
        status VARCHAR(50) DEFAULT 'Pending',
        files JSONB DEFAULT '{}',
        point_of_contact JSONB DEFAULT '[]',
        startup_benefits VARCHAR(10) DEFAULT 'No',
        rating DECIMAL(3,2) DEFAULT 0.00,
        total_orders INTEGER DEFAULT 0,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `;

    // Create clients table
    await sql`
      CREATE TABLE IF NOT EXISTS clients (
        id SERIAL PRIMARY KEY,
        company_name VARCHAR(255) NOT NULL,
        company_type VARCHAR(100),
        onboarding_date DATE,
        emails JSONB DEFAULT '[]',
        phones JSONB DEFAULT '[]',
        address TEXT,
        country VARCHAR(100),
        state VARCHAR(100),
        city VARCHAR(100),
        dpiit_registered BOOLEAN DEFAULT FALSE,
        dpiit_number VARCHAR(100),
        files JSONB DEFAULT '{}',
        status VARCHAR(50) DEFAULT 'Active',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `;

    // Create orders table
    await sql`
      CREATE TABLE IF NOT EXISTS orders (
        id SERIAL PRIMARY KEY,
        reference_number VARCHAR(100) UNIQUE NOT NULL,
        client_id INTEGER REFERENCES clients(id),
        vendor_id INTEGER REFERENCES vendors(id),
        description TEXT,
        amount DECIMAL(15,2),
        status VARCHAR(50) DEFAULT 'Pending',
        priority VARCHAR(50) DEFAULT 'Medium',
        files JSONB DEFAULT '{}',
        status_history JSONB DEFAULT '[]',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `;

    // Create sub_admins table
    await sql`
      CREATE TABLE IF NOT EXISTS sub_admins (
        id SERIAL PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        email VARCHAR(255) UNIQUE NOT NULL,
        onboarding_date DATE,
        address TEXT,
        country VARCHAR(100),
        state VARCHAR(100),
        city VARCHAR(100),
        username VARCHAR(255),
        pan_number VARCHAR(50),
        term_of_work VARCHAR(100),
        files JSONB DEFAULT '{}',
        status VARCHAR(50) DEFAULT 'Active',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `;

    // Create type_of_work table
    await sql`
      CREATE TABLE IF NOT EXISTS type_of_work (
        id SERIAL PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        description TEXT,
        "isActive" BOOLEAN DEFAULT TRUE,
        "createdAt" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        "updatedAt" TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `;

    // Create area_of_expertise table
    await sql`
      CREATE TABLE IF NOT EXISTS area_of_expertise (
        id SERIAL PRIMARY KEY,
        name VARCHAR(255) NOT NULL UNIQUE,
        description TEXT,
        is_active BOOLEAN DEFAULT TRUE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `;

    // Create service_categories table
    await sql`
      CREATE TABLE IF NOT EXISTS service_categories (
        id SERIAL PRIMARY KEY,
        name VARCHAR(255) NOT NULL UNIQUE,
        description TEXT,
        color VARCHAR(7) DEFAULT '#6B7280',
        "isActive" BOOLEAN DEFAULT TRUE,
        status VARCHAR(50) DEFAULT 'Active',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `;

    // Create services table
    await sql`
      CREATE TABLE IF NOT EXISTS services (
        id SERIAL PRIMARY KEY,
        name VARCHAR(255) NOT NULL UNIQUE,
        description TEXT,
        category_id INTEGER REFERENCES service_categories(id),
        category VARCHAR(100),
        "isActive" BOOLEAN DEFAULT TRUE,
        status VARCHAR(50) DEFAULT 'Active',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `;

    // Run database migrations to add missing columns
    console.log('🔄 Running database migrations...');
    await runMigrations();

    console.log('✅ Database schema initialized successfully');
    return true;
  } catch (error) {
    console.error('❌ Database schema initialization failed:', error);
    return false;
  }
};

/**
 * Run database migrations to add missing columns
 */
export const runMigrations = async () => {
  try {
    console.log('🔄 Running database migrations...');

    // Migration 1: Add startup_benefits column if it doesn't exist
    try {
      console.log('🔍 Checking for startup_benefits column...');
      const columnCheck = await sql`
        SELECT column_name, data_type, column_default
        FROM information_schema.columns
        WHERE table_name = 'vendors' AND column_name = 'startup_benefits'
      `;

      if (columnCheck.length === 0) {
        console.log('❌ startup_benefits column is missing!');
        console.log('🔄 Adding startup_benefits column to vendors table...');

        await sql`
          ALTER TABLE vendors
          ADD COLUMN startup_benefits VARCHAR(10) DEFAULT 'No'
        `;

        console.log('✅ startup_benefits column added successfully');

        // Verify the column was added
        const verifyCheck = await sql`
          SELECT column_name, data_type, column_default
          FROM information_schema.columns
          WHERE table_name = 'vendors' AND column_name = 'startup_benefits'
        `;

        if (verifyCheck.length > 0) {
          console.log('✅ Column verification successful:', verifyCheck[0]);
        } else {
          console.error('❌ Column verification failed!');
        }
      } else {
        console.log('✅ startup_benefits column already exists:', columnCheck[0]);
      }
    } catch (error) {
      console.error('❌ Error adding startup_benefits column:', error);
      console.error('Error details:', error.message);
    }

    // Migration 2: Add point_of_contact column if it doesn't exist
    try {
      const pocColumnCheck = await sql`
        SELECT column_name
        FROM information_schema.columns
        WHERE table_name = 'vendors' AND column_name = 'point_of_contact'
      `;

      if (pocColumnCheck.length === 0) {
        console.log('🔄 Adding point_of_contact column to vendors table...');
        await sql`
          ALTER TABLE vendors
          ADD COLUMN point_of_contact JSONB DEFAULT '[]'
        `;
        console.log('✅ point_of_contact column added successfully');
      } else {
        console.log('✅ point_of_contact column already exists');
      }
    } catch (error) {
      console.error('❌ Error adding point_of_contact column:', error);
    }

    console.log('✅ Database migrations completed');
    return true;

  } catch (error) {
    console.error('❌ Error running migrations:', error);
    return false;
  }
};

/**
 * Emergency fix function for startup_benefits column
 * Can be called directly from browser console
 */
export const fixStartupBenefitsColumn = async () => {
  try {
    console.log('🚨 EMERGENCY FIX: Adding startup_benefits column...');

    // Check if column exists
    const columnCheck = await sql`
      SELECT column_name, data_type, column_default
      FROM information_schema.columns
      WHERE table_name = 'vendors' AND column_name = 'startup_benefits'
    `;

    if (columnCheck.length > 0) {
      console.log('✅ startup_benefits column already exists:', columnCheck[0]);
      return { success: true, message: 'Column already exists' };
    }

    // Add the column
    console.log('🔄 Adding startup_benefits column...');
    await sql`
      ALTER TABLE vendors
      ADD COLUMN startup_benefits VARCHAR(10) DEFAULT 'No'
    `;

    // Verify
    const verifyCheck = await sql`
      SELECT column_name, data_type, column_default
      FROM information_schema.columns
      WHERE table_name = 'vendors' AND column_name = 'startup_benefits'
    `;

    if (verifyCheck.length > 0) {
      console.log('✅ startup_benefits column added successfully:', verifyCheck[0]);
      console.log('🎉 You can now create vendors with startup benefits!');
      return { success: true, message: 'Column added successfully' };
    } else {
      throw new Error('Column verification failed');
    }

  } catch (error) {
    console.error('❌ Emergency fix failed:', error);
    return { success: false, error: error.message };
  }
};

// Make emergency fix available globally
if (typeof window !== 'undefined') {
  window.fixStartupBenefitsColumn = fixStartupBenefitsColumn;
  window.runMigrations = runMigrations;
  console.log('🔧 Emergency database fix available: window.fixStartupBenefitsColumn()');
}

// Insert sample data using the new seeding service
export const insertSampleData = async () => {
  try {
    console.log('🔄 Inserting sample data using seeding service...');

    // Import the seeding service dynamically to avoid circular dependencies
    const { seedAllData } = await import('../services/seedService.js');

    const result = await seedAllData();

    if (result.success) {
      console.log('✅ Sample data insertion completed successfully');
      console.log('📊 Seeding summary:', result.summary);
      return true;
    } else {
      console.warn('⚠️ Sample data insertion completed with some issues');
      console.log('📊 Seeding summary:', result.summary);
      return true; // Return true to not block app initialization
    }
  } catch (error) {
    console.error('❌ Error inserting sample data:', error);
    return true; // Return true to not block app initialization
  }
};

// Function to refresh database with corrected data
export const refreshDatabaseData = async () => {
  try {
    console.log('🔄 Refreshing database data...');
    console.log('⚠️ Skipping database refresh to avoid schema conflicts');
    console.log('✅ Database refresh completed (skipped for compatibility)');
    return true;
  } catch (error) {
    console.error('❌ Error refreshing database data:', error);
    return false;
  }
};
