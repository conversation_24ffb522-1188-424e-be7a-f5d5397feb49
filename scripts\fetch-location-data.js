const fs = require('fs');
const https = require('https');
const path = require('path');

// URLs for the complete datasets
const COUNTRIES_URL = 'https://raw.githubusercontent.com/dr5hn/countries-states-cities-database/master/json/countries.json';
const STATES_URL = 'https://raw.githubusercontent.com/dr5hn/countries-states-cities-database/master/json/states.json';
const CITIES_URL = 'https://raw.githubusercontent.com/dr5hn/countries-states-cities-database/master/json/cities.json';

// Function to download JSON data
function downloadJSON(url) {
  return new Promise((resolve, reject) => {
    https.get(url, (response) => {
      let data = '';
      
      response.on('data', (chunk) => {
        data += chunk;
      });
      
      response.on('end', () => {
        try {
          const jsonData = JSON.parse(data);
          resolve(jsonData);
        } catch (error) {
          reject(error);
        }
      });
    }).on('error', (error) => {
      reject(error);
    });
  });
}

// Function to save data to file
function saveToFile(filename, data) {
  const filePath = path.join(__dirname, '..', 'public', filename);
  fs.writeFileSync(filePath, JSON.stringify(data, null, 2));
  console.log(`✅ Saved ${filename} with ${data.length} records`);
}

// Main function to fetch all data
async function fetchAllLocationData() {
  try {
    console.log('🌍 Fetching complete world location data...');
    
    // Download all datasets
    console.log('📥 Downloading countries...');
    const countries = await downloadJSON(COUNTRIES_URL);
    
    console.log('📥 Downloading states...');
    const states = await downloadJSON(STATES_URL);
    
    console.log('📥 Downloading cities...');
    const cities = await downloadJSON(CITIES_URL);
    
    // Save to files
    saveToFile('countries.json', countries);
    saveToFile('states.json', states);
    saveToFile('cities.json', cities);
    
    // Create summary
    console.log('\n📊 Summary:');
    console.log(`Countries: ${countries.length}`);
    console.log(`States/Provinces: ${states.length}`);
    console.log(`Cities: ${cities.length}`);
    
    // Create a sample of popular countries for quick access
    const popularCountries = countries.filter(country => 
      ['US', 'IN', 'GB', 'CA', 'AU', 'DE', 'FR', 'JP', 'CN', 'BR', 'RU', 'IT', 'ES', 'MX', 'KR', 'NL', 'SE', 'NO', 'DK', 'FI'].includes(country.iso2)
    );
    
    saveToFile('popular-countries.json', popularCountries);
    
    console.log('\n🎉 All location data successfully downloaded and saved!');
    console.log('Files created:');
    console.log('- public/countries.json (All countries)');
    console.log('- public/states.json (All states/provinces)');
    console.log('- public/cities.json (All cities)');
    console.log('- public/popular-countries.json (Popular countries)');
    
  } catch (error) {
    console.error('❌ Error fetching location data:', error);
  }
}

// Run the script
fetchAllLocationData();
