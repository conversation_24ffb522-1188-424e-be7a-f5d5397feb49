// S3 Debug Utilities
// Helps diagnose S3 access issues

/**
 * Test S3 file access and provide debugging information
 * @param {string} fileUrl - The S3 file URL to test
 * @returns {Promise<object>} Test results
 */
export const testS3FileAccess = async (fileUrl) => {
  const results = {
    url: fileUrl,
    accessible: false,
    error: null,
    statusCode: null,
    headers: {},
    suggestions: []
  };

  try {
    console.log('🔍 Testing S3 file access:', fileUrl);
    
    // Test with fetch
    const response = await fetch(fileUrl, { method: 'HEAD' });
    
    results.statusCode = response.status;
    results.accessible = response.ok;
    
    // Get response headers
    response.headers.forEach((value, key) => {
      results.headers[key] = value;
    });
    
    if (!response.ok) {
      results.error = `HTTP ${response.status}: ${response.statusText}`;
      
      // Provide specific suggestions based on status code
      switch (response.status) {
        case 403:
          results.suggestions.push('File access denied - check S3 bucket permissions');
          results.suggestions.push('Ensure bucket has public read access or use signed URLs');
          results.suggestions.push('Check if ACL is set to public-read on the file');
          break;
        case 404:
          results.suggestions.push('File not found - check if the file was uploaded correctly');
          results.suggestions.push('Verify the S3 key/path is correct');
          break;
        case 500:
          results.suggestions.push('S3 server error - try again later');
          break;
        default:
          results.suggestions.push('Unknown error - check AWS S3 console for more details');
      }
    } else {
      results.suggestions.push('File is accessible - no issues detected');
    }
    
  } catch (error) {
    results.error = error.message;
    results.suggestions.push('Network error or CORS issue');
    results.suggestions.push('Check if the S3 bucket allows cross-origin requests');
  }
  
  return results;
};

/**
 * Extract S3 key from URL
 * @param {string} url - S3 URL
 * @returns {string|null} S3 key or null if not a valid S3 URL
 */
export const extractS3Key = (url) => {
  if (!url) return null;
  
  try {
    // Handle different S3 URL formats
    const patterns = [
      // https://bucket.s3.region.amazonaws.com/key
      /https:\/\/([^.]+)\.s3\.([^.]+)\.amazonaws\.com\/(.+)/,
      // https://s3.region.amazonaws.com/bucket/key
      /https:\/\/s3\.([^.]+)\.amazonaws\.com\/([^/]+)\/(.+)/,
      // https://bucket.s3.amazonaws.com/key (legacy)
      /https:\/\/([^.]+)\.s3\.amazonaws\.com\/(.+)/
    ];
    
    for (const pattern of patterns) {
      const match = url.match(pattern);
      if (match) {
        // Return the key part (last capture group)
        return match[match.length - 1];
      }
    }
    
    return null;
  } catch (error) {
    console.error('Error extracting S3 key:', error);
    return null;
  }
};

/**
 * Generate debug report for S3 file access issues
 * @param {string} fileUrl - The problematic file URL
 * @returns {Promise<string>} Debug report
 */
export const generateS3DebugReport = async (fileUrl) => {
  const testResults = await testS3FileAccess(fileUrl);
  const s3Key = extractS3Key(fileUrl);
  
  let report = `
🔍 S3 File Access Debug Report
=====================================

📄 File URL: ${fileUrl}
🔑 S3 Key: ${s3Key || 'Could not extract'}
📊 Status Code: ${testResults.statusCode || 'N/A'}
✅ Accessible: ${testResults.accessible ? 'Yes' : 'No'}
❌ Error: ${testResults.error || 'None'}

📋 Response Headers:
${Object.entries(testResults.headers).map(([key, value]) => `  ${key}: ${value}`).join('\n')}

💡 Suggestions:
${testResults.suggestions.map(suggestion => `  • ${suggestion}`).join('\n')}

🔧 Quick Fixes:
  1. Check AWS S3 Console → Bucket Permissions
  2. Ensure bucket policy allows public read access
  3. Verify file ACL is set to 'public-read'
  4. Try using signed URLs for private files

📞 Support Information:
  - Bucket: innoventory3solutions
  - Region: us-east-1
  - Generated: ${new Date().toISOString()}
`;

  return report;
};

/**
 * Log S3 debug information to console
 * @param {string} fileUrl - The file URL to debug
 */
export const logS3Debug = async (fileUrl) => {
  const report = await generateS3DebugReport(fileUrl);
  console.log(report);
};

export default {
  testS3FileAccess,
  extractS3Key,
  generateS3DebugReport,
  logS3Debug
};
