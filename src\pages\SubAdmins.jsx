import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { PlusIcon, KeyIcon, ShieldCheckIcon, EyeIcon, PencilIcon, TrashIcon } from '@heroicons/react/24/outline';
import DataTable from '../components/DataTable/DataTable';
import S3FileUpload from '../components/FileUpload/S3FileUpload';
import { getAllCountries, getStatesByCountry, getCitiesByState } from '../services/locationService';

const SubAdmins = () => {
  const navigate = useNavigate();
  const [showAddForm, setShowAddForm] = useState(false);
  const [formData, setFormData] = useState({
    subAdminOnboardingDate: '',
    name: '',
    email: '',
    password: '',
    confirmPassword: '',
    address: '',
    country: '',
    countryId: '',
    state: '',
    stateId: '',
    city: '',
    username: '',
    panNumber: '',
    termOfWork: '',
    // Legacy fields for compatibility
    role: '',
    permissions: [],
    status: 'Active',
    isActive: true,
    mustChangePassword: true
  });
  const [uploadedFiles, setUploadedFiles] = useState({
    tdsFile: [],
    ndaFile: [],
    employmentAgreement: [],
    panCard: []
  });
  const [tempSubAdminId] = useState(() => `temp-subadmin-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`);

  const [subAdmins, setSubAdmins] = useState([]);
  const [loading, setLoading] = useState(true);
  const [showInactive, setShowInactive] = useState(false);

  // Location data states
  const [countries, setCountries] = useState([]);
  const [states, setStates] = useState([]);
  const [cities, setCities] = useState([]);
  const [locationLoading, setLocationLoading] = useState(false);

  // Load sub-admins from database
  useEffect(() => {
    const loadSubAdmins = async () => {
      try {
        setLoading(true);
        // Import and use sub-admin service
        const { getAllSubAdmins } = await import('../services/subAdminService');
        const dbSubAdmins = await getAllSubAdmins(showInactive);

        setSubAdmins(dbSubAdmins || []);
      } catch (err) {
        console.error('❌ Error loading sub-admins:', err);
        setSubAdmins([]);
      } finally {
        setLoading(false);
      }
    };

    loadSubAdmins();
  }, [showInactive]);



  const availablePermissions = [
    { value: 'MANAGE_USERS', label: 'Manage Users', description: 'Create, edit, and manage sub-admin users' },
    { value: 'MANAGE_CUSTOMERS', label: 'Manage Customers', description: 'Create, edit, and manage customer records' },
    { value: 'MANAGE_VENDORS', label: 'Manage Vendors', description: 'Create, edit, and manage vendor records' },
    { value: 'MANAGE_ORDERS', label: 'Manage Orders', description: 'Create, edit, and manage orders' },
    { value: 'VIEW_ANALYTICS', label: 'View Analytics', description: 'Access dashboard analytics and insights' },
    { value: 'MANAGE_PAYMENTS', label: 'Manage Payments', description: 'Handle payment processing and invoicing' },
    { value: 'VIEW_REPORTS', label: 'View Reports', description: 'Access and generate system reports' }
  ];

  // Load countries on component mount
  useEffect(() => {
    const loadCountries = async () => {
      try {
        setLocationLoading(true);
        const countriesData = await getAllCountries();
        setCountries(countriesData);
      } catch (error) {
        console.error('Error loading countries:', error);
      } finally {
        setLocationLoading(false);
      }
    };

    loadCountries();
  }, []);

  // Load states when country changes
  useEffect(() => {
    const loadStates = async () => {
      if (formData.countryId) {
        try {
          setLocationLoading(true);
          const statesData = await getStatesByCountry(formData.countryId);
          setStates(statesData);
        } catch (error) {
          console.error('Error loading states:', error);
          setStates([]);
        } finally {
          setLocationLoading(false);
        }
      } else {
        setStates([]);
      }
    };

    loadStates();
  }, [formData.countryId]);

  // Load cities when state changes
  useEffect(() => {
    const loadCities = async () => {
      if (formData.stateId) {
        try {
          setLocationLoading(true);
          const citiesData = await getCitiesByState(formData.stateId);
          setCities(citiesData);
        } catch (error) {
          console.error('Error loading cities:', error);
          setCities([]);
        } finally {
          setLocationLoading(false);
        }
      } else {
        setCities([]);
      }
    };

    loadCities();
  }, [formData.stateId]);

  const handleInputChange = (e) => {
    const { name, value } = e.target;

    if (name === 'country') {
      // Find the selected country to get its ID
      const selectedCountry = countries.find(country => country.name === value);
      setFormData(prev => ({
        ...prev,
        country: value,
        countryId: selectedCountry ? selectedCountry.id : '',
        state: '',
        stateId: '',
        city: ''
      }));
      setStates([]);
      setCities([]);
    } else if (name === 'state') {
      // Find the selected state to get its ID
      const selectedState = states.find(state => state.name === value);
      setFormData(prev => ({
        ...prev,
        state: value,
        stateId: selectedState ? selectedState.id : '',
        city: ''
      }));
      setCities([]);
    } else {
      setFormData(prev => ({
        ...prev,
        [name]: value
      }));
    }
  };

  // File upload handler
  const handleFileUpload = (fileType, files) => {
    if (!files || files.length === 0) return;

    const maxSize = 10 * 1024 * 1024; // 10MB in bytes
    const validFiles = [];
    const errors = [];

    Array.from(files).forEach(file => {
      // Check file size
      if (file.size > maxSize) {
        errors.push(`${file.name} exceeds 10MB limit`);
        return;
      }

      // Handle duplicate filename by appending timestamp
      const existingFiles = uploadedFiles[fileType] || [];
      const existingNames = existingFiles.map(f => f.name);
      let fileName = file.name;

      if (existingNames.includes(fileName)) {
        const timestamp = new Date().getTime();
        const fileExtension = fileName.substring(fileName.lastIndexOf('.'));
        const fileNameWithoutExt = fileName.substring(0, fileName.lastIndexOf('.'));
        fileName = `${fileNameWithoutExt}_${timestamp}${fileExtension}`;
      }

      // Create new file object with updated name
      const newFile = new File([file], fileName, { type: file.type });
      validFiles.push(newFile);
    });

    if (errors.length > 0) {
      alert(errors.join('\n'));
    }

    if (validFiles.length > 0) {
      setUploadedFiles(prev => ({
        ...prev,
        [fileType]: [...(prev[fileType] || []), ...validFiles]
      }));
    }
  };

  const removeFile = (fileType, index) => {
    setUploadedFiles(prev => ({
      ...prev,
      [fileType]: prev[fileType].filter((_, i) => i !== index)
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    try {
      // Validate required fields
      if (!formData.name || !formData.email || !formData.password || !formData.confirmPassword) {
        alert('Please fill in all required fields (Name, Email, and Password)');
        return;
      }

      // Validate password match
      if (formData.password !== formData.confirmPassword) {
        alert('Passwords do not match. Please check and try again.');
        return;
      }

      // Validate password strength
      if (formData.password.length < 6) {
        alert('Password must be at least 6 characters long.');
        return;
      }

      // Validate permissions
      if (!formData.permissions || formData.permissions.length === 0) {
        alert('Please select at least one permission for the sub-admin.');
        return;
      }

      // Import and use sub-admin service
      const { createSubAdmin } = await import('../services/subAdminService');

      // Get current admin user for createdById
      const { getCurrentUser } = await import('../services/authService');
      const currentAdmin = getCurrentUser();

      // Prepare sub-admin data
      const subAdminData = {
        name: formData.name,
        email: formData.email,
        password: formData.password,
        onboardingDate: formData.subAdminOnboardingDate || new Date().toISOString().split('T')[0],
        address: formData.address || '',
        country: formData.country || 'India',
        state: formData.state || '',
        city: formData.city || '',
        username: formData.username || formData.email.split('@')[0],
        panNumber: formData.panNumber || '',
        termOfWork: formData.termOfWork || 'Full-time',
        files: uploadedFiles,
        status: formData.status || 'Active',
        isActive: formData.isActive !== undefined ? formData.isActive : true,
        mustChangePassword: formData.mustChangePassword !== undefined ? formData.mustChangePassword : true,
        permissions: formData.permissions || [],
        createdById: currentAdmin?.id || null
      };

      // Create the sub-admin
      const newSubAdmin = await createSubAdmin(subAdminData);

      if (newSubAdmin) {
        // Show success message with credentials
        const credentialsMessage = `Sub-admin created successfully!

📧 Login Credentials:
Email: ${formData.email}
Password: ${formData.password}

⚠️ Important Notes:
• Sub-admin must change password on first login
• Share these credentials securely with the sub-admin
• Keep a record of these credentials for your reference

The sub-admin can now login at: ${window.location.origin}/login`;

        alert(credentialsMessage);

        // Refresh the sub-admins list
        const { getAllSubAdmins } = await import('../services/subAdminService');
        const updatedSubAdmins = await getAllSubAdmins();
        setSubAdmins(updatedSubAdmins || []);

        // Close form and reset
        setShowAddForm(false);
        resetForm();
      } else {
        alert('Failed to create sub-admin. Please try again.');
      }
    } catch (error) {
      console.error('❌ Error creating sub-admin:', error);
      alert('Error creating sub-admin: ' + error.message);
    }
  };

  const resetForm = () => {
    setFormData({
      subAdminOnboardingDate: '',
      name: '',
      email: '',
      password: '',
      confirmPassword: '',
      address: '',
      country: '',
      state: '',
      city: '',
      username: '',
      panNumber: '',
      termOfWork: '',
      role: '',
      permissions: [],
      status: 'Active',
      isActive: true,
      mustChangePassword: true
    });
    setUploadedFiles({
      tdsFile: [],
      ndaFile: [],
      employmentAgreement: [],
      panCard: []
    });
  };

  // Delete sub-admin (actually deactivate)
  const handleDelete = async (subAdminId) => {
    const subAdmin = subAdmins.find(sa => sa.id === subAdminId);
    const confirmMessage = `Are you sure you want to deactivate this sub-admin?

👤 Sub-Admin: ${subAdmin?.name || 'Unknown'}
📧 Email: ${subAdmin?.email || 'Unknown'}

⚠️ Note: This will deactivate the account and prevent login, but preserve all related data (customers, orders, etc.) for audit purposes.

The sub-admin will no longer be able to access the system.`;

    if (!confirm(confirmMessage)) {
      return;
    }

    try {
      // Import and use sub-admin service
      const { deleteSubAdmin } = await import('../services/subAdminService');
      await deleteSubAdmin(subAdminId);

      alert('Sub-admin deactivated successfully! The account has been disabled and the user can no longer login.');

      // Refresh the sub-admins list
      const { getAllSubAdmins } = await import('../services/subAdminService');
      const updatedSubAdmins = await getAllSubAdmins();
      setSubAdmins(updatedSubAdmins || []);
    } catch (error) {
      console.error('❌ Error deactivating sub-admin:', error);
      alert('Error deactivating sub-admin: ' + error.message);
    }
  };

  // DataTable columns
  const columns = [
    {
      key: 'name',
      label: 'Name',
      sortable: true,
      filterable: true
    },
    {
      key: 'email',
      label: 'Email',
      sortable: true,
      filterable: true
    },
    {
      key: 'username',
      label: 'Username',
      sortable: true,
      filterable: true
    },
    {
      key: 'termOfWork',
      label: 'Term of Work',
      sortable: true,
      filterable: true,
      render: (value) => (
        <span className={`px-2 py-1 text-xs font-medium rounded-full ${
          value === 'Full-time' ? 'bg-green-100 text-green-800' :
          value === 'Part-time' ? 'bg-blue-100 text-blue-800' :
          value === 'Contract' ? 'bg-yellow-100 text-yellow-800' :
          value === 'Internship' ? 'bg-purple-100 text-purple-800' :
          'bg-gray-100 text-gray-800'
        }`}>
          {value}
        </span>
      )
    },
    {
      key: 'subAdminOnboardingDate',
      label: 'Onboarding Date',
      sortable: true,
      filterable: false
    },
    {
      key: 'city',
      label: 'Location',
      sortable: true,
      filterable: true,
      render: (value, row) => `${row.city}, ${row.state}`
    },
    {
      key: 'status',
      label: 'Status',
      sortable: true,
      filterable: true,
      render: (value) => (
        <span className={`px-2 py-1 text-xs font-medium rounded-full ${
          value === 'Active' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
        }`}>
          {value}
        </span>
      )
    },
    {
      key: 'actions',
      label: 'Actions',
      sortable: false,
      filterable: false,
      render: (value, row) => (
        <div className="flex space-x-2">
          <button
            onClick={() => navigate(`/sub-admins/${row.id}`)}
            className="text-blue-600 hover:text-blue-900 p-1 hover:bg-blue-50 rounded"
            title="View Sub-admin"
          >
            <EyeIcon className="h-4 w-4" />
          </button>
          <button
            onClick={() => navigate(`/sub-admins/${row.id}/edit`)}
            className="text-green-600 hover:text-green-900 p-1 hover:bg-green-50 rounded"
            title="Edit Sub-admin"
          >
            <PencilIcon className="h-4 w-4" />
          </button>
          <button
            onClick={() => handleDelete(row.id)}
            className="text-red-600 hover:text-red-900 p-1 hover:bg-red-50 rounded"
            title="Delete Sub-admin"
          >
            <TrashIcon className="h-4 w-4" />
          </button>
        </div>
      )
    }
  ];

  // Debug: Add error boundary and loading state
  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex justify-between items-center border-b border-gray-200 pb-4">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Sub-admins</h1>
            <p className="mt-2 text-gray-600">Loading...</p>
          </div>
        </div>
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex justify-between items-center border-b border-gray-200 pb-4">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Sub-admins</h1>
          <p className="mt-2 text-gray-600">Manage administrative users and permissions</p>
        </div>
        <div className="flex space-x-3">
          <button
            onClick={() => setShowInactive(!showInactive)}
            className={`btn-secondary flex items-center ${showInactive ? 'bg-orange-100 text-orange-700 border-orange-300' : ''}`}
          >
            <EyeIcon className="h-5 w-5 mr-2" />
            {showInactive ? 'Hide Inactive' : 'Show Inactive'}
          </button>
          <button
            onClick={() => setShowAddForm(true)}
            className="btn-primary flex items-center"
          >
            <PlusIcon className="h-5 w-5 mr-2" />
            Add New Sub-admin
          </button>
        </div>
      </div>



      {/* Sub-admins Table */}
      {Array.isArray(subAdmins) && subAdmins.length > 0 ? (
        <div className="bg-white shadow rounded-lg overflow-hidden">
          <div className="px-4 py-5 sm:p-6">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-medium text-gray-900">Sub-admins ({subAdmins.length})</h3>
              <button
                onClick={() => setShowAddForm(true)}
                className="btn-primary flex items-center text-sm"
              >
                <PlusIcon className="h-4 w-4 mr-2" />
                Add Sub-admin
              </button>
            </div>
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Email</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Username</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Term of Work</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {subAdmins.map((admin) => (
                    <tr key={admin.id}>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{admin.name}</td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{admin.email}</td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{admin.username || 'N/A'}</td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        <span className={`px-2 py-1 text-xs font-medium rounded-full ${
                          admin.termOfWork === 'Full-time' ? 'bg-green-100 text-green-800' :
                          admin.termOfWork === 'Part-time' ? 'bg-blue-100 text-blue-800' :
                          admin.termOfWork === 'Contract' ? 'bg-yellow-100 text-yellow-800' :
                          'bg-gray-100 text-gray-800'
                        }`}>
                          {admin.termOfWork || 'N/A'}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        <span className={`px-2 py-1 text-xs font-medium rounded-full ${
                          admin.status === 'Active' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                        }`}>
                          {admin.status}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <div className="flex space-x-2">
                          <button
                            onClick={() => navigate(`/sub-admins/${admin.id}`)}
                            className="text-blue-600 hover:text-blue-900 p-1 hover:bg-blue-50 rounded"
                            title="View Sub-admin"
                          >
                            <EyeIcon className="h-4 w-4" />
                          </button>
                          <button
                            onClick={() => navigate(`/sub-admins/${admin.id}/edit`)}
                            className="text-green-600 hover:text-green-900 p-1 hover:bg-green-50 rounded"
                            title="Edit Sub-admin"
                          >
                            <PencilIcon className="h-4 w-4" />
                          </button>
                          <button
                            onClick={() => handleDelete(admin.id)}
                            className="text-red-600 hover:text-red-900 p-1 hover:bg-red-50 rounded"
                            title="Delete Sub-admin"
                          >
                            <TrashIcon className="h-4 w-4" />
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      ) : (
        <div className="text-center py-12">
          <h3 className="text-lg font-medium text-gray-900">No Sub-admins Found</h3>
          <p className="text-gray-500 mt-2">Get started by adding your first sub-admin.</p>
          <button
            onClick={() => setShowAddForm(true)}
            className="mt-4 btn-primary flex items-center mx-auto"
          >
            <PlusIcon className="h-5 w-5 mr-2" />
            Add New Sub-admin
          </button>
        </div>
      )}

      {/* Permission Summary */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="card">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Permission Overview</h3>
          <div className="space-y-3">
            {availablePermissions.map((permission, index) => {
              const adminCount = subAdmins.filter(admin =>
                Array.isArray(admin.permissions) && admin.permissions.includes(permission.value) && admin.status === 'Active'
              ).length;
              return (
                <div key={index} className="flex justify-between items-center p-2 rounded hover:bg-gray-50">
                  <div>
                    <span className="text-sm font-medium text-gray-900">{permission.label}</span>
                    <p className="text-xs text-gray-500">{permission.description}</p>
                  </div>
                  <span className="text-sm font-medium text-blue-600">{adminCount} admin{adminCount !== 1 ? 's' : ''}</span>
                </div>
              );
            })}
          </div>
        </div>

        <div className="card">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Quick Stats</h3>
          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600">Total Sub-admins:</span>
              <span className="text-lg font-semibold text-gray-900">{subAdmins.length}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600">Active Sub-admins:</span>
              <span className="text-lg font-semibold text-green-600">
                {subAdmins.filter(admin => admin.status === 'Active').length}
              </span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600">Inactive Sub-admins:</span>
              <span className="text-lg font-semibold text-red-600">
                {subAdmins.filter(admin => admin.status === 'Inactive').length}
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* Add Sub-admin Modal */}
      {showAddForm && (
        <div className="fixed inset-0 z-50 overflow-y-auto">
          <div className="flex items-center justify-center min-h-screen px-4">
            <div className="fixed inset-0 bg-black opacity-50" onClick={() => setShowAddForm(false)}></div>
            <div className="relative bg-white rounded-lg max-w-6xl w-full max-h-[90vh] overflow-y-auto">
              <form onSubmit={handleSubmit} className="p-6">
                <div className="flex items-center justify-between mb-6">
                  <h3 className="text-lg font-medium text-gray-900">Create New Sub-admin</h3>
                  <button
                    type="button"
                    onClick={() => {
                      setShowAddForm(false);
                      resetForm();
                    }}
                    className="text-gray-400 hover:text-gray-600"
                  >
                    ×
                  </button>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {/* Personal Information */}
                  <div className="space-y-4">
                    <h4 className="text-md font-medium text-gray-900">Personal Information</h4>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Sub-admin Onboarding Date *
                      </label>
                      <input
                        type="date"
                        name="subAdminOnboardingDate"
                        value={formData.subAdminOnboardingDate}
                        onChange={handleInputChange}
                        required
                        className="input-field"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Name *
                      </label>
                      <input
                        type="text"
                        name="name"
                        value={formData.name}
                        onChange={handleInputChange}
                        required
                        className="input-field"
                        placeholder="Enter full name"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Email ID *
                      </label>
                      <input
                        type="email"
                        name="email"
                        value={formData.email}
                        onChange={handleInputChange}
                        required
                        className="input-field"
                        placeholder="Enter email address"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Password *
                      </label>
                      <input
                        type="password"
                        name="password"
                        value={formData.password}
                        onChange={handleInputChange}
                        required
                        minLength="6"
                        className="input-field"
                        placeholder="Enter temporary password (min 6 characters)"
                      />
                      <p className="text-xs text-gray-500 mt-1">
                        Sub-admin will be required to change this password on first login
                      </p>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Confirm Password *
                      </label>
                      <input
                        type="password"
                        name="confirmPassword"
                        value={formData.confirmPassword}
                        onChange={handleInputChange}
                        required
                        minLength="6"
                        className="input-field"
                        placeholder="Confirm password"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Address *
                      </label>
                      <textarea
                        name="address"
                        value={formData.address}
                        onChange={handleInputChange}
                        required
                        rows={3}
                        className="input-field"
                        placeholder="Enter complete address"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Country
                      </label>
                      <select
                        name="country"
                        value={formData.country}
                        onChange={handleInputChange}
                        className="input-field"
                        disabled={locationLoading}
                      >
                        <option value="">
                          {locationLoading ? 'Loading countries...' : 'Select Country'}
                        </option>
                        {countries.map(country => (
                          <option key={country.iso2} value={country.name}>
                            {country.name}
                          </option>
                        ))}
                      </select>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        State
                      </label>
                      <select
                        name="state"
                        value={formData.state}
                        onChange={handleInputChange}
                        className="input-field"
                        disabled={!formData.country || locationLoading}
                      >
                        <option value="">
                          {!formData.country ? 'Select Country First' :
                           locationLoading ? 'Loading states...' : 'Select State'}
                        </option>
                        {states.map(state => (
                          <option key={state.iso2} value={state.name}>
                            {state.name}
                          </option>
                        ))}
                      </select>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        City
                      </label>
                      <select
                        name="city"
                        value={formData.city}
                        onChange={handleInputChange}
                        className="input-field"
                        disabled={!formData.state || locationLoading}
                      >
                        <option value="">
                          {!formData.state ? 'Select State First' :
                           locationLoading ? 'Loading cities...' : 'Select City'}
                        </option>
                        {cities.map(city => (
                          <option key={city.id || city.name} value={city.name}>
                            {city.name}
                          </option>
                        ))}
                      </select>
                    </div>
                  </div>

                  {/* Professional Information */}
                  <div className="space-y-4">
                    <h4 className="text-md font-medium text-gray-900">Professional Information</h4>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Username *
                      </label>
                      <input
                        type="text"
                        name="username"
                        value={formData.username}
                        onChange={handleInputChange}
                        required
                        className="input-field"
                        placeholder="Enter username"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        PAN Number
                      </label>
                      <input
                        type="text"
                        name="panNumber"
                        value={formData.panNumber}
                        onChange={handleInputChange}
                        className="input-field"
                        placeholder="Enter PAN number"
                        pattern="[A-Z]{5}[0-9]{4}[A-Z]{1}"
                        title="Please enter a valid PAN number (e.g., **********)"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Term of Work *
                      </label>
                      <select
                        name="termOfWork"
                        value={formData.termOfWork}
                        onChange={handleInputChange}
                        required
                        className="input-field"
                      >
                        <option value="">Select term of work</option>
                        <option value="Part-time">Part-time</option>
                        <option value="Full-time">Full-time</option>
                        <option value="Contract">Contract</option>
                        <option value="Internship">Internship</option>
                      </select>
                    </div>
                  </div>
                </div>

                {/* Permissions Section */}
                <div className="mt-8">
                  <h4 className="text-md font-medium text-gray-900 mb-6">Permission Assignment</h4>
                  <div className="bg-gray-50 rounded-lg p-6">
                    <p className="text-sm text-gray-600 mb-4">
                      Select the modules and features this sub-admin can access. Admins have full access to all features.
                    </p>

                    <div className="grid grid-cols-1 gap-4">
                      {availablePermissions.map((permission) => (
                        <div key={permission.value} className="flex items-start space-x-3 p-3 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                          <input
                            type="checkbox"
                            id={`permission-${permission.value}`}
                            checked={formData.permissions.includes(permission.value)}
                            onChange={(e) => {
                              const updatedPermissions = e.target.checked
                                ? [...formData.permissions, permission.value]
                                : formData.permissions.filter(p => p !== permission.value);
                              setFormData(prev => ({
                                ...prev,
                                permissions: updatedPermissions
                              }));
                            }}
                            className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded mt-1"
                          />
                          <div className="flex-1">
                            <label htmlFor={`permission-${permission.value}`} className="text-sm font-medium text-gray-900 cursor-pointer">
                              {permission.label}
                            </label>
                            <p className="text-xs text-gray-500 mt-1">{permission.description}</p>
                          </div>
                        </div>
                      ))}
                    </div>

                    {/* Quick Permission Presets */}
                    <div className="mt-6 pt-4 border-t border-gray-200">
                      <p className="text-sm font-medium text-gray-700 mb-3">Quick Presets:</p>
                      <div className="flex flex-wrap gap-2">
                        <button
                          type="button"
                          onClick={() => setFormData(prev => ({ ...prev, permissions: availablePermissions.map(p => p.value) }))}
                          className="px-3 py-1 text-xs bg-blue-100 text-blue-700 rounded-full hover:bg-blue-200 transition-colors"
                        >
                          Select All
                        </button>
                        <button
                          type="button"
                          onClick={() => setFormData(prev => ({ ...prev, permissions: [] }))}
                          className="px-3 py-1 text-xs bg-gray-100 text-gray-700 rounded-full hover:bg-gray-200 transition-colors"
                        >
                          Clear All
                        </button>
                        <button
                          type="button"
                          onClick={() => setFormData(prev => ({ ...prev, permissions: ['MANAGE_CUSTOMERS', 'MANAGE_VENDORS', 'MANAGE_ORDERS'] }))}
                          className="px-3 py-1 text-xs bg-green-100 text-green-700 rounded-full hover:bg-green-200 transition-colors"
                        >
                          Basic Access
                        </button>
                        <button
                          type="button"
                          onClick={() => setFormData(prev => ({ ...prev, permissions: ['MANAGE_CUSTOMERS', 'MANAGE_VENDORS', 'MANAGE_ORDERS', 'VIEW_ANALYTICS', 'VIEW_REPORTS'] }))}
                          className="px-3 py-1 text-xs bg-yellow-100 text-yellow-700 rounded-full hover:bg-yellow-200 transition-colors"
                        >
                          Standard Access
                        </button>
                      </div>
                    </div>

                    {/* Selected Permissions Summary */}
                    {formData.permissions.length > 0 && (
                      <div className="mt-4 p-3 bg-blue-50 rounded-lg">
                        <p className="text-sm font-medium text-blue-900 mb-2">
                          Selected Permissions ({formData.permissions.length}):
                        </p>
                        <div className="flex flex-wrap gap-1">
                          {formData.permissions.map((permissionValue) => {
                            const permission = availablePermissions.find(p => p.value === permissionValue);
                            return (
                              <span
                                key={permissionValue}
                                className="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-blue-100 text-blue-800"
                              >
                                {permission ? permission.label : permissionValue}
                              </span>
                            );
                          })}
                        </div>
                      </div>
                    )}
                  </div>
                </div>

                {/* File Upload Section */}
                <div className="mt-8">
                  <h4 className="text-md font-medium text-gray-900 mb-6">File Upload Section</h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">

                    {/* TDS File Upload - S3 Integration */}
                    <div>
                      <S3FileUpload
                        module="sub-admins"
                        recordId={tempSubAdminId}
                        fileType="tds"
                        allowedTypes="documents"
                        multiple={true}
                        maxFiles={3}
                        label="TDS File"
                        description="Upload TDS documents (PDF, Office files)"
                        onFilesUploaded={(files) => {
                          setUploadedFiles(prev => ({
                            ...prev,
                            tdsFile: files
                          }));
                        }}
                        onFileDeleted={(deletedFile, remainingFiles) => {
                          setUploadedFiles(prev => ({
                            ...prev,
                            tdsFile: remainingFiles
                          }));
                        }}
                        existingFiles={uploadedFiles.tdsFile || []}
                      />
                    </div>

                    {/* NDA File Upload - S3 Integration */}
                    <div>
                      <S3FileUpload
                        module="sub-admins"
                        recordId={tempSubAdminId}
                        fileType="nda"
                        allowedTypes="documents"
                        multiple={true}
                        maxFiles={3}
                        label="NDA (Required) *"
                        description="Upload NDA documents (PDF, Office files) - Required"
                        onFilesUploaded={(files) => {
                          setUploadedFiles(prev => ({
                            ...prev,
                            ndaFile: files
                          }));
                        }}
                        onFileDeleted={(deletedFile, remainingFiles) => {
                          setUploadedFiles(prev => ({
                            ...prev,
                            ndaFile: remainingFiles
                          }));
                        }}
                        existingFiles={uploadedFiles.ndaFile || []}
                      />
                    </div>

                    {/* Employment Agreement Upload - S3 Integration */}
                    <div>
                      <S3FileUpload
                        module="sub-admins"
                        recordId={tempSubAdminId}
                        fileType="employment"
                        allowedTypes="documents"
                        multiple={true}
                        maxFiles={3}
                        label="Employment Agreement (Required) *"
                        description="Upload employment agreement documents (PDF, Office files) - Required"
                        onFilesUploaded={(files) => {
                          setUploadedFiles(prev => ({
                            ...prev,
                            employmentAgreement: files
                          }));
                        }}
                        onFileDeleted={(deletedFile, remainingFiles) => {
                          setUploadedFiles(prev => ({
                            ...prev,
                            employmentAgreement: remainingFiles
                          }));
                        }}
                        existingFiles={uploadedFiles.employmentAgreement || []}
                      />
                    </div>

                    {/* PAN Card Upload - S3 Integration */}
                    <div>
                      <S3FileUpload
                        module="sub-admins"
                        recordId={tempSubAdminId}
                        fileType="pancard"
                        allowedTypes="all"
                        multiple={true}
                        maxFiles={2}
                        label="PAN Card"
                        description="Upload PAN card documents (PDF, Images)"
                        onFilesUploaded={(files) => {
                          setUploadedFiles(prev => ({
                            ...prev,
                            panCard: files
                          }));
                        }}
                        onFileDeleted={(deletedFile, remainingFiles) => {
                          setUploadedFiles(prev => ({
                            ...prev,
                            panCard: remainingFiles
                          }));
                        }}
                        existingFiles={uploadedFiles.panCard || []}
                      />
                    </div>
                  </div>

                  <p className="text-xs text-gray-500 mt-4">
                    Max file size: 10MB. Supported formats: PDF, Images, MS Office documents.
                  </p>
                </div>

                {/* Form Actions */}
                <div className="flex justify-end space-x-3 mt-8 pt-6 border-t border-gray-200">
                  <button
                    type="button"
                    onClick={() => {
                      setShowAddForm(false);
                      resetForm();
                    }}
                    className="btn-secondary"
                  >
                    Cancel
                  </button>
                  <button
                    type="submit"
                    className="btn-primary"
                  >
                    Create Sub-admin
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default SubAdmins;
