import { sql } from '../config/database.js';
import { createSubAdminUser } from './authService.js';

// Get all sub-admins
export const getAllSubAdmins = async (includeInactive = false) => {
  try {
    let subAdmins;

    if (includeInactive) {
      // Get all sub-admins including inactive ones
      subAdmins = await sql`
        SELECT
          id,
          name,
          email,
          "isActive",
          "createdAt",
          "updatedAt",
          "createdById",
          address,
          city,
          state,
          country,
          "panNumber",
          "subAdminOnboardingDate",
          "termOfWork",
          username,
          role
        FROM users
        WHERE role = 'SUB_ADMIN'
        ORDER BY "isActive" DESC, "createdAt" DESC
      `;
    } else {
      // Get only active sub-admins
      subAdmins = await sql`
        SELECT
          id,
          name,
          email,
          "isActive",
          "createdAt",
          "updatedAt",
          "createdById",
          address,
          city,
          state,
          country,
          "panNumber",
          "subAdminOnboardingDate",
          "termOfWork",
          username,
          role
        FROM users
        WHERE role = 'SUB_ADMIN' AND "isActive" = true
        ORDER BY "createdAt" DESC
      `;
    }

    // Get permissions for each sub-admin
    const { getUserPermissions } = await import('./userPermissionsService.js');

    // Transform data to match UI expectations
    const transformedSubAdmins = await Promise.all(subAdmins.map(async (admin) => {
      let permissions = [];
      try {
        permissions = await getUserPermissions(admin.id);
      } catch (error) {
        console.warn('⚠️ Could not fetch permissions for user:', admin.id, error.message);
      }

      return {
        id: admin.id,
        name: admin.name,
        email: admin.email,
        username: admin.username,
        address: admin.address,
        country: admin.country,
        state: admin.state,
        city: admin.city,
        panNumber: admin.panNumber,
        termOfWork: admin.termOfWork,
        subAdminOnboardingDate: admin.subAdminOnboardingDate ? new Date(admin.subAdminOnboardingDate).toISOString().split('T')[0] : '',
        status: admin.isActive ? 'Active' : 'Inactive',
        isActive: admin.isActive,
        files: {}, // Default empty object for files
        permissions: permissions, // Actual permissions from database
        role: admin.role,
        lastLogin: 'N/A', // Placeholder for last login
        createdDate: admin.createdAt ? new Date(admin.createdAt).toISOString().split('T')[0] : '',
        created_at: admin.createdAt ? new Date(admin.createdAt).toISOString().split('T')[0] : '',
        updated_at: admin.updatedAt ? new Date(admin.updatedAt).toISOString().split('T')[0] : ''
      };
    }));

    return transformedSubAdmins;
  } catch (error) {
    console.error('Error fetching sub-admins:', error);
    throw error;
  }
};



// Get sub-admin by ID
export const getSubAdminById = async (id) => {
  try {
    console.log('🔍 Getting sub-admin by ID:', id);

    // Get from users table
    const subAdmin = await sql`
      SELECT
        id,
        name,
        email,
        "isActive",
        "createdAt",
        "updatedAt",
        "createdById",
        address,
        city,
        state,
        country,
        "panNumber",
        "subAdminOnboardingDate",
        "termOfWork",
        username,
        role
      FROM users
      WHERE id = ${id} AND role = 'SUB_ADMIN'
    `;

    const adminData = subAdmin[0];

    // If found in database, return it
    if (adminData) {
      console.log('✅ Sub-admin found in database:', adminData);
      return {
        ...adminData,
        onboarding_date: adminData.onboarding_date ? new Date(adminData.onboarding_date).toISOString().split('T')[0] : '',
        created_at: adminData.created_at ? new Date(adminData.created_at).toISOString().split('T')[0] : '',
        updated_at: adminData.updated_at ? new Date(adminData.updated_at).toISOString().split('T')[0] : ''
      };
    }

    console.log('❌ Sub-admin not found:', id);
    return null;
  } catch (error) {
    console.error('Error fetching sub-admin by ID:', error);
    throw error;
  }
};

// Create new sub-admin
export const createSubAdmin = async (subAdminData) => {
  try {
    console.log('🔄 Creating sub-admin with data:', subAdminData);

    // Use the authentication service to create the user
    const result = await createSubAdminUser(subAdminData, subAdminData.createdById);

    if (!result.success) {
      throw new Error(result.message || 'Failed to create sub-admin user');
    }

    console.log('✅ Sub-admin user created successfully:', result.user);
    return result.user;

  } catch (error) {
    console.error('❌ Error creating sub-admin:', error);
    throw error;
  }
};

// Update sub-admin
export const updateSubAdmin = async (id, subAdminData) => {
  try {
    console.log('🔄 Updating sub-admin:', id, subAdminData);

    const {
      name,
      email,
      address,
      country,
      state,
      city,
      username,
      panNumber,
      termOfWork,
      isActive
    } = subAdminData;

    const subAdmin = await sql`
      UPDATE users SET
        name = ${name},
        email = ${email},
        address = ${address},
        country = ${country},
        state = ${state},
        city = ${city},
        username = ${username},
        "panNumber" = ${panNumber},
        "termOfWork" = ${termOfWork},
        "isActive" = ${isActive !== undefined ? isActive : true},
        "updatedAt" = NOW()
      WHERE id = ${id} AND role = 'SUB_ADMIN'
      RETURNING id, name, email, "isActive", "createdAt", "updatedAt"
    `;

    console.log('✅ Sub-admin updated successfully');
    return subAdmin[0];
  } catch (error) {
    console.error('❌ Error updating sub-admin:', error);
    throw error;
  }
};

// Delete sub-admin (actually deactivate due to foreign key constraints)
export const deleteSubAdmin = async (id) => {
  try {
    console.log('🗑️ Deactivating sub-admin with ID:', id);

    // Instead of deleting, deactivate the user to preserve foreign key relationships
    const result = await sql`
      UPDATE users
      SET
        "isActive" = false,
        "updatedAt" = NOW(),
        email = CONCAT(email, '_DELETED_', EXTRACT(EPOCH FROM NOW())::text)
      WHERE id = ${id} AND role = 'SUB_ADMIN'
      RETURNING id, name, email, "isActive"
    `;

    if (result.length === 0) {
      throw new Error('Sub-admin not found or already deleted');
    }

    console.log('✅ Sub-admin deactivated successfully:', result[0]);
    return true;
  } catch (error) {
    console.error('❌ Error deactivating sub-admin:', error);
    throw error;
  }
};

// Get sub-admin statistics
export const getSubAdminStats = async () => {
  try {
    const stats = await sql`
      SELECT 
        COUNT(*) as total_sub_admins,
        COUNT(CASE WHEN status = 'Active' THEN 1 END) as active_sub_admins,
        COUNT(CASE WHEN status = 'Inactive' THEN 1 END) as inactive_sub_admins,
        COUNT(CASE WHEN term_of_work = 'Full-time' THEN 1 END) as full_time_admins,
        COUNT(CASE WHEN term_of_work = 'Part-time' THEN 1 END) as part_time_admins,
        COUNT(CASE WHEN term_of_work = 'Contract' THEN 1 END) as contract_admins,
        COUNT(CASE WHEN term_of_work = 'Internship' THEN 1 END) as internship_admins
      FROM sub_admins
    `;
    return stats[0];
  } catch (error) {
    console.error('Error fetching sub-admin stats:', error);
    throw error;
  }
};

// Search sub-admins
export const searchSubAdmins = async (searchTerm) => {
  try {
    const subAdmins = await sql`
      SELECT 
        id,
        name,
        email,
        onboarding_date,
        address,
        country,
        state,
        city,
        username,
        pan_number,
        term_of_work,
        files,
        status,
        created_at,
        updated_at
      FROM sub_admins 
      WHERE 
        name ILIKE ${`%${searchTerm}%`} OR
        email ILIKE ${`%${searchTerm}%`} OR
        username ILIKE ${`%${searchTerm}%`} OR
        pan_number ILIKE ${`%${searchTerm}%`} OR
        term_of_work ILIKE ${`%${searchTerm}%`}
      ORDER BY created_at DESC
    `;
    return subAdmins;
  } catch (error) {
    console.error('Error searching sub-admins:', error);
    throw error;
  }
};

// Check if email exists
export const checkEmailExists = async (email, excludeId = null) => {
  try {
    let query = sql`SELECT id FROM sub_admins WHERE email = ${email}`;
    
    if (excludeId) {
      query = sql`SELECT id FROM sub_admins WHERE email = ${email} AND id != ${excludeId}`;
    }
    
    const result = await query;
    return result.length > 0;
  } catch (error) {
    console.error('Error checking email exists:', error);
    throw error;
  }
};

// Check if username exists
export const checkUsernameExists = async (username, excludeId = null) => {
  try {
    let query = sql`SELECT id FROM sub_admins WHERE username = ${username}`;
    
    if (excludeId) {
      query = sql`SELECT id FROM sub_admins WHERE username = ${username} AND id != ${excludeId}`;
    }
    
    const result = await query;
    return result.length > 0;
  } catch (error) {
    console.error('Error checking username exists:', error);
    throw error;
  }
};
