/**
 * Debug Export Functionality
 * Helps diagnose PDF export issues
 */

/**
 * Check if all required libraries are loaded
 */
export const checkExportDependencies = () => {
  console.log('🔍 Checking Export Dependencies...');
  console.log('');

  const results = {
    jsPDF: false,
    jsPDFAutoTable: false,
    xlsx: false,
    fileSaver: false
  };

  // Check jsPDF
  try {
    if (typeof window !== 'undefined') {
      // Try to import jsPDF
      import('jspdf').then(jsPDF => {
        console.log('✅ jsPDF: Available');
        results.jsPDF = true;
      }).catch(err => {
        console.log('❌ jsPDF: Not available', err);
      });

      // Check jsPDF autoTable
      import('jspdf-autotable').then(() => {
        console.log('✅ jsPDF AutoTable: Available');
        results.jsPDFAutoTable = true;
      }).catch(err => {
        console.log('❌ jsPDF AutoTable: Not available', err);
      });

      // Check XLSX
      import('xlsx').then(() => {
        console.log('✅ XLSX: Available');
        results.xlsx = true;
      }).catch(err => {
        console.log('❌ XLSX: Not available', err);
      });

      // Check file-saver
      import('file-saver').then(() => {
        console.log('✅ File Saver: Available');
        results.fileSaver = true;
      }).catch(err => {
        console.log('❌ File Saver: Not available', err);
      });
    }
  } catch (error) {
    console.error('💥 Error checking dependencies:', error);
  }

  return results;
};

/**
 * Test PDF generation directly
 */
export const testPDFGeneration = () => {
  console.log('🧪 Testing PDF Generation...');
  console.log('');

  try {
    // Import jsPDF dynamically
    import('jspdf').then(({ default: jsPDF }) => {
      import('jspdf-autotable').then(() => {
        console.log('📄 Creating test PDF...');

        const doc = new jsPDF();
        
        // Add title
        doc.setFontSize(16);
        doc.text('Test PDF Export', 14, 22);

        // Add timestamp
        doc.setFontSize(10);
        doc.text(`Generated on: ${new Date().toLocaleString()}`, 14, 32);

        // Test data
        const headers = ['Name', 'Description', 'Status'];
        const rows = [
          ['Test Item 1', 'This is a test description', 'Active'],
          ['Test Item 2', 'Another test description', 'Inactive'],
          ['Test Item 3', 'Third test description', 'Active']
        ];

        // Add table
        doc.autoTable({
          head: [headers],
          body: rows,
          startY: 40,
          styles: {
            fontSize: 9,
            cellPadding: 3,
            lineColor: [0, 0, 0],
            lineWidth: 0.1,
            textColor: [0, 0, 0],
          },
          headStyles: {
            fillColor: [59, 130, 246],
            textColor: [255, 255, 255],
            fontStyle: 'bold',
            lineColor: [0, 0, 0],
            lineWidth: 0.2,
          },
          bodyStyles: {
            lineColor: [0, 0, 0],
            lineWidth: 0.1,
          },
          alternateRowStyles: {
            fillColor: [248, 250, 252],
          },
          tableLineColor: [0, 0, 0],
          tableLineWidth: 0.2,
          margin: { top: 40, left: 14, right: 14 },
        });

        // Save the PDF
        const fileName = `test_export_${new Date().toISOString().split('T')[0]}.pdf`;
        doc.save(fileName);

        console.log('✅ Test PDF generated successfully!');
        console.log(`📁 File name: ${fileName}`);

        return { success: true, fileName };

      }).catch(error => {
        console.error('❌ jsPDF AutoTable import failed:', error);
        return { success: false, error: 'AutoTable not available' };
      });

    }).catch(error => {
      console.error('❌ jsPDF import failed:', error);
      return { success: false, error: 'jsPDF not available' };
    });

  } catch (error) {
    console.error('💥 PDF generation test failed:', error);
    return { success: false, error: error.message };
  }
};

/**
 * Debug export modal functionality
 */
export const debugExportModal = () => {
  console.log('🔍 Debugging Export Modal...');
  console.log('');

  try {
    // Check if we're on the Type of Work page
    const currentPath = window.location.pathname;
    console.log(`📍 Current page: ${currentPath}`);

    // Look for export button
    const exportButtons = document.querySelectorAll('button');
    let exportButton = null;

    exportButtons.forEach(button => {
      if (button.textContent.includes('Export')) {
        exportButton = button;
      }
    });

    if (exportButton) {
      console.log('✅ Export button found');
      console.log(`🎯 Button text: "${exportButton.textContent}"`);
      console.log(`🎨 Button classes: ${exportButton.className}`);
      console.log(`🔧 Button disabled: ${exportButton.disabled}`);

      // Check if button has click handler
      const hasClickHandler = exportButton.onclick !== null || 
                             exportButton.getAttribute('onclick') !== null;
      console.log(`🖱️ Has click handler: ${hasClickHandler}`);

      // Try to click the button programmatically
      console.log('🖱️ Attempting to click export button...');
      exportButton.click();

      // Check for modal after a short delay
      setTimeout(() => {
        const modals = document.querySelectorAll('[class*="fixed"], [class*="modal"], [class*="z-50"]');
        console.log(`🔧 Modals found after click: ${modals.length}`);

        modals.forEach((modal, index) => {
          console.log(`   Modal ${index + 1}: ${modal.className}`);
        });

        // Look for export modal specifically
        const exportModal = document.querySelector('[class*="Export"]') || 
                           document.querySelector('h3[class*="Export"]') ||
                           Array.from(document.querySelectorAll('h3')).find(h3 => 
                             h3.textContent.includes('Export')
                           );

        if (exportModal) {
          console.log('✅ Export modal opened successfully');
        } else {
          console.log('❌ Export modal did not open');
        }
      }, 500);

    } else {
      console.log('❌ Export button not found');
      console.log('🔍 Available buttons:');
      exportButtons.forEach((button, index) => {
        console.log(`   Button ${index + 1}: "${button.textContent}"`);
      });
    }

    return { success: true, exportButtonFound: !!exportButton };

  } catch (error) {
    console.error('💥 Export modal debug failed:', error);
    return { success: false, error: error.message };
  }
};

/**
 * Check DataTable component props
 */
export const checkDataTableProps = () => {
  console.log('🔍 Checking DataTable Props...');
  console.log('');

  try {
    // Look for DataTable component in the DOM
    const tables = document.querySelectorAll('table');
    console.log(`📊 Tables found: ${tables.length}`);

    // Look for export-related elements
    const exportElements = document.querySelectorAll('[class*="export"], [class*="Export"]');
    console.log(`📤 Export elements: ${exportElements.length}`);

    // Check for DataTable wrapper
    const dataTableWrappers = document.querySelectorAll('[class*="card"]');
    console.log(`🃏 Card wrappers: ${dataTableWrappers.length}`);

    // Look for specific DataTable indicators
    const tableHeaders = document.querySelectorAll('th');
    console.log(`📋 Table headers: ${tableHeaders.length}`);

    tableHeaders.forEach((header, index) => {
      console.log(`   Header ${index + 1}: "${header.textContent}"`);
    });

    // Check for pagination
    const paginationElements = document.querySelectorAll('[class*="pagination"], [class*="page"]');
    console.log(`📄 Pagination elements: ${paginationElements.length}`);

    return {
      success: true,
      results: {
        tables: tables.length,
        exportElements: exportElements.length,
        cardWrappers: dataTableWrappers.length,
        tableHeaders: tableHeaders.length,
        paginationElements: paginationElements.length
      }
    };

  } catch (error) {
    console.error('💥 DataTable props check failed:', error);
    return { success: false, error: error.message };
  }
};

/**
 * Run all export debugging tests
 */
export const debugAllExportIssues = () => {
  console.log('🚀 DEBUGGING ALL EXPORT ISSUES...');
  console.log('');

  // Test 1: Check dependencies
  const depsResult = checkExportDependencies();
  console.log('');

  // Test 2: Check DataTable props
  const propsResult = checkDataTableProps();
  console.log('');

  // Test 3: Debug export modal
  const modalResult = debugExportModal();
  console.log('');

  // Test 4: Test PDF generation
  const pdfResult = testPDFGeneration();
  console.log('');

  console.log('📊 DEBUGGING SUMMARY:');
  console.log('');
  console.log('1️⃣ Dependencies Check:');
  console.log(`   Status: ${depsResult ? '✅ COMPLETED' : '❌ FAILED'}`);

  console.log('');
  console.log('2️⃣ DataTable Props Check:');
  console.log(`   Status: ${propsResult.success ? '✅ COMPLETED' : '❌ FAILED'}`);
  if (propsResult.results) {
    console.log(`   - Tables: ${propsResult.results.tables}`);
    console.log(`   - Export elements: ${propsResult.results.exportElements}`);
    console.log(`   - Headers: ${propsResult.results.tableHeaders}`);
  }

  console.log('');
  console.log('3️⃣ Export Modal Debug:');
  console.log(`   Status: ${modalResult.success ? '✅ COMPLETED' : '❌ FAILED'}`);
  console.log(`   - Export button found: ${modalResult.exportButtonFound ? '✅' : '❌'}`);

  console.log('');
  console.log('4️⃣ PDF Generation Test:');
  console.log('   Status: ⏳ RUNNING (check downloads folder)');

  console.log('');
  console.log('🎉 ALL EXPORT DEBUGGING COMPLETED!');

  return {
    success: true,
    tests: {
      dependencies: depsResult,
      dataTableProps: propsResult,
      exportModal: modalResult,
      pdfGeneration: pdfResult
    }
  };
};

// Make functions available globally
if (typeof window !== 'undefined') {
  window.checkExportDependencies = checkExportDependencies;
  window.testPDFGeneration = testPDFGeneration;
  window.debugExportModal = debugExportModal;
  window.checkDataTableProps = checkDataTableProps;
  window.debugAllExportIssues = debugAllExportIssues;
  
  console.log('🔧 Export Debug Functions Available:');
  console.log('- window.checkExportDependencies() - Check if libraries are loaded');
  console.log('- window.testPDFGeneration() - Test PDF creation directly');
  console.log('- window.debugExportModal() - Debug export modal functionality');
  console.log('- window.checkDataTableProps() - Check DataTable component');
  console.log('- window.debugAllExportIssues() - Run all debugging tests');
}
