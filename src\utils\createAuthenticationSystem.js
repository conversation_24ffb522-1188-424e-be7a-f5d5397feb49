/**
 * Authentication System Setup
 * Creates users table and authentication system for Vendors and Sub-Admins
 */

import { sql } from '../config/database.js';

/**
 * Create users authentication table
 */
export const createUsersTable = async () => {
  try {
    console.log('🔐 Creating users authentication table...');

    await sql`
      CREATE TABLE IF NOT EXISTS auth_users (
        id SERIAL PRIMARY KEY,
        username VARCHAR(255) NOT NULL UNIQUE,
        email VARCHAR(255) NOT NULL,
        password_hash VARCHAR(255) NOT NULL,
        user_type VARCHAR(50) NOT NULL CHECK (user_type IN ('admin', 'subadmin', 'vendor')),
        user_id INTEGER NOT NULL,
        is_active BOOLEAN DEFAULT true,
        must_change_password BOOLEAN DEFAULT true,
        last_login TIMESTAMP,
        password_reset_token VARCHAR(255),
        password_reset_expires TIMESTAMP,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

        -- Ensure unique combination of user_type and user_id
        UNIQUE(user_type, user_id)
      )
    `;

    console.log('✅ Auth users table created');

    // Create indexes for better performance
    await sql`CREATE INDEX IF NOT EXISTS idx_auth_users_username ON auth_users(username)`;
    await sql`CREATE INDEX IF NOT EXISTS idx_auth_users_email ON auth_users(email)`;
    await sql`CREATE INDEX IF NOT EXISTS idx_auth_users_type_id ON auth_users(user_type, user_id)`;
    
    console.log('✅ Database indexes created');

    return { success: true, message: 'Users table created successfully' };

  } catch (error) {
    console.error('❌ Error creating users table:', error);
    return { success: false, error: error.message };
  }
};

/**
 * Create login sessions table
 */
export const createSessionsTable = async () => {
  try {
    console.log('🔐 Creating sessions table...');

    await sql`
      CREATE TABLE IF NOT EXISTS auth_sessions (
        id SERIAL PRIMARY KEY,
        user_id INTEGER NOT NULL REFERENCES auth_users(id) ON DELETE CASCADE,
        session_token VARCHAR(255) NOT NULL UNIQUE,
        expires_at TIMESTAMP NOT NULL,
        ip_address INET,
        user_agent TEXT,
        is_active BOOLEAN DEFAULT true,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `;

    console.log('✅ Sessions table created');

    // Create indexes
    await sql`CREATE INDEX IF NOT EXISTS idx_auth_sessions_token ON auth_sessions(session_token)`;
    await sql`CREATE INDEX IF NOT EXISTS idx_auth_sessions_user ON auth_sessions(user_id)`;
    await sql`CREATE INDEX IF NOT EXISTS idx_auth_sessions_expires ON auth_sessions(expires_at)`;

    console.log('✅ Session indexes created');

    return { success: true, message: 'Sessions table created successfully' };

  } catch (error) {
    console.error('❌ Error creating sessions table:', error);
    return { success: false, error: error.message };
  }
};

/**
 * Generate random password
 */
export const generatePassword = (length = 12) => {
  const charset = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*';
  let password = '';
  
  // Ensure at least one of each type
  password += 'ABCDEFGHIJKLMNOPQRSTUVWXYZ'[Math.floor(Math.random() * 26)]; // Uppercase
  password += 'abcdefghijklmnopqrstuvwxyz'[Math.floor(Math.random() * 26)]; // Lowercase
  password += '0123456789'[Math.floor(Math.random() * 10)]; // Number
  password += '!@#$%^&*'[Math.floor(Math.random() * 8)]; // Special char
  
  // Fill the rest
  for (let i = 4; i < length; i++) {
    password += charset[Math.floor(Math.random() * charset.length)];
  }
  
  // Shuffle the password
  return password.split('').sort(() => Math.random() - 0.5).join('');
};

/**
 * Hash password (simple implementation - in production use bcrypt)
 */
export const hashPassword = async (password) => {
  // Simple hash for demo - in production use bcrypt
  const encoder = new TextEncoder();
  const data = encoder.encode(password + 'innoventory_salt_2024');
  const hashBuffer = await crypto.subtle.digest('SHA-256', data);
  const hashArray = Array.from(new Uint8Array(hashBuffer));
  return hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
};

/**
 * Verify password
 */
export const verifyPassword = async (password, hash) => {
  const passwordHash = await hashPassword(password);
  return passwordHash === hash;
};

/**
 * Create user account for vendor or sub-admin
 */
export const createUserAccount = async (userData) => {
  try {
    const { username, email, userType, userId } = userData;
    
    console.log(`🔐 Creating user account for ${userType}:`, username);

    // Check if username already exists
    const existingUser = await sql`
      SELECT id FROM auth_users WHERE username = ${username}
    `;

    if (existingUser.length > 0) {
      throw new Error(`Username '${username}' already exists`);
    }

    // Generate password
    const password = generatePassword();
    const passwordHash = await hashPassword(password);

    // Create user record
    const newUser = await sql`
      INSERT INTO auth_users (username, email, password_hash, user_type, user_id, must_change_password)
      VALUES (${username}, ${email}, ${passwordHash}, ${userType}, ${userId}, true)
      RETURNING id, username, email, user_type, created_at
    `;

    console.log('✅ User account created:', newUser[0]);

    return {
      success: true,
      user: newUser[0],
      password: password, // Return plain password for email
      message: 'User account created successfully'
    };

  } catch (error) {
    console.error('❌ Error creating user account:', error);
    return {
      success: false,
      error: error.message,
      message: 'Failed to create user account'
    };
  }
};

/**
 * Send login credentials via email
 */
export const sendLoginCredentials = async (email, username, password, userType) => {
  try {
    console.log(`📧 Sending login credentials to ${email}...`);

    // Email template
    const emailSubject = `Your ${userType === 'vendor' ? 'Vendor' : 'Sub-Admin'} Login Credentials - Innoventory`;
    
    const emailBody = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2 style="color: #1f2937;">Welcome to Innoventory!</h2>
        
        <p>Your ${userType === 'vendor' ? 'vendor' : 'sub-admin'} account has been created successfully.</p>
        
        <div style="background-color: #f3f4f6; padding: 20px; border-radius: 8px; margin: 20px 0;">
          <h3 style="color: #374151; margin-top: 0;">Login Credentials:</h3>
          <p><strong>Username:</strong> ${username}</p>
          <p><strong>Password:</strong> ${password}</p>
          <p><strong>Login URL:</strong> <a href="${window.location.origin}/login">${window.location.origin}/login</a></p>
        </div>
        
        <div style="background-color: #fef3c7; padding: 15px; border-radius: 8px; margin: 20px 0;">
          <p style="margin: 0; color: #92400e;">
            <strong>Important:</strong> You will be required to change your password on first login for security purposes.
          </p>
        </div>
        
        <p>If you have any questions, please contact the administrator.</p>
        
        <hr style="margin: 30px 0; border: none; border-top: 1px solid #e5e7eb;">
        <p style="color: #6b7280; font-size: 14px;">
          This is an automated message. Please do not reply to this email.
        </p>
      </div>
    `;

    // For now, just log the email (in production, integrate with email service)
    console.log('📧 EMAIL TO SEND:');
    console.log('To:', email);
    console.log('Subject:', emailSubject);
    console.log('Body:', emailBody);
    
    // TODO: Integrate with actual email service (SendGrid, AWS SES, etc.)
    // await emailService.send({ to: email, subject: emailSubject, html: emailBody });

    console.log('✅ Login credentials email prepared');

    return {
      success: true,
      message: 'Login credentials email sent successfully',
      emailData: { to: email, subject: emailSubject, body: emailBody }
    };

  } catch (error) {
    console.error('❌ Error sending login credentials:', error);
    return {
      success: false,
      error: error.message,
      message: 'Failed to send login credentials'
    };
  }
};

/**
 * Complete authentication system setup
 */
export const setupAuthenticationSystem = async () => {
  try {
    console.log('🚀 Setting up complete authentication system...');

    // Create tables
    const usersResult = await createUsersTable();
    if (!usersResult.success) {
      throw new Error('Failed to create users table: ' + usersResult.error);
    }

    const sessionsResult = await createSessionsTable();
    if (!sessionsResult.success) {
      throw new Error('Failed to create sessions table: ' + sessionsResult.error);
    }

    console.log('🎉 Authentication system setup completed successfully!');
    console.log('');
    console.log('✅ What was created:');
    console.log('   - Users authentication table');
    console.log('   - User sessions table');
    console.log('   - Password generation system');
    console.log('   - Email credential system');
    console.log('');
    console.log('🔧 Next steps:');
    console.log('   - Create vendors/sub-admins to test');
    console.log('   - Check email logs for credentials');
    console.log('   - Test login with generated credentials');

    return {
      success: true,
      message: 'Authentication system setup completed'
    };

  } catch (error) {
    console.error('💥 Authentication system setup failed:', error);
    return {
      success: false,
      error: error.message,
      message: 'Authentication system setup failed'
    };
  }
};

// Make functions available globally
if (typeof window !== 'undefined') {
  window.setupAuthenticationSystem = setupAuthenticationSystem;
  window.createUsersTable = createUsersTable;
  window.createSessionsTable = createSessionsTable;
  window.createUserAccount = createUserAccount;
  window.generatePassword = generatePassword;
  
  console.log('🔐 Authentication System Functions Available:');
  console.log('- window.setupAuthenticationSystem() - Complete setup');
  console.log('- window.createUsersTable() - Create users table');
  console.log('- window.createUserAccount({username, email, userType, userId}) - Create user');
  console.log('- window.generatePassword() - Generate random password');
}
