<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Global Location System Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background: #f9f9f9;
        }
        .test-section h3 {
            margin-top: 0;
            color: #555;
        }
        button {
            background: #007cba;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #005a87;
        }
        .result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 3px;
            font-family: monospace;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .loading {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        select {
            width: 100%;
            padding: 8px;
            margin: 5px 0;
            border: 1px solid #ddd;
            border-radius: 3px;
        }
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .stat-card {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 5px;
            text-align: center;
        }
        .stat-number {
            font-size: 24px;
            font-weight: bold;
            color: #1976d2;
        }
        .stat-label {
            color: #666;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🌍 Global Location System Test</h1>
        
        <div class="stats">
            <div class="stat-card">
                <div class="stat-number" id="countryCount">-</div>
                <div class="stat-label">Countries</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="stateCount">-</div>
                <div class="stat-label">States/Provinces</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="cityCount">-</div>
                <div class="stat-label">Cities</div>
            </div>
        </div>

        <div class="test-section">
            <h3>📊 Data Loading Test</h3>
            <button onclick="testDataLoading()">Test Data Sources</button>
            <div id="dataResult"></div>
        </div>

        <div class="test-section">
            <h3>🌍 Country Selection Test</h3>
            <button onclick="loadCountries()">Load Countries</button>
            <select id="countrySelect" onchange="onCountryChange()">
                <option value="">Select a country</option>
            </select>
            <div id="countryResult"></div>
        </div>

        <div class="test-section">
            <h3>🏛️ State/Province Selection Test</h3>
            <select id="stateSelect" onchange="onStateChange()" disabled>
                <option value="">Select a state/province</option>
            </select>
            <div id="stateResult"></div>
        </div>

        <div class="test-section">
            <h3>🏙️ City Selection Test</h3>
            <select id="citySelect" onchange="onCityChange()" disabled>
                <option value="">Select a city</option>
            </select>
            <div id="cityResult"></div>
        </div>

        <div class="test-section">
            <h3>🔍 Search Test</h3>
            <input type="text" id="searchInput" placeholder="Search countries, states, or cities..." 
                   oninput="performSearch()" style="width: 100%; padding: 8px; margin: 5px 0;">
            <div id="searchResult"></div>
        </div>

        <div class="test-section">
            <h3>📍 Selected Location</h3>
            <div id="selectedLocation" class="result">No location selected</div>
        </div>
    </div>

    <script>
        let countriesData = [];
        let statesData = [];
        let citiesData = [];
        let selectedCountry = null;
        let selectedState = null;
        let selectedCity = null;

        // Test data loading from multiple sources
        async function testDataLoading() {
            const resultDiv = document.getElementById('dataResult');
            resultDiv.innerHTML = '<div class="loading">Testing data sources...</div>';

            try {
                // Test 1: Local data
                console.log('🧪 Testing local data source...');
                try {
                    const response = await fetch('/global-locations.json');
                    if (response.ok) {
                        const data = await response.json();
                        console.log('✅ Local data loaded:', data.countries?.length, 'countries');
                        resultDiv.innerHTML += '<div class="success">✅ Local data source: ' + (data.countries?.length || 0) + ' countries</div>';
                    } else {
                        throw new Error('Local data not available');
                    }
                } catch (error) {
                    console.log('⚠️ Local data failed:', error.message);
                    resultDiv.innerHTML += '<div class="error">❌ Local data source failed</div>';
                }

                // Test 2: External API
                console.log('🧪 Testing external API...');
                try {
                    const response = await fetch('https://raw.githubusercontent.com/dr5hn/countries-states-cities-database/master/json/countries.json');
                    if (response.ok) {
                        const data = await response.json();
                        console.log('✅ External API loaded:', data.length, 'countries');
                        resultDiv.innerHTML += '<div class="success">✅ External API: ' + data.length + ' countries</div>';
                        
                        // Store data for testing
                        countriesData = data;
                        document.getElementById('countryCount').textContent = data.length;
                    } else {
                        throw new Error('External API not available');
                    }
                } catch (error) {
                    console.log('⚠️ External API failed:', error.message);
                    resultDiv.innerHTML += '<div class="error">❌ External API failed</div>';
                }

                // Test 3: States data
                console.log('🧪 Testing states data...');
                try {
                    const response = await fetch('https://raw.githubusercontent.com/dr5hn/countries-states-cities-database/master/json/states.json');
                    if (response.ok) {
                        const data = await response.json();
                        console.log('✅ States data loaded:', data.length, 'states');
                        resultDiv.innerHTML += '<div class="success">✅ States data: ' + data.length + ' states/provinces</div>';
                        statesData = data;
                        document.getElementById('stateCount').textContent = data.length;
                    }
                } catch (error) {
                    console.log('⚠️ States data failed:', error.message);
                    resultDiv.innerHTML += '<div class="error">❌ States data failed</div>';
                }

                // Test 4: Cities data (sample)
                console.log('🧪 Testing cities data...');
                try {
                    const response = await fetch('https://raw.githubusercontent.com/dr5hn/countries-states-cities-database/master/json/cities.json');
                    if (response.ok) {
                        const data = await response.json();
                        console.log('✅ Cities data loaded:', data.length, 'cities');
                        resultDiv.innerHTML += '<div class="success">✅ Cities data: ' + data.length + ' cities</div>';
                        citiesData = data;
                        document.getElementById('cityCount').textContent = data.length;
                    }
                } catch (error) {
                    console.log('⚠️ Cities data failed:', error.message);
                    resultDiv.innerHTML += '<div class="error">❌ Cities data failed</div>';
                }

                resultDiv.innerHTML += '<div class="success">🎉 Data loading test completed!</div>';

            } catch (error) {
                resultDiv.innerHTML += '<div class="error">❌ Test failed: ' + error.message + '</div>';
            }
        }

        // Load countries into dropdown
        async function loadCountries() {
            const select = document.getElementById('countrySelect');
            const resultDiv = document.getElementById('countryResult');
            
            if (countriesData.length === 0) {
                await testDataLoading();
            }

            select.innerHTML = '<option value="">Select a country</option>';
            
            // Sort countries alphabetically
            const sortedCountries = countriesData.sort((a, b) => a.name.localeCompare(b.name));
            
            sortedCountries.forEach(country => {
                const option = document.createElement('option');
                option.value = country.id;
                option.textContent = `${country.emoji || '🏳️'} ${country.name}`;
                option.dataset.country = JSON.stringify(country);
                select.appendChild(option);
            });

            resultDiv.innerHTML = `<div class="success">✅ Loaded ${sortedCountries.length} countries</div>`;
        }

        // Handle country selection
        function onCountryChange() {
            const select = document.getElementById('countrySelect');
            const stateSelect = document.getElementById('stateSelect');
            const citySelect = document.getElementById('citySelect');
            
            if (select.value) {
                selectedCountry = JSON.parse(select.options[select.selectedIndex].dataset.country);
                
                // Load states for selected country
                const countryStates = statesData.filter(state => state.country_id == select.value);
                
                stateSelect.innerHTML = '<option value="">Select a state/province</option>';
                countryStates.forEach(state => {
                    const option = document.createElement('option');
                    option.value = state.id;
                    option.textContent = state.name;
                    option.dataset.state = JSON.stringify(state);
                    stateSelect.appendChild(option);
                });
                
                stateSelect.disabled = false;
                citySelect.disabled = true;
                citySelect.innerHTML = '<option value="">Select a city</option>';
                
                document.getElementById('stateResult').innerHTML = 
                    `<div class="success">✅ Loaded ${countryStates.length} states/provinces for ${selectedCountry.name}</div>`;
            } else {
                selectedCountry = null;
                stateSelect.disabled = true;
                citySelect.disabled = true;
            }
            
            updateSelectedLocation();
        }

        // Handle state selection
        function onStateChange() {
            const stateSelect = document.getElementById('stateSelect');
            const citySelect = document.getElementById('citySelect');
            
            if (stateSelect.value) {
                selectedState = JSON.parse(stateSelect.options[stateSelect.selectedIndex].dataset.state);
                
                // Load cities for selected state
                const stateCities = citiesData.filter(city => city.state_id == stateSelect.value);
                
                citySelect.innerHTML = '<option value="">Select a city</option>';
                stateCities.forEach(city => {
                    const option = document.createElement('option');
                    option.value = city.id;
                    option.textContent = city.name;
                    option.dataset.city = JSON.stringify(city);
                    citySelect.appendChild(option);
                });
                
                citySelect.disabled = false;
                
                document.getElementById('cityResult').innerHTML = 
                    `<div class="success">✅ Loaded ${stateCities.length} cities for ${selectedState.name}</div>`;
            } else {
                selectedState = null;
                citySelect.disabled = true;
            }
            
            updateSelectedLocation();
        }

        // Handle city selection
        function onCityChange() {
            const citySelect = document.getElementById('citySelect');
            
            if (citySelect.value) {
                selectedCity = JSON.parse(citySelect.options[citySelect.selectedIndex].dataset.city);
            } else {
                selectedCity = null;
            }
            
            updateSelectedLocation();
        }

        // Update selected location display
        function updateSelectedLocation() {
            const div = document.getElementById('selectedLocation');
            
            if (selectedCity) {
                div.innerHTML = `
                    <strong>Complete Location:</strong><br>
                    🏙️ City: ${selectedCity.name}<br>
                    🏛️ State: ${selectedState.name}<br>
                    🌍 Country: ${selectedCountry.name} ${selectedCountry.emoji || ''}
                `;
                div.className = 'result success';
            } else if (selectedState) {
                div.innerHTML = `
                    <strong>Partial Location:</strong><br>
                    🏛️ State: ${selectedState.name}<br>
                    🌍 Country: ${selectedCountry.name} ${selectedCountry.emoji || ''}
                `;
                div.className = 'result';
            } else if (selectedCountry) {
                div.innerHTML = `
                    <strong>Country Selected:</strong><br>
                    🌍 Country: ${selectedCountry.name} ${selectedCountry.emoji || ''}
                `;
                div.className = 'result';
            } else {
                div.innerHTML = 'No location selected';
                div.className = 'result';
            }
        }

        // Search functionality
        function performSearch() {
            const searchTerm = document.getElementById('searchInput').value.toLowerCase();
            const resultDiv = document.getElementById('searchResult');
            
            if (searchTerm.length < 2) {
                resultDiv.innerHTML = '';
                return;
            }

            const countryResults = countriesData.filter(country => 
                country.name.toLowerCase().includes(searchTerm)
            ).slice(0, 5);

            const stateResults = statesData.filter(state => 
                state.name.toLowerCase().includes(searchTerm)
            ).slice(0, 5);

            const cityResults = citiesData.filter(city => 
                city.name.toLowerCase().includes(searchTerm)
            ).slice(0, 10);

            let html = '';
            
            if (countryResults.length > 0) {
                html += '<strong>Countries:</strong><br>';
                countryResults.forEach(country => {
                    html += `${country.emoji || '🏳️'} ${country.name}<br>`;
                });
                html += '<br>';
            }

            if (stateResults.length > 0) {
                html += '<strong>States/Provinces:</strong><br>';
                stateResults.forEach(state => {
                    const country = countriesData.find(c => c.id == state.country_id);
                    html += `🏛️ ${state.name} (${country?.name || 'Unknown'})<br>`;
                });
                html += '<br>';
            }

            if (cityResults.length > 0) {
                html += '<strong>Cities:</strong><br>';
                cityResults.forEach(city => {
                    const state = statesData.find(s => s.id == city.state_id);
                    const country = countriesData.find(c => c.id == city.country_id);
                    html += `🏙️ ${city.name} (${state?.name || 'Unknown'}, ${country?.name || 'Unknown'})<br>`;
                });
            }

            if (html === '') {
                html = 'No results found';
            }

            resultDiv.innerHTML = html;
        }

        // Auto-load data on page load
        window.onload = function() {
            testDataLoading();
        };
    </script>
</body>
</html>
