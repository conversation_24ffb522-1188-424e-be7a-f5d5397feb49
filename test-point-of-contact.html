<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Point of Contact Component Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background: #f9f9f9;
        }
        .test-section h3 {
            margin-top: 0;
            color: #555;
        }
        .contact-card {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 16px;
            margin: 16px 0;
            background: #f8f9fa;
        }
        .contact-header {
            display: flex;
            justify-content: between;
            align-items: center;
            margin-bottom: 12px;
        }
        .contact-title {
            font-weight: 600;
            color: #333;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .contact-fields {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 16px;
        }
        .field-group {
            display: flex;
            flex-direction: column;
        }
        .field-label {
            font-size: 12px;
            font-weight: 500;
            color: #666;
            margin-bottom: 4px;
        }
        .field-input {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        .field-input:focus {
            outline: none;
            border-color: #007cba;
            box-shadow: 0 0 0 2px rgba(0, 124, 186, 0.2);
        }
        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            display: inline-flex;
            align-items: center;
            gap: 4px;
        }
        .btn-primary {
            background: #007cba;
            color: white;
        }
        .btn-primary:hover {
            background: #005a87;
        }
        .btn-danger {
            background: #dc3545;
            color: white;
        }
        .btn-danger:hover {
            background: #c82333;
        }
        .btn-secondary {
            background: #6c757d;
            color: white;
        }
        .btn-secondary:hover {
            background: #545b62;
        }
        .add-contact-btn {
            background: #e3f2fd;
            color: #1976d2;
            border: 1px solid #bbdefb;
        }
        .add-contact-btn:hover {
            background: #bbdefb;
        }
        .remove-btn {
            background: #ffebee;
            color: #d32f2f;
            border: 1px solid #ffcdd2;
            padding: 4px 8px;
            font-size: 12px;
        }
        .remove-btn:hover {
            background: #ffcdd2;
        }
        .result {
            margin: 10px 0;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            background: #e3f2fd;
            border: 1px solid #bbdefb;
            white-space: pre-wrap;
        }
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .stat-card {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 5px;
            text-align: center;
        }
        .stat-number {
            font-size: 24px;
            font-weight: bold;
            color: #1976d2;
        }
        .stat-label {
            color: #666;
            font-size: 14px;
        }
        .expertise-badge {
            background: #e8f5e8;
            color: #2e7d32;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }
        .summary {
            background: #f0f8ff;
            border: 1px solid #b3d9ff;
            padding: 12px;
            border-radius: 5px;
            margin-top: 16px;
            font-size: 12px;
            color: #0066cc;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>👥 Point of Contact Component Test</h1>
        
        <div class="stats">
            <div class="stat-card">
                <div class="stat-number" id="contactCount">0</div>
                <div class="stat-label">Total Contacts</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="validContacts">0</div>
                <div class="stat-label">Valid Contacts</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="expertiseAreas">5</div>
                <div class="stat-label">Expertise Areas</div>
            </div>
        </div>

        <div class="test-section">
            <h3>📋 Point of Contact Demo</h3>
            <p>This demonstrates the Point of Contact functionality for vendor forms:</p>
            
            <!-- Header -->
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 16px;">
                <div>
                    <label style="font-weight: 500; color: #333;">Point of Contact <span style="color: red;">*</span></label>
                    <p style="font-size: 12px; color: #666; margin: 4px 0 0 0;">Add contact persons for this vendor (in case of not an individual)</p>
                </div>
                <button class="btn add-contact-btn" onclick="addContact()">
                    ➕ Add Contact
                </button>
            </div>

            <!-- Contacts Container -->
            <div id="contactsContainer"></div>

            <!-- Summary -->
            <div class="summary" id="summary">
                Summary: 0 contacts added
            </div>
        </div>

        <div class="test-section">
            <h3>🧪 Test Actions</h3>
            <button class="btn btn-primary" onclick="addSampleContacts()">Add Sample Contacts</button>
            <button class="btn btn-secondary" onclick="clearAllContacts()">Clear All</button>
            <button class="btn btn-secondary" onclick="validateContacts()">Validate Contacts</button>
            <button class="btn btn-secondary" onclick="showContactData()">Show Data</button>
        </div>

        <div class="test-section">
            <h3>📊 Features Demonstrated</h3>
            <ul>
                <li>✅ <strong>Multiple Contacts</strong>: Add/remove multiple contact persons</li>
                <li>✅ <strong>Required Fields</strong>: Name, Phone, Email, Area of Expertise</li>
                <li>✅ <strong>Validation</strong>: Email and phone number validation</li>
                <li>✅ <strong>Area of Expertise</strong>: Dropdown with predefined options</li>
                <li>✅ <strong>Dynamic UI</strong>: Add/remove contacts dynamically</li>
                <li>✅ <strong>Professional Layout</strong>: Clean, organized interface</li>
                <li>✅ <strong>Data Structure</strong>: Proper JSON format for database storage</li>
            </ul>
        </div>

        <div class="test-section">
            <h3>📄 Contact Data Output</h3>
            <div class="result" id="dataOutput">
                No contacts added yet
            </div>
        </div>
    </div>

    <script>
        // Sample area of expertise options
        const areaOfExpertiseOptions = [
            { id: 1, name: "Patent Law" },
            { id: 2, name: "Trademark Law" },
            { id: 3, name: "Copyright Law" },
            { id: 4, name: "IP Litigation" },
            { id: 5, name: "Technical Writing" },
            { id: 6, name: "Prior Art Search" },
            { id: 7, name: "Patent Filing" },
            { id: 8, name: "Legal Consulting" }
        ];

        let contacts = [];
        let nextId = 1;

        // Initialize with one empty contact
        function init() {
            addContact();
            updateDisplay();
        }

        // Create empty contact
        function createEmptyContact() {
            return {
                id: nextId++,
                name: '',
                phone: '',
                email: '',
                areaOfExpertise: ''
            };
        }

        // Add new contact
        function addContact() {
            const newContact = createEmptyContact();
            contacts.push(newContact);
            renderContacts();
            updateDisplay();
        }

        // Remove contact
        function removeContact(id) {
            if (contacts.length <= 1) {
                // Don't allow removing the last contact, just clear it
                contacts = [createEmptyContact()];
            } else {
                contacts = contacts.filter(contact => contact.id !== id);
            }
            renderContacts();
            updateDisplay();
        }

        // Update contact field
        function updateContact(id, field, value) {
            const contact = contacts.find(c => c.id === id);
            if (contact) {
                contact[field] = value;
                updateDisplay();
            }
        }

        // Render all contacts
        function renderContacts() {
            const container = document.getElementById('contactsContainer');
            container.innerHTML = '';

            contacts.forEach((contact, index) => {
                const contactDiv = document.createElement('div');
                contactDiv.className = 'contact-card';
                contactDiv.innerHTML = `
                    <div class="contact-header">
                        <div class="contact-title">
                            👤 Contact ${index + 1}
                            ${contact.name ? ` - ${contact.name}` : ''}
                        </div>
                        ${contacts.length > 1 ? `
                            <button class="btn remove-btn" onclick="removeContact(${contact.id})">
                                🗑️ Remove
                            </button>
                        ` : ''}
                    </div>
                    <div class="contact-fields">
                        <div class="field-group">
                            <label class="field-label">Name *</label>
                            <input 
                                type="text" 
                                class="field-input" 
                                value="${contact.name}" 
                                placeholder="Contact person name"
                                onchange="updateContact(${contact.id}, 'name', this.value)"
                            />
                        </div>
                        <div class="field-group">
                            <label class="field-label">Phone Number *</label>
                            <input 
                                type="tel" 
                                class="field-input" 
                                value="${contact.phone}" 
                                placeholder="+91 9876543210"
                                onchange="updateContact(${contact.id}, 'phone', this.value)"
                            />
                        </div>
                        <div class="field-group">
                            <label class="field-label">Email ID *</label>
                            <input 
                                type="email" 
                                class="field-input" 
                                value="${contact.email}" 
                                placeholder="<EMAIL>"
                                onchange="updateContact(${contact.id}, 'email', this.value)"
                            />
                        </div>
                        <div class="field-group">
                            <label class="field-label">Area of Expertise *</label>
                            <select 
                                class="field-input" 
                                onchange="updateContact(${contact.id}, 'areaOfExpertise', this.value)"
                            >
                                <option value="">Select area of expertise</option>
                                ${areaOfExpertiseOptions.map(area => `
                                    <option value="${area.id}" ${contact.areaOfExpertise == area.id ? 'selected' : ''}>
                                        ${area.name}
                                    </option>
                                `).join('')}
                            </select>
                        </div>
                    </div>
                `;
                container.appendChild(contactDiv);
            });
        }

        // Update display and stats
        function updateDisplay() {
            const validContacts = contacts.filter(c => 
                c.name.trim() && c.phone.trim() && c.email.trim() && c.areaOfExpertise
            ).length;

            document.getElementById('contactCount').textContent = contacts.length;
            document.getElementById('validContacts').textContent = validContacts;

            // Update summary
            const summary = document.getElementById('summary');
            if (contacts.length === 0) {
                summary.textContent = 'Summary: 0 contacts added';
            } else {
                const names = contacts.filter(c => c.name).map(c => c.name);
                summary.textContent = `Summary: ${contacts.length} contact${contacts.length !== 1 ? 's' : ''} added${names.length > 0 ? ' - ' + names.join(', ') : ''}`;
            }

            // Update data output
            updateDataOutput();
        }

        // Update data output
        function updateDataOutput() {
            const output = document.getElementById('dataOutput');
            if (contacts.length === 0 || contacts.every(c => !c.name && !c.phone && !c.email)) {
                output.textContent = 'No contacts added yet';
            } else {
                const formattedData = {
                    pointOfContact: contacts.map(contact => ({
                        name: contact.name,
                        phone: contact.phone,
                        email: contact.email,
                        areaOfExpertise: contact.areaOfExpertise,
                        areaOfExpertiseName: areaOfExpertiseOptions.find(a => a.id == contact.areaOfExpertise)?.name || ''
                    }))
                };
                output.textContent = JSON.stringify(formattedData, null, 2);
            }
        }

        // Test functions
        function addSampleContacts() {
            contacts = [
                {
                    id: nextId++,
                    name: "John Smith",
                    phone: "+91 9876543210",
                    email: "<EMAIL>",
                    areaOfExpertise: "1"
                },
                {
                    id: nextId++,
                    name: "Sarah Johnson",
                    phone: "+91 8765432109",
                    email: "<EMAIL>",
                    areaOfExpertise: "2"
                },
                {
                    id: nextId++,
                    name: "Michael Brown",
                    phone: "+91 7654321098",
                    email: "<EMAIL>",
                    areaOfExpertise: "4"
                }
            ];
            renderContacts();
            updateDisplay();
        }

        function clearAllContacts() {
            contacts = [createEmptyContact()];
            renderContacts();
            updateDisplay();
        }

        function validateContacts() {
            const errors = [];
            contacts.forEach((contact, index) => {
                if (!contact.name.trim()) {
                    errors.push(`Contact ${index + 1}: Name is required`);
                }
                if (!contact.phone.trim()) {
                    errors.push(`Contact ${index + 1}: Phone is required`);
                } else if (!/^\+?[\d\s-()]+$/.test(contact.phone)) {
                    errors.push(`Contact ${index + 1}: Invalid phone format`);
                }
                if (!contact.email.trim()) {
                    errors.push(`Contact ${index + 1}: Email is required`);
                } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(contact.email)) {
                    errors.push(`Contact ${index + 1}: Invalid email format`);
                }
                if (!contact.areaOfExpertise) {
                    errors.push(`Contact ${index + 1}: Area of expertise is required`);
                }
            });

            if (errors.length === 0) {
                alert('✅ All contacts are valid!');
            } else {
                alert('❌ Validation errors:\n\n' + errors.join('\n'));
            }
        }

        function showContactData() {
            const data = {
                totalContacts: contacts.length,
                validContacts: contacts.filter(c => c.name && c.phone && c.email && c.areaOfExpertise).length,
                contacts: contacts,
                formattedForDatabase: contacts.map(contact => ({
                    name: contact.name,
                    phone: contact.phone,
                    email: contact.email,
                    areaOfExpertise: contact.areaOfExpertise
                }))
            };
            alert('Contact Data:\n\n' + JSON.stringify(data, null, 2));
        }

        // Initialize on page load
        window.onload = init;
    </script>
</body>
</html>
