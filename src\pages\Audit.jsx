import { useState, useEffect } from 'react';
import {
  ChartBarIcon,
  UsersIcon,
  UserGroupIcon,
  DocumentTextIcon,
  EyeIcon,
  ArrowPathIcon
} from '@heroicons/react/24/outline';

const Audit = () => {
  const [selectedPeriod, setSelectedPeriod] = useState('7days');
  const [loading, setLoading] = useState(true);
  const [usageStats, setUsageStats] = useState({
    clients: { totalUsers: 0, activeUsers: 0, sessionsToday: 0, avgSessionTime: '0m 0s' },
    vendors: { totalUsers: 0, activeUsers: 0, sessionsToday: 0, avgSessionTime: '0m 0s' },
    subAdmins: { totalUsers: 0, activeUsers: 0, sessionsToday: 0, avgSessionTime: '0m 0s' },
    orders: { total: 0, today: 0, thisWeek: 0 }
  });
  const [mostUsedFeatures, setMostUsedFeatures] = useState([]);
  const [recentActivity, setRecentActivity] = useState([]);

  // Load audit data from database
  useEffect(() => {
    loadAuditData();
  }, [selectedPeriod]);

  const loadAuditData = async () => {
    try {
      setLoading(true);

      // Import audit service
      const { getUsageStats, getFeatureUsage, getRecentActivity } = await import('../services/auditService');

      // Load all audit data
      const [stats, features, activity] = await Promise.all([
        getUsageStats(),
        getFeatureUsage(),
        getRecentActivity(10)
      ]);

      setUsageStats(stats);
      setMostUsedFeatures(features);
      setRecentActivity(activity);
    } catch (error) {
      console.error('Error loading audit data:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleViewDetails = (userType) => {
    // Navigate to detailed view based on user type
    switch (userType) {
      case 'clients':
        window.location.href = '/clients';
        break;
      case 'vendors':
        window.location.href = '/vendors';
        break;
      case 'sub-admins':
        window.location.href = '/sub-admins';
        break;
      default:
        console.log(`Viewing detailed analytics for ${userType}`);
    }
  };

  const handleRefresh = () => {
    loadAuditData();
  };

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="border-b border-gray-200 pb-4">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Audit & Analytics</h1>
            <p className="mt-2 text-gray-600">Monitor portal usage and user activity</p>
          </div>
          <button
            onClick={handleRefresh}
            disabled={loading}
            className="btn-secondary flex items-center"
          >
            <ArrowPathIcon className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            Refresh
          </button>
        </div>
      </div>

      {/* Time Period Selector */}
      <div className="flex space-x-4">
        <select
          value={selectedPeriod}
          onChange={(e) => setSelectedPeriod(e.target.value)}
          className="input-field max-w-xs"
        >
          <option value="24hours">Last 24 Hours</option>
          <option value="7days">Last 7 Days</option>
          <option value="30days">Last 30 Days</option>
          <option value="90days">Last 90 Days</option>
        </select>
      </div>

      {/* System Overview */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <UsersIcon className="h-8 w-8 text-blue-600" />
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium text-blue-900">Total Users</p>
              <p className="text-2xl font-bold text-blue-600">
                {loading ? '...' : (usageStats.clients.totalUsers + usageStats.vendors.totalUsers + usageStats.subAdmins.totalUsers).toLocaleString()}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-green-50 border border-green-200 rounded-lg p-4">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <DocumentTextIcon className="h-8 w-8 text-green-600" />
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium text-green-900">Total Orders</p>
              <p className="text-2xl font-bold text-green-600">
                {loading ? '...' : usageStats.orders.total.toLocaleString()}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <ChartBarIcon className="h-8 w-8 text-yellow-600" />
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium text-yellow-900">Orders Today</p>
              <p className="text-2xl font-bold text-yellow-600">
                {loading ? '...' : usageStats.orders.today.toLocaleString()}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-purple-50 border border-purple-200 rounded-lg p-4">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <UserGroupIcon className="h-8 w-8 text-purple-600" />
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium text-purple-900">This Week</p>
              <p className="text-2xl font-bold text-purple-600">
                {loading ? '...' : usageStats.orders.thisWeek.toLocaleString()}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Usage Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {/* Clients Usage */}
        <div className="card">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-900">Client Usage</h3>
            <UsersIcon className="h-8 w-8 text-blue-500" />
          </div>
          {loading ? (
            <div className="space-y-3">
              <div className="animate-pulse">
                <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                <div className="h-4 bg-gray-200 rounded w-1/2 mb-2"></div>
                <div className="h-4 bg-gray-200 rounded w-2/3 mb-2"></div>
                <div className="h-4 bg-gray-200 rounded w-1/2"></div>
              </div>
            </div>
          ) : (
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">Total Users:</span>
                <span className="font-medium">{usageStats.clients.totalUsers.toLocaleString()}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">Active Users:</span>
                <span className="font-medium text-green-600">{usageStats.clients.activeUsers.toLocaleString()}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">Sessions Today:</span>
                <span className="font-medium">{usageStats.clients.sessionsToday}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">Avg Session Time:</span>
                <span className="font-medium">{usageStats.clients.avgSessionTime}</span>
              </div>
            </div>
          )}
          <button
            onClick={() => handleViewDetails('clients')}
            className="mt-4 w-full btn-secondary flex items-center justify-center"
          >
            <EyeIcon className="h-4 w-4 mr-2" />
            View Details
          </button>
        </div>

        {/* Vendors Usage */}
        <div className="card">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-900">Vendor Usage</h3>
            <UserGroupIcon className="h-8 w-8 text-green-500" />
          </div>
          {loading ? (
            <div className="space-y-3">
              <div className="animate-pulse">
                <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                <div className="h-4 bg-gray-200 rounded w-1/2 mb-2"></div>
                <div className="h-4 bg-gray-200 rounded w-2/3 mb-2"></div>
                <div className="h-4 bg-gray-200 rounded w-1/2"></div>
              </div>
            </div>
          ) : (
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">Total Users:</span>
                <span className="font-medium">{usageStats.vendors.totalUsers.toLocaleString()}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">Active Users:</span>
                <span className="font-medium text-green-600">{usageStats.vendors.activeUsers.toLocaleString()}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">Sessions Today:</span>
                <span className="font-medium">{usageStats.vendors.sessionsToday}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">Avg Session Time:</span>
                <span className="font-medium">{usageStats.vendors.avgSessionTime}</span>
              </div>
            </div>
          )}
          <button
            onClick={() => handleViewDetails('vendors')}
            className="mt-4 w-full btn-secondary flex items-center justify-center"
          >
            <EyeIcon className="h-4 w-4 mr-2" />
            View Details
          </button>
        </div>

        {/* Sub-admins Usage */}
        <div className="card">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-900">Sub-admin Usage</h3>
            <DocumentTextIcon className="h-8 w-8 text-purple-500" />
          </div>
          {loading ? (
            <div className="space-y-3">
              <div className="animate-pulse">
                <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                <div className="h-4 bg-gray-200 rounded w-1/2 mb-2"></div>
                <div className="h-4 bg-gray-200 rounded w-2/3 mb-2"></div>
                <div className="h-4 bg-gray-200 rounded w-1/2"></div>
              </div>
            </div>
          ) : (
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">Total Users:</span>
                <span className="font-medium">{usageStats.subAdmins.totalUsers}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">Active Users:</span>
                <span className="font-medium text-green-600">{usageStats.subAdmins.activeUsers}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">Sessions Today:</span>
                <span className="font-medium">{usageStats.subAdmins.sessionsToday}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">Avg Session Time:</span>
                <span className="font-medium">{usageStats.subAdmins.avgSessionTime}</span>
              </div>
            </div>
          )}
          <button
            onClick={() => handleViewDetails('sub-admins')}
            className="mt-4 w-full btn-secondary flex items-center justify-center"
          >
            <EyeIcon className="h-4 w-4 mr-2" />
            View Details
          </button>
        </div>
      </div>

      {/* Most Used Features */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="card">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Most Used Features</h3>
          {loading ? (
            <div className="space-y-4">
              {[1, 2, 3, 4, 5].map((i) => (
                <div key={i} className="animate-pulse">
                  <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                  <div className="h-2 bg-gray-200 rounded w-full"></div>
                </div>
              ))}
            </div>
          ) : mostUsedFeatures.length > 0 ? (
            <div className="space-y-4">
              {mostUsedFeatures.map((feature, index) => (
                <div key={index} className="flex items-center justify-between">
                  <div className="flex-1">
                    <div className="flex justify-between items-center mb-1">
                      <span className="text-sm font-medium text-gray-900">{feature.feature}</span>
                      <span className="text-sm text-gray-500">{feature.usage.toLocaleString()} uses</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div
                        className="bg-primary-600 h-2 rounded-full"
                        style={{ width: `${feature.percentage}%` }}
                      ></div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-8">
              <ChartBarIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-500">No feature usage data available</p>
              <p className="text-sm text-gray-400">Data will appear as users interact with the system</p>
            </div>
          )}
        </div>

        {/* Recent Activity */}
        <div className="card">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Recent Activity</h3>
          {loading ? (
            <div className="space-y-3 max-h-80 overflow-y-auto">
              {[1, 2, 3, 4, 5].map((i) => (
                <div key={i} className="animate-pulse p-3 bg-gray-50 rounded-lg">
                  <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                  <div className="h-3 bg-gray-200 rounded w-1/2 mb-2"></div>
                  <div className="h-3 bg-gray-200 rounded w-1/4"></div>
                </div>
              ))}
            </div>
          ) : recentActivity.length > 0 ? (
            <div className="space-y-3 max-h-80 overflow-y-auto">
              {recentActivity.map((activity, index) => (
                <div key={index} className="flex items-start space-x-3 p-3 bg-gray-50 rounded-lg">
                  <div className="flex-1">
                    <p className="text-sm font-medium text-gray-900">{activity.user}</p>
                    <p className="text-sm text-gray-600">{activity.action}</p>
                    <div className="flex items-center mt-1 text-xs text-gray-500">
                      <span>{activity.timestamp}</span>
                      <span className="mx-2">•</span>
                      <span>{activity.ip}</span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-8">
              <DocumentTextIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-500">No recent activity</p>
              <p className="text-sm text-gray-400">Activity will appear as users interact with the system</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default Audit;
