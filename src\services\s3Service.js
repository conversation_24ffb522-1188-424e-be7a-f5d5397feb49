// Secure S3 Service - Uses backend API for uploads
// No AWS credentials exposed in frontend

import {
  S3_CONFIG,
  UPLOAD_CONFIG,
  API_ENDPOINTS,
  validateFile as configValidateFile,
  generateFile<PERSON>ey as configGenerateFileKey,
  getPublicFileUrl as configGetPublicFileUrl,
  getDebugInfo
} from '../config/uploadConfig.js';

// Re-export functions from config
export const generateFileKey = configGenerateFileKey;
export const getPublicFileUrl = configGetPublicFileUrl;
export const validateFile = configValidateFile;

/**
 * Get the upload API endpoint
 */
const getUploadEndpoint = () => {
  return API_ENDPOINTS.upload;
};



/**
 * Upload file to S3 via secure backend API with fallback
 * @param {File} file - File to upload
 * @param {string} module - Module name
 * @param {string} recordId - Record ID
 * @param {string} fileType - File type
 * @param {string} allowedTypes - Allowed file types
 * @param {function} onProgress - Progress callback
 * @returns {Promise<object>} Upload result
 */
export const uploadFileToS3 = async (file, module, recordId, fileType, allowedTypes = 'all', onProgress = null) => {
  try {
    console.log('🚀 Starting file upload:', {
      fileName: file.name,
      fileSize: file.size,
      fileType: file.type,
      module,
      recordId,
      fileType: fileType,
      allowedTypes,
      debugInfo: getDebugInfo()
    });

    // Validate file on frontend
    const validation = validateFile(file, allowedTypes);
    if (!validation.isValid) {
      throw new Error(validation.errors.join(', '));
    }

    // Upload progress tracking
    if (onProgress) {
      onProgress(0);
    }

    // Try backend API first
    try {
      console.log('📤 Attempting upload via backend API...');

      // Prepare form data
      const formData = new FormData();
      formData.append('file', file);

      const uploadUrl = `${getUploadEndpoint()}?module=${module}&recordId=${recordId}&fileType=${fileType}&allowedTypes=${allowedTypes}`;
      console.log('🔗 Upload URL:', uploadUrl);

      const response = await fetch(uploadUrl, {
        method: 'POST',
        body: formData,
        // Don't set Content-Type header - let browser set it with boundary for FormData
      });

      console.log('📊 Upload response status:', response.status);

      if (response.ok) {
        const result = await response.json();
        console.log('✅ Backend upload successful:', result);

        if (onProgress) {
          onProgress(100);
        }

        if (result.success) {
          const uploadResult = {
            success: true,
            key: result.file.key,
            url: result.file.url,
            size: result.file.size,
            type: result.file.type,
            name: result.file.name || result.file.originalname,
            originalName: result.file.name || result.file.originalname
          };

          console.log('🎉 Upload completed via backend:', uploadResult);
          return uploadResult;
        } else {
          throw new Error(result.error || 'Upload failed - no success flag');
        }
      } else {
        const errorText = await response.text();
        console.error('❌ Upload response error:', errorText);
        throw new Error(`HTTP ${response.status}: ${errorText}`);
      }

    } catch (backendError) {
      console.warn('⚠️ Backend upload failed:', backendError.message);
      console.error('Full error:', backendError);

      // Re-throw the error - no fallback for production
      throw backendError;
    }

  } catch (error) {
    console.error('❌ Upload failed completely:', error);

    if (onProgress) {
      onProgress(0);
    }

    throw new Error(`Upload failed: ${error.message}`);
  }
};

/**
 * Delete file from S3 via backend API
 * @param {string} key - S3 key to delete
 * @returns {Promise<boolean>} Success status
 */
export const deleteFileFromS3 = async (key) => {
  try {
    console.log('🗑️ Deleting file via backend API:', key);

    const response = await fetch(`${getUploadEndpoint()}?key=${encodeURIComponent(key)}`, {
      method: 'DELETE',
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({ error: 'Delete failed' }));
      throw new Error(errorData.error || `HTTP ${response.status}: ${response.statusText}`);
    }

    const result = await response.json();

    if (!result.success) {
      throw new Error(result.error || 'Delete failed');
    }

    console.log('✅ File deleted successfully:', key);
    return true;

  } catch (error) {
    console.error('❌ Delete failed:', error);
    throw new Error(`Delete failed: ${error.message}`);
  }
};

/**
 * Get signed URL for secure file access (via backend API)
 * @param {string} key - S3 key
 * @param {number} expiresIn - URL expiration in seconds (default: 1 hour)
 * @returns {Promise<string>} Signed URL
 */
export const getSignedFileUrl = async (key, expiresIn = 3600) => {
  try {
    console.log('🔗 Generating signed URL for key:', key);

    // Try to get signed URL from backend API
    try {
      const response = await fetch(`${API_ENDPOINTS.signedUrl}?key=${encodeURIComponent(key)}&expiresIn=${expiresIn}`);

      if (response.ok) {
        const data = await response.json();
        if (data.success && data.signedUrl) {
          console.log('✅ Got signed URL from backend');
          return data.signedUrl;
        }
      }
    } catch (apiError) {
      console.warn('⚠️ Backend signed URL failed, falling back to public URL:', apiError);
    }

    // Fallback to public URL
    console.log('🔄 Using public URL as fallback');
    return getPublicFileUrl(key);
  } catch (error) {
    console.error('❌ Failed to get file URL:', error);
    throw new Error(`Failed to get file URL: ${error.message}`);
  }
};

// getPublicFileUrl is already exported from config above

/**
 * Upload multiple files
 * @param {FileList} files - Files to upload
 * @param {string} module - Module name
 * @param {string} recordId - Record ID
 * @param {string} fileType - File type
 * @param {function} onProgress - Progress callback
 * @returns {Promise<Array>} Upload results
 */
export const uploadMultipleFiles = async (files, module, recordId, fileType, onProgress = null) => {
  const results = [];
  const totalFiles = files.length;
  
  for (let i = 0; i < totalFiles; i++) {
    const file = files[i];
    
    try {
      const fileProgress = (progress) => {
        if (onProgress) {
          const overallProgress = ((i * 100) + progress) / totalFiles;
          onProgress(Math.round(overallProgress));
        }
      };
      
      const result = await uploadFileToS3(file, module, recordId, fileType, fileProgress);
      results.push({ success: true, file: result });
      
    } catch (error) {
      console.error(`❌ Failed to upload ${file.name}:`, error);
      results.push({ success: false, error: error.message, fileName: file.name });
    }
  }
  
  return results;
};

export default {
  uploadFileToS3,
  deleteFileFromS3,
  getSignedFileUrl,
  getPublicFileUrl,
  uploadMultipleFiles,
  validateFile,
  generateFileKey
};
