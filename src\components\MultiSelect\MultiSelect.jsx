import React, { useState, useRef, useEffect } from 'react';
import { ChevronDownIcon, XMarkIcon } from '@heroicons/react/24/outline';

const MultiSelect = ({
  options = [],
  value = [],
  onChange,
  placeholder = "Select options...",
  className = "",
  disabled = false,
  required = false,
  maxHeight = "200px",
  searchable = true,
  showSelectAll = true,
  label = "",
  error = ""
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const dropdownRef = useRef(null);
  const inputRef = useRef(null);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setIsOpen(false);
        setSearchTerm('');
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  // Filter options based on search term
  const filteredOptions = searchable && searchTerm
    ? options.filter(option =>
        option.name.toLowerCase().includes(searchTerm.toLowerCase())
      )
    : options;

  // Handle option selection
  const handleOptionToggle = (option) => {
    const isSelected = value.some(item => item.id === option.id);
    let newValue;

    if (isSelected) {
      newValue = value.filter(item => item.id !== option.id);
    } else {
      newValue = [...value, option];
    }

    onChange(newValue);
  };

  // Handle select all
  const handleSelectAll = () => {
    if (value.length === filteredOptions.length) {
      onChange([]);
    } else {
      onChange(filteredOptions);
    }
  };

  // Remove selected item
  const removeItem = (itemToRemove) => {
    const newValue = value.filter(item => item.id !== itemToRemove.id);
    onChange(newValue);
  };

  // Get display text
  const getDisplayText = () => {
    if (value.length === 0) return placeholder;
    if (value.length === 1) return value[0].name;
    return `${value.length} items selected`;
  };

  return (
    <div className={`relative ${className}`}>
      {/* Label */}
      {label && (
        <label className="block text-sm font-medium text-gray-700 mb-1">
          {label} {required && <span className="text-red-500">*</span>}
        </label>
      )}

      {/* Main Input */}
      <div
        ref={dropdownRef}
        className={`relative border rounded-md bg-white ${
          disabled ? 'bg-gray-100 cursor-not-allowed' : 'cursor-pointer'
        } ${error ? 'border-red-300' : 'border-gray-300'} ${
          isOpen ? 'ring-2 ring-blue-500 border-blue-500' : ''
        }`}
      >
        <div
          className="flex items-center justify-between px-3 py-2 min-h-[38px]"
          onClick={() => !disabled && setIsOpen(!isOpen)}
        >
          <div className="flex-1 flex flex-wrap gap-1">
            {value.length === 0 ? (
              <span className="text-gray-500">{placeholder}</span>
            ) : (
              <>
                {value.slice(0, 2).map((item) => (
                  <span
                    key={item.id}
                    className="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-blue-100 text-blue-800"
                  >
                    {item.name}
                    {!disabled && (
                      <button
                        type="button"
                        className="ml-1 inline-flex items-center justify-center w-4 h-4 rounded-full hover:bg-blue-200"
                        onClick={(e) => {
                          e.stopPropagation();
                          removeItem(item);
                        }}
                      >
                        <XMarkIcon className="w-3 h-3" />
                      </button>
                    )}
                  </span>
                ))}
                {value.length > 2 && (
                  <span className="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-gray-100 text-gray-600">
                    +{value.length - 2} more
                  </span>
                )}
              </>
            )}
          </div>
          <ChevronDownIcon
            className={`w-5 h-5 text-gray-400 transition-transform ${
              isOpen ? 'transform rotate-180' : ''
            }`}
          />
        </div>

        {/* Dropdown */}
        {isOpen && !disabled && (
          <div
            className="absolute z-50 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg"
            style={{ maxHeight: maxHeight }}
          >
            {/* Search Input */}
            {searchable && (
              <div className="p-2 border-b border-gray-200">
                <input
                  ref={inputRef}
                  type="text"
                  className="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Search options..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  onClick={(e) => e.stopPropagation()}
                />
              </div>
            )}

            {/* Options Container */}
            <div className="max-h-48 overflow-y-auto">
              {/* Select All Option */}
              {showSelectAll && filteredOptions.length > 1 && (
                <div
                  className="flex items-center px-3 py-2 hover:bg-gray-50 cursor-pointer border-b border-gray-100"
                  onClick={handleSelectAll}
                >
                  <input
                    type="checkbox"
                    className="mr-3 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    checked={value.length === filteredOptions.length && filteredOptions.length > 0}
                    onChange={() => {}} // Handled by onClick
                  />
                  <span className="text-sm font-medium text-gray-700">
                    {value.length === filteredOptions.length ? 'Deselect All' : 'Select All'}
                  </span>
                </div>
              )}

              {/* Options */}
              {filteredOptions.length === 0 ? (
                <div className="px-3 py-2 text-sm text-gray-500">
                  {searchTerm ? 'No options found' : 'No options available'}
                </div>
              ) : (
                filteredOptions.map((option) => {
                  const isSelected = value.some(item => item.id === option.id);
                  return (
                    <div
                      key={option.id}
                      className={`flex items-center px-3 py-2 hover:bg-gray-50 cursor-pointer ${
                        isSelected ? 'bg-blue-50' : ''
                      }`}
                      onClick={() => handleOptionToggle(option)}
                    >
                      <input
                        type="checkbox"
                        className="mr-3 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                        checked={isSelected}
                        onChange={() => {}} // Handled by onClick
                      />
                      <div className="flex-1">
                        <div className="text-sm font-medium text-gray-900">
                          {option.name}
                        </div>
                        {option.description && (
                          <div className="text-xs text-gray-500">
                            {option.description}
                          </div>
                        )}
                      </div>
                    </div>
                  );
                })
              )}
            </div>
          </div>
        )}
      </div>

      {/* Error Message */}
      {error && (
        <p className="mt-1 text-sm text-red-600">{error}</p>
      )}

      {/* Selected Count */}
      {value.length > 0 && (
        <p className="mt-1 text-xs text-gray-500">
          {value.length} item{value.length !== 1 ? 's' : ''} selected
        </p>
      )}
    </div>
  );
};

export default MultiSelect;
