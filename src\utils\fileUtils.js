/**
 * File utilities for handling file viewing and downloading
 */

/**
 * Test if a URL is accessible
 * @param {string} url - URL to test
 * @returns {Promise<boolean>} Whether the URL is accessible
 */
export const testUrlAccessibility = async (url) => {
  try {
    const response = await fetch(url, { 
      method: 'HEAD',
      mode: 'no-cors' // Avoid CORS issues for testing
    });
    return true; // If no error, assume accessible
  } catch (error) {
    console.warn('URL accessibility test failed:', error.message);
    return false;
  }
};

/**
 * Open file in new tab with fallback options
 * @param {string} url - File URL
 * @param {string} filename - File name for download fallback
 * @returns {boolean} Whether the file was opened successfully
 */
export const openFileInNewTab = (url, filename = 'document') => {
  try {
    // Try to open in new tab
    const newWindow = window.open(url, '_blank');
    
    if (newWindow) {
      console.log('✅ File opened in new tab successfully');
      return true;
    } else {
      console.warn('⚠️ Popup blocked, trying alternative methods');
      
      // Fallback 1: Create a temporary link and click it
      const link = document.createElement('a');
      link.href = url;
      link.target = '_blank';
      link.rel = 'noopener noreferrer';
      link.download = filename;
      
      // Add to DOM temporarily
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      
      console.log('✅ File opened via temporary link');
      return true;
    }
  } catch (error) {
    console.error('❌ Failed to open file:', error);
    return false;
  }
};

/**
 * Download file with proper filename
 * @param {string} url - File URL
 * @param {string} filename - Desired filename
 */
export const downloadFile = async (url, filename) => {
  try {
    console.log('📥 Downloading file:', filename);
    
    // Create download link
    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    link.style.display = 'none';
    
    // Add to DOM and trigger download
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    
    console.log('✅ Download initiated successfully');
  } catch (error) {
    console.error('❌ Download failed:', error);
    // Fallback: open in new tab
    window.open(url, '_blank');
  }
};

/**
 * Get file extension from URL or filename
 * @param {string} url - File URL or filename
 * @returns {string} File extension
 */
export const getFileExtension = (url) => {
  try {
    const pathname = new URL(url).pathname;
    return pathname.split('.').pop().toLowerCase();
  } catch (error) {
    // If URL parsing fails, try simple string split
    return url.split('.').pop().toLowerCase();
  }
};

/**
 * Check if file is an image
 * @param {string} url - File URL
 * @returns {boolean} Whether the file is an image
 */
export const isImageFile = (url) => {
  const imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'webp', 'svg', 'bmp'];
  const extension = getFileExtension(url);
  return imageExtensions.includes(extension);
};

/**
 * Check if file is a PDF
 * @param {string} url - File URL
 * @returns {boolean} Whether the file is a PDF
 */
export const isPdfFile = (url) => {
  const extension = getFileExtension(url);
  return extension === 'pdf';
};

/**
 * Get appropriate file icon based on file type
 * @param {string} url - File URL
 * @returns {string} Icon class or emoji
 */
export const getFileIcon = (url) => {
  const extension = getFileExtension(url);
  
  switch (extension) {
    case 'pdf':
      return '📄';
    case 'doc':
    case 'docx':
      return '📝';
    case 'xls':
    case 'xlsx':
      return '📊';
    case 'jpg':
    case 'jpeg':
    case 'png':
    case 'gif':
    case 'webp':
      return '🖼️';
    case 'zip':
    case 'rar':
      return '📦';
    default:
      return '📎';
  }
};

/**
 * Enhanced file opening with multiple fallback strategies
 * @param {string} url - File URL
 * @param {string} filename - File name
 * @param {Object} options - Additional options
 * @returns {Promise<boolean>} Whether the file was opened successfully
 */
export const openFileWithFallbacks = async (url, filename, options = {}) => {
  console.log('🔄 Opening file with fallbacks:', { url, filename, options });
  
  if (!url) {
    console.error('❌ No URL provided');
    return false;
  }
  
  try {
    // Strategy 1: Direct new tab opening
    if (openFileInNewTab(url, filename)) {
      return true;
    }
    
    // Strategy 2: For images, try creating an image element
    if (isImageFile(url)) {
      const img = new Image();
      img.onload = () => {
        const newWindow = window.open('', '_blank');
        if (newWindow) {
          newWindow.document.write(`
            <html>
              <head><title>${filename}</title></head>
              <body style="margin:0;display:flex;justify-content:center;align-items:center;min-height:100vh;background:#f0f0f0;">
                <img src="${url}" style="max-width:100%;max-height:100%;object-fit:contain;" alt="${filename}">
              </body>
            </html>
          `);
          newWindow.document.close();
        }
      };
      img.onerror = () => {
        console.warn('⚠️ Image failed to load, trying download');
        downloadFile(url, filename);
      };
      img.src = url;
      return true;
    }
    
    // Strategy 3: For PDFs, try embedding
    if (isPdfFile(url)) {
      const newWindow = window.open('', '_blank');
      if (newWindow) {
        newWindow.document.write(`
          <html>
            <head><title>${filename}</title></head>
            <body style="margin:0;">
              <embed src="${url}" type="application/pdf" width="100%" height="100%" />
              <p style="text-align:center;margin-top:20px;">
                <a href="${url}" download="${filename}">Download ${filename}</a>
              </p>
            </body>
          </html>
        `);
        newWindow.document.close();
        return true;
      }
    }
    
    // Strategy 4: Force download
    downloadFile(url, filename);
    return true;
    
  } catch (error) {
    console.error('❌ All file opening strategies failed:', error);
    return false;
  }
};
